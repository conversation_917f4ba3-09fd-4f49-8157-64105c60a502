/**
 * 券卡片组件 JS
 * 用于展示信任券信息，支持多种状态和交互
 */

Component({
  /**
   * 组件属性列表
   */
  properties: {
    // 券标题
    title: {
      type: String,
      value: '',
      observer: 'onTitleChange'
    },
    
    // 券描述
    description: {
      type: String,
      value: ''
    },
    
    // 券编号 - 新增字段
    couponNumber: {
      type: String,
      value: '',
      observer: 'onCouponNumberChange'
    },
    
    // 券类型 - 新增字段 (promise, task, reward, apology, special)
    couponType: {
      type: String,
      value: 'promise',
      observer: 'onCouponTypeChange'
    },
    
    // 券状态 (received, pending_redeem, redeemed, expired, sent)
    status: {
      type: String,
      value: 'received',
      observer: 'onStatusChange'
    },
    
    // 发送方
    sender: {
      type: String,
      value: ''
    },
    
    // 接收方
    receiver: {
      type: String,
      value: ''
    },
    
    // 有效期日期
    expiryDate: {
      type: String,
      value: ''
    },
    
    // 创建时间
    createTime: {
      type: String,
      value: ''
    },
    
    // 券ID
    couponId: {
      type: String,
      value: ''
    },
    
    // 自定义样式类名
    customClass: {
      type: String,
      value: ''
    },
    
    // 是否显示状态标识
    showStatus: {
      type: Boolean,
      value: true
    },
    
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: false
    },
    
    // 是否显示装饰
    showDecoration: {
      type: Boolean,
      value: true
    },
    
    // 是否可点击
    clickable: {
      type: Boolean,
      value: true
    },
    
    // 特殊效果 (glow, pulse)
    effect: {
      type: String,
      value: ''
    },
    
    // 券风格 (gradient, glass, minimal, 3d)
    style: {
      type: String,
      value: 'gradient'
    }
  },

  /**
   * 组件数据
   */
  data: {
    // 状态文本映射
    statusTextMap: {
      received: '待兑现',
      pending_redeem: '申请兑现中',
      redeemed: '已兑现',
      expired: '已过期',
      sent: '已发送'
    },
    
    // 状态图标映射
    statusIconMap: {
      received: '✓',
      pending_redeem: '⏳',
      redeemed: '✅',
      expired: '⚠',
      sent: '📤'
    },
    
    // 券类型文本映射
    couponTypeTextMap: {
      promise: '承诺券',
      task: '任务券',
      reward: '奖励券',
      apology: '道歉券',
      special: '特别券'
    },
    
    // 券类型图标映射
    typeIconMap: {
      promise: '💕',
      task: '📝',
      reward: '🎁',
      apology: '🙏',
      special: '✨'
    },
    
    // 装饰孔数组 (左右各5个)
    decorationHoles: Array.from({ length: 5 }, (_, i) => i),
    
    // 计算属性
    statusClass: '',
    typeClass: '',
    statusText: '',
    statusIcon: '',
    typeIcon: '',
    couponTypeText: '',
    formattedExpiryDate: '',
    formattedCreateTime: ''
  },

  /**
   * 组件方法列表
   */
  methods: {
    /**
     * 初始化组件
     */
    initializeComponent() {
      this.updateStatusDisplay();
      this.updateTypeDisplay();
      this.formatDateDisplay();
      this.checkExpiry();
      this.generateCouponNumberIfNeeded();
    },

    /**
     * 标题变化监听
     */
    onTitleChange(newVal, oldVal) {
      if (newVal !== oldVal && newVal) {
        console.log(`[CouponCard] Title changed: ${newVal}`);
      }
    },

    /**
     * 券编号变化监听
     */
    onCouponNumberChange(newVal, oldVal) {
      if (newVal !== oldVal) {
        console.log(`[CouponCard] Coupon number changed: ${oldVal} -> ${newVal}`);
      }
    },

    /**
     * 券类型变化监听
     */
    onCouponTypeChange(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.updateTypeDisplay();
        console.log(`[CouponCard] Coupon type changed: ${oldVal} -> ${newVal}`);
      }
    },

    /**
     * 状态变化监听
     */
    onStatusChange(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.updateStatusDisplay();
        console.log(`[CouponCard] Status changed: ${oldVal} -> ${newVal}`);
      }
    },

    /**
     * 卡片点击事件
     */
    onCardTap(e) {
      if (!this.properties.clickable) return;
      
      // 安全检查事件对象
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      
      // 获取点击位置信息
      const detail = e ? e.detail : {};
      const { offsetX, offsetY } = detail;
      
      // 触发点击事件
      this.triggerEvent('tap', {
        couponId: this.properties.couponId,
        couponNumber: this.properties.couponNumber,
        title: this.properties.title,
        status: this.properties.status,
        couponType: this.properties.couponType,
        position: { x: offsetX || 0, y: offsetY || 0 },
        timeStamp: Date.now()
      });
      
      // 添加点击反馈
      this.addClickFeedback();
    },

    /**
     * 添加点击反馈效果
     */
    addClickFeedback() {
      // 短暂缩放效果
      const currentClass = this.properties.customClass;
      this.setData({
        customClass: `${currentClass} card-clicked`
      });
      
      setTimeout(() => {
        this.setData({
          customClass: currentClass
        });
      }, 150);
    },

    /**
     * 更新状态显示
     */
    updateStatusDisplay() {
      const status = this.properties.status;
      const statusText = this.data.statusTextMap[status] || '未知状态';
      const statusIcon = this.data.statusIconMap[status] || '?';
      
      this.setData({
        statusClass: `status-${status}`,
        statusText,
        statusIcon
      });
    },

    /**
     * 更新类型显示
     */
    updateTypeDisplay() {
      const couponType = this.properties.couponType;
      const couponTypeText = this.data.couponTypeTextMap[couponType] || '未知类型';
      const typeIcon = this.data.typeIconMap[couponType] || '💕';
      
      this.setData({
        typeClass: `type-${couponType}`,
        couponTypeText,
        typeIcon
      });
    },

    /**
     * 格式化日期显示
     */
    formatDateDisplay() {
      const { expiryDate, createTime } = this.properties;
      
      let formattedExpiryDate = '';
      let formattedCreateTime = '';
      
      if (expiryDate) {
        try {
          const date = new Date(expiryDate);
          formattedExpiryDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        } catch (error) {
          console.error('[CouponCard] Format expiry date error:', error);
          formattedExpiryDate = expiryDate;
        }
      }
      
      if (createTime) {
        try {
          const date = new Date(createTime);
          formattedCreateTime = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        } catch (error) {
          console.error('[CouponCard] Format create time error:', error);
          formattedCreateTime = createTime;
        }
      }
      
      this.setData({
        formattedExpiryDate,
        formattedCreateTime
      });
    },

    /**
     * 检查是否过期
     */
    checkExpiry() {
      const { expiryDate } = this.properties;
      
      if (expiryDate) {
        const now = new Date();
        const expiry = new Date(expiryDate);
        
        if (expiry <= now && this.properties.status !== 'expired') {
          // 触发过期事件
          this.triggerEvent('expired', {
            couponId: this.properties.couponId,
            title: this.properties.title,
            expiryDate
          });
        }
      }
    },

    /**
     * 生成券编号（如果需要）
     */
    generateCouponNumberIfNeeded() {
      if (!this.properties.couponNumber) {
        const generatedNumber = this.generateCouponNumber();
        this.setData({
          couponNumber: generatedNumber
        });
      }
    },

    /**
     * 生成券编号
     * 规则：No.YY+4位数字 (如: No.25001)
     */
    generateCouponNumber() {
      const currentYear = new Date().getFullYear();
      const yearSuffix = String(currentYear).slice(-2); // 取年份后两位
      
      // 生成4位随机数字
      const randomNum = Math.floor(Math.random() * 9000) + 1000; // 1000-9999
      
      return `${yearSuffix}${randomNum}`;
    },

    /**
     * 获取券的完整信息
     */
    getCouponInfo() {
      return {
        couponId: this.properties.couponId,
        couponNumber: this.properties.couponNumber,
        title: this.properties.title,
        description: this.properties.description,
        couponType: this.properties.couponType,
        status: this.properties.status,
        sender: this.properties.sender,
        receiver: this.properties.receiver,
        expiryDate: this.properties.expiryDate,
        createTime: this.properties.createTime,
        formattedExpiryDate: this.data.formattedExpiryDate,
        formattedCreateTime: this.data.formattedCreateTime,
        statusText: this.data.statusText,
        couponTypeText: this.data.couponTypeText,
        typeIcon: this.data.typeIcon
      };
    },

    /**
     * 设置特殊效果
     */
    setEffect(effect) {
      if (!effect) return;
      
      const currentClass = this.properties.customClass;
      const newClass = `${currentClass} effect-${effect}`.trim();
      
      this.setData({
        customClass: newClass
      });
    },

    /**
     * 移除特殊效果
     */
    removeEffect() {
      const currentClass = this.properties.customClass;
      const newClass = currentClass.replace(/effect-\w+/g, '').trim();
      
      this.setData({
        customClass: newClass
      });
    },

    /**
     * 设置券风格
     */
    setStyle(style) {
      if (!style) return;
      
      const currentClass = this.properties.customClass;
      // 移除现有风格类
      const cleanClass = currentClass.replace(/style-\w+/g, '').trim();
      const newClass = `${cleanClass} style-${style}`.trim();
      
      this.setData({
        customClass: newClass
      });
    },

    /**
     * 验证券数据
     */
    validateCouponData() {
      const { title, couponType, sender } = this.properties;
      
      const errors = [];
      
      if (!title.trim()) {
        errors.push('券标题不能为空');
      }
      
      if (!couponType) {
        errors.push('券类型不能为空');
      }
      
      if (!sender.trim()) {
        errors.push('发券人不能为空');
      }
      
      return {
        isValid: errors.length === 0,
        errors
      };
    },

    /**
     * 清理资源
     */
    cleanup() {
      // 清理定时器、事件监听等
      this.removeEffect();
    }
  },

  /**
   * 生命周期函数
   */
  lifetimes: {
    /**
     * 组件实例被创建
     */
    created() {
      console.log('[CouponCard] Component created');
    },

    /**
     * 组件实例进入页面节点树
     */
    attached() {
      console.log('[CouponCard] Component attached');
      
      // 初始化显示
      this.initializeComponent();
    },

    /**
     * 组件实例被从页面节点树移除
     */
    detached() {
      console.log('[CouponCard] Component detached');
      this.cleanup();
    }
  },

  /**
   * 组件数据字段监听器
   */
  observers: {
    /**
     * 监听日期变化
     */
    'expiryDate, createTime': function(expiryDate, createTime) {
      this.formatDateDisplay();
    },

    /**
     * 监听类型变化
     */
    'couponType': function(couponType) {
      this.updateTypeDisplay();
    },

    /**
     * 监听效果变化
     */
    'effect': function(effect) {
      if (effect) {
        console.log(`[CouponCard] Effect changed to: ${effect}`);
      }
    }
  },

  /**
   * 组件选项
   */
  options: {
    // 启用多插槽支持
    multipleSlots: true,
    // 使用纯数据字段优化性能
    pureDataPattern: /^_/,
    // 样式隔离
    styleIsolation: 'isolated'
  },

  /**
   * 外部样式类
   */
  externalClasses: [
    'custom-class',
    'card-class',
    'content-class',
    'title-class',
    'meta-class'
  ]
});
