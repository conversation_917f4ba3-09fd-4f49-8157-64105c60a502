{"component": true, "componentGenerics": {}, "usingComponents": {}, "componentPlaceholder": {}, "styleIsolation": "isolated", "styleScope": "isolated", "description": "自定义模态框组件，支持标题、内容展示、确认/取消按钮，可自定义按钮文本和回调函数", "version": "1.0.0", "author": "情侣信任券小程序团队", "category": "UI Components", "keywords": ["模态框", "对话框", "弹窗", "确认框"], "repository": {"type": "git", "url": "https://github.com/coupleapp/miniprogram-components"}, "license": "MIT", "properties": {"visible": {"type": "Boolean", "description": "是否显示模态框", "default": false, "required": false}, "title": {"type": "String", "description": "模态框标题", "default": "提示", "required": false}, "content": {"type": "String", "description": "模态框内容文本", "default": "", "required": false}, "confirmText": {"type": "String", "description": "确认按钮文本", "default": "确定", "required": false}, "cancelText": {"type": "String", "description": "取消按钮文本", "default": "取消", "required": false}, "showCancel": {"type": "Boolean", "description": "是否显示取消按钮", "default": true, "required": false}, "showCloseIcon": {"type": "Boolean", "description": "是否显示关闭图标", "default": false, "required": false}, "showActions": {"type": "Boolean", "description": "是否显示底部按钮区域", "default": true, "required": false}, "maskClosable": {"type": "Boolean", "description": "点击遮罩层是否关闭", "default": true, "required": false}, "confirmLoading": {"type": "Boolean", "description": "确认按钮是否加载状态", "default": false, "required": false}}, "events": {"confirm": {"description": "点击确认按钮时触发", "parameters": {"type": "String", "timeStamp": "Number"}}, "cancel": {"description": "点击取消按钮时触发", "parameters": {"type": "String", "timeStamp": "Number"}}, "maskClick": {"description": "点击遮罩层时触发", "parameters": {"type": "String", "timeStamp": "Number"}}, "show": {"description": "模态框显示时触发", "parameters": {"type": "String", "timeStamp": "Number"}}, "hide": {"description": "模态框隐藏时触发", "parameters": {"type": "String", "timeStamp": "Number"}}}, "methods": {"show": {"description": "显示模态框", "parameters": {}, "returns": "void"}, "hide": {"description": "隐藏模态框", "parameters": {}, "returns": "void"}, "toggle": {"description": "切换模态框显示状态", "parameters": {}, "returns": "void"}, "setConfirmLoading": {"description": "设置确认按钮加载状态", "parameters": {"loading": "Boolean"}, "returns": "void"}}, "slots": {"content": {"description": "自定义内容插槽，可替代 content 属性"}}, "examples": [{"title": "基础使用", "description": "显示基本的确认对话框", "code": {"wxml": "<custom-modal visible=\"{{showModal}}\" title=\"确认删除\" content=\"确定要删除这张信任券吗？\" bind:confirm=\"handleConfirm\" bind:cancel=\"handleCancel\" />", "js": "Page({ data: { showModal: false }, handleConfirm() { console.log('用户确认'); }, handleCancel() { console.log('用户取消'); } })"}}, {"title": "自定义内容", "description": "使用插槽自定义模态框内容", "code": {"wxml": "<custom-modal visible=\"{{showModal}}\" title=\"券详情\"><view slot=\"content\"><image src=\"coupon-image.jpg\" /><text>详细信息...</text></view></custom-modal>"}}, {"title": "仅显示确认按钮", "description": "隐藏取消按钮，只显示确认按钮", "code": {"wxml": "<custom-modal visible=\"{{showModal}}\" title=\"操作成功\" content=\"券已核销成功！\" show-cancel=\"{{false}}\" confirm-text=\"我知道了\" bind:confirm=\"handleOk\" />"}}], "changelog": [{"version": "1.0.0", "date": "2024-01-01", "changes": ["初始版本发布", "支持基础模态框功能", "支持自定义按钮文本", "支持插槽内容", "支持动画效果"]}]}