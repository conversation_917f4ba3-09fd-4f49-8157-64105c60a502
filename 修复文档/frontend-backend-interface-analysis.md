# 前端与云函数接口对接分析报告

## 📋 接口调用情况统计

### 核心页面云函数调用汇总

#### 1. index/index.js (首页)
- **调用云函数**: `getCoupons`
- **发送参数**: 
  ```javascript
  {
    filterType: "received",
    status: ["received", "pending_redeem"],
    limit: 10
  }
  ```
- **期望返回**: 券列表数据
- **潜在问题**: 前端期望特定状态值，与云函数状态系统需要对齐

#### 2. createCoupon/createCoupon.js (创建券)
- **调用云函数**: `createOrUpdateCoupon`
- **发送参数**:
  ```javascript
  {
    name: "券名称",              // ✅ 与云函数一致
    description: "券描述",       // ✅ 与云函数一致
    icon: "图标",               // ✅ 与云函数一致
    bgColor: "背景色",          // ✅ 与云函数一致
    expiryDate: "过期时间",     // ✅ 与云函数一致
    creatorId: "创建者ID",      // ❌ 云函数会自动设置
    recipientId: "接收者ID",    // ❌ 云函数会自动设置
    status: "sent"              // ✅ 与云函数一致
  }
  ```
- **问题**: 前端不应发送creatorId/recipientId，云函数会自动处理

#### 3. myCoupons/myCoupons.js (我的券)
- **调用云函数**: `getCoupons`
- **期望数据字段**: 
  ```javascript
  {
    expiresAt: "过期时间"    // ❌ 云函数返回的是 expiryDate
  }
  ```
- **问题**: 字段名不匹配导致过期时间显示异常

#### 4. couponDetail/couponDetail.js (券详情)
- **调用云函数**: 
  - `getCouponDetail` - 获取券详情
  - `updateCouponStatus` - 更新券状态
- **状态操作**: `receive`, `redeem`, `approve`, `cancel`
- **字段使用**: 
  ```javascript
  {
    expiryDate: "过期时间"   // ✅ 与云函数一致
  }
  ```

#### 5. notifications/notifications.js (通知)
- **调用云函数**: 
  - `getNotifications` - 获取通知列表
  - `markNotificationAsRead` - 标记已读
  - `markAllNotificationsAsRead` - 全部标记已读
  - `deleteNotification` - 删除通知
  - `clearAllNotifications` - 清空所有通知
- **数据处理**: 
  ```javascript
  {
    isRead: "已读状态"       // ✅ 与云函数一致
  }
  ```

## 🚨 发现的接口对接问题

### 问题1: 券字段名不一致
**位置**: myCoupons页面第136行
```javascript
// 前端期望
const expiryText = this.formatDate(coupon.expiresAt);

// 云函数返回
expiryDate: "过期时间"
```
**影响**: 过期时间无法正确显示

### 问题2: 券名称字段访问不一致
**位置**: getCoupons云函数返回数据映射
```javascript
// getCoupons云函数第137行
title: c.title  // 但数据中存储的是 c.name
```
**影响**: 券名称可能显示为空

### 问题3: 创建券时冗余参数
**位置**: createCoupon页面
```javascript
// 前端发送了不必要的参数
creatorId: this.data.userInfo._id,        // 云函数会自动设置
recipientId: this.data.coupleInfo.partnerId  // 云函数会自动设置
```
**影响**: 可能导致权限检查或数据不一致

### 问题4: 券状态值的不统一使用
**位置**: 多个页面
- 前端使用: `received`, `pending_redeem`, `redeemed`
- 某些地方期望: `active`, `expired`, `cancelled`
**影响**: 状态判断逻辑可能失效

## 🔧 修复建议

### 1. 统一字段名映射
在云函数返回数据时，添加字段兼容性处理：
```javascript
// getCoupons云函数中添加
return {
  ...coupon,
  title: coupon.name,           // 向后兼容
  expiresAt: coupon.expiryDate  // 字段名兼容
}
```

### 2. 前端参数清理
移除createCoupon中不必要的参数：
```javascript
const couponData = {
  name: this.data.name.trim(),
  description: this.data.description.trim(),
  icon: this.data.selectedIcon,
  bgColor: this.data.selectedColor,
  expiryDate: fullExpiresAt ? fullExpiresAt.toISOString() : null,
  status: 'sent'
  // 删除 creatorId, recipientId - 云函数自动处理
};
```

### 3. 前端字段名标准化
修改myCoupons页面：
```javascript
// 第136行修改为
const expiryText = this.formatDate(coupon.expiryDate);
```

### 4. 状态值标准化
在所有前端页面统一使用：
- `sent`: 已发送
- `received`: 已接收  
- `pending_redeem`: 申请兑现中
- `redeemed`: 已兑现
- `cancelled`: 已取消
- `expired`: 已过期

## 🧪 测试验证点

### 基础功能测试
1. **创建券流程**: 创建 → 发送 → 接收 → 显示正常
2. **券列表显示**: 券名称、过期时间正确显示
3. **券详情页**: 所有信息完整显示
4. **状态流转**: 各状态转换正常
5. **通知系统**: 通知正确发送和显示

### 字段兼容性测试
1. **expiryDate vs expiresAt**: 过期时间显示
2. **name vs title**: 券名称显示
3. **状态值**: 状态判断逻辑
4. **用户信息**: 创建者/接收者信息显示

## 📊 接口调用关系图

```
前端页面              云函数                数据集合
─────────            ─────────            ─────────
index              → getCoupons         → coupons
createCoupon       → createOrUpdateCoupon → coupons, users, couples
myCoupons          → getCoupons         → coupons, users  
couponDetail       → getCouponDetail    → coupons, users
                   → updateCouponStatus  → coupons, notifications
notifications      → getNotifications   → notifications
                   → markAsRead/Delete   → notifications
```

## 🎯 优先修复列表

1. **P0**: myCoupons页面过期时间字段 (expiresAt → expiryDate)
2. **P0**: getCoupons云函数券名称字段 (title → name)  
3. **P1**: createCoupon页面参数清理
4. **P1**: 状态值标准化
5. **P2**: 添加字段兼容性处理

