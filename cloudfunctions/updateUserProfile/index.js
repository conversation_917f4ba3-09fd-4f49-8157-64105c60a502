/**
 * 更新用户资料
 * 更新用户的昵称、头像等信息
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log("=== updateUserProfile 开始执行 ===");
  console.log("事件参数:", JSON.stringify(event, null, 2));

  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;

  console.log('更新用户资料请求 - openid:', openid);

  // 参数验证
  if (!openid) {
    console.error('错误：未获取到用户openid');
    return {
      success: false,
      message: '用户身份验证失败',
      code: 'INVALID_OPENID'
    };
  }

  try {
    // 首先查找用户是否存在 - 使用正确的字段名
    console.log('开始查询用户，openid:', openid);
    const userResult = await db.collection('users').where({
      _openid: openid  // 修复：使用_openid与其他云函数保持一致
    }).get();

    console.log('用户查询结果:', {
      count: userResult.data.length,
      hasData: userResult.data.length > 0
    });

    if (userResult.data.length === 0) {
      console.error('用户不存在，openid:', openid);
      return {
        success: false,
        message: '用户不存在，请先登录',
        code: 'USER_NOT_FOUND',
        debug: {
          openid: openid,
          queryField: '_openid'
        }
      };
    }

    const user = userResult.data[0];
    const userId = user._id;

    console.log('找到用户:', {
      userId: userId,
      currentNickName: user.nickName,
      currentSignature: user.signature,
      currentAvatarUrl: user.avatarUrl
    });

    // 准备更新的数据
    const updateData = {};
    const changedFields = [];

    // 只更新提供的字段，并记录变更
    if (event.nickName !== undefined) {
      updateData.nickName = String(event.nickName).trim();
      changedFields.push('nickName');
      console.log('更新昵称:', user.nickName, '->', updateData.nickName);
    }

    if (event.avatarUrl !== undefined) {
      updateData.avatarUrl = String(event.avatarUrl);
      changedFields.push('avatarUrl');
      console.log('更新头像:', user.avatarUrl, '->', updateData.avatarUrl);
    }

    if (event.signature !== undefined) {
      updateData.signature = String(event.signature).trim();
      changedFields.push('signature');
      console.log('更新签名:', user.signature, '->', updateData.signature);
    }

    if (event.gender !== undefined) {
      updateData.gender = event.gender;
      changedFields.push('gender');
    }

    if (event.city !== undefined) {
      updateData.city = event.city;
      changedFields.push('city');
    }

    if (event.province !== undefined) {
      updateData.province = event.province;
      changedFields.push('province');
    }

    if (event.country !== undefined) {
      updateData.country = event.country;
      changedFields.push('country');
    }

    if (event.isAutoGenerated !== undefined) {
      updateData.isAutoGenerated = event.isAutoGenerated;
      changedFields.push('isAutoGenerated');
    }

    if (event.isProfileComplete !== undefined) {
      updateData.isProfileComplete = event.isProfileComplete;
      changedFields.push('isProfileComplete');
    }

    // 添加更新时间
    updateData.updateTime = new Date();

    // 如果没有要更新的数据
    if (Object.keys(updateData).length <= 1) { // 只有updateTime
      console.log('没有要更新的数据');
      return {
        success: false,
        message: '没有要更新的数据',
        code: 'NO_DATA_TO_UPDATE'
      };
    }

    console.log('准备更新的数据:', JSON.stringify(updateData, null, 2));
    console.log('变更的字段:', changedFields);

    // 更新用户信息
    console.log('开始执行数据库更新操作...');
    const updateResult = await db.collection('users').doc(userId).update({
      data: updateData
    });

    console.log('数据库更新结果:', {
      stats: updateResult.stats,
      updated: updateResult.stats?.updated || 0
    });

    // 验证更新是否成功
    if (updateResult.stats?.updated === 0) {
      console.error('数据库更新失败：没有记录被更新');
      return {
        success: false,
        message: '数据库更新失败，没有记录被更新',
        code: 'UPDATE_NO_EFFECT',
        debug: {
          userId: userId,
          updateData: updateData,
          updateResult: updateResult
        }
      };
    }

    // 获取更新后的用户信息进行验证
    console.log('获取更新后的用户信息进行验证...');
    const updatedUserResult = await db.collection('users').doc(userId).get();

    if (!updatedUserResult.data) {
      console.error('获取更新后的用户信息失败');
      return {
        success: false,
        message: '获取更新后的用户信息失败',
        code: 'GET_UPDATED_USER_FAILED'
      };
    }

    const updatedUser = updatedUserResult.data;
    console.log('更新后的用户信息验证:', {
      nickName: updatedUser.nickName,
      signature: updatedUser.signature,
      avatarUrl: updatedUser.avatarUrl,
      updateTime: updatedUser.updateTime
    });

    // 验证关键字段是否更新成功
    const verificationErrors = [];
    if (event.nickName !== undefined && updatedUser.nickName !== updateData.nickName) {
      verificationErrors.push(`昵称更新失败: 期望 "${updateData.nickName}", 实际 "${updatedUser.nickName}"`);
    }
    if (event.signature !== undefined && updatedUser.signature !== updateData.signature) {
      verificationErrors.push(`签名更新失败: 期望 "${updateData.signature}", 实际 "${updatedUser.signature}"`);
    }
    if (event.avatarUrl !== undefined && updatedUser.avatarUrl !== updateData.avatarUrl) {
      verificationErrors.push(`头像更新失败: 期望 "${updateData.avatarUrl}", 实际 "${updatedUser.avatarUrl}"`);
    }

    if (verificationErrors.length > 0) {
      console.error('字段验证失败:', verificationErrors);
      return {
        success: false,
        message: '部分字段更新失败',
        code: 'FIELD_VERIFICATION_FAILED',
        errors: verificationErrors,
        debug: {
          expected: updateData,
          actual: {
            nickName: updatedUser.nickName,
            signature: updatedUser.signature,
            avatarUrl: updatedUser.avatarUrl
          }
        }
      };
    }

    // 如果更新了头像、昵称或签名，需要通知伴侣刷新缓存
    if (changedFields.includes('avatarUrl') || changedFields.includes('nickName') || changedFields.includes('signature')) {
      console.log('检测到关键信息更新，准备通知伴侣刷新缓存...');

      try {
        // 查找用户的情侣关系
        if (updatedUser.partnerId) {
          console.log('用户有伴侣关系，partnerId:', updatedUser.partnerId);

          // 通过partnerId查找情侣关系
          const coupleResult = await db.collection('couples').where({
            $or: [
              { user1Id: openid },
              { user2Id: openid }
            ],
            status: 'active'
          }).get();

          if (coupleResult.data.length > 0) {
            const coupleInfo = coupleResult.data[0];
            const partnerOpenid = coupleInfo.user1Id === openid ? coupleInfo.user2Id : coupleInfo.user1Id;

            console.log('找到伴侣openid:', partnerOpenid);

            // 发送通知给伴侣，提示刷新缓存
            const notificationResult = await cloud.callFunction({
              name: 'sendNotification',
              data: {
                type: 'profile_updated',
                recipientId: partnerOpenid,
                title: '伴侣资料更新',
                content: `${updatedUser.nickName || '你的伴侣'} 更新了个人资料`,
                data: {
                  updatedFields: changedFields,
                  shouldRefreshCache: true,
                  updatedBy: openid,
                  updateTime: updateData.updateTime
                }
              }
            });

            console.log('伴侣通知发送结果:', notificationResult.result?.success ? '成功' : '失败');
          } else {
            console.log('未找到有效的情侣关系');
          }
        } else {
          console.log('用户没有伴侣关系，无需通知');
        }
      } catch (partnerNotifyError) {
        console.error('通知伴侣失败，但不影响主流程:', partnerNotifyError);
        // 不影响主要的更新流程
      }
    }

    console.log('=== 用户资料更新成功 ===');
    return {
      success: true,
      message: '用户资料更新成功',
      data: {
        userInfo: updatedUser,
        updatedFields: changedFields,
        updateTime: updateData.updateTime,
        partnerNotified: changedFields.includes('avatarUrl') || changedFields.includes('nickName') || changedFields.includes('signature')
      }
    };

  } catch (error) {
    console.error('=== 更新用户资料失败 ===');
    console.error('错误类型:', error.name);
    console.error('错误消息:', error.message);
    console.error('错误堆栈:', error.stack);
    console.error('错误详情:', error);

    return {
      success: false,
      message: '更新用户资料失败: ' + error.message,
      error: error.message,
      code: 'UPDATE_FAILED',
      debug: {
        errorName: error.name,
        errorMessage: error.message,
        openid: openid
      }
    };
  }
};