// Content for the Feasibility Study section

function renderFeasibilityStudy() {
    return `
        <h3 class="text-xl font-bold text-pink-800 mb-3">项目想法可行性分析</h3>
        
        <div class="info-card">
            <div class="info-card-title">
                <i class="fas fa-lightbulb card-icon"></i> 概念来源与现实需求
            </div>
            <p class="mb-3">"情侣信誉券"的想法源于伴侣间承诺难以记录和履约的日常生活痛点。将承诺具象化为券，通过应用进行管理，具备创意性和情感价值。</p>
            <p>其核心玩法简单易懂，符合碎片化时间的互动需求。对于MVP阶段，专注于券的创建、发送、接收和核销核心闭环，技术实现难度适中，尤其是基于小程序平台。</p>
        </div>
        
        <div class="info-card">
            <div class="info-card-title">
                <i class="fas fa-plus-minus card-icon"></i> 积极作用与潜在挑战
            </div>
            <p class="font-semibold text-green-700 mb-2">积极作用：</p>
            <ul class="list-disc pl-5 mb-4 text-green-800">
                <li>通过游戏化和仪式感增强伴侣互动</li>
                <li>促进承诺履行</li>
                <li>记录甜蜜瞬间</li>
            </ul>
            
            <p class="font-semibold text-red-700 mb-2">潜在挑战：</p>
            <ul class="list-disc pl-5 mb-4 text-red-800">
                <li>用户接受度（避免过于儿戏化）</li>
                <li>长期使用意愿的维持</li>
                <li>作为初学者面临的技术实现细节</li>
            </ul>
            
            <p class="text-gray-600 italic">MVP阶段的目标正是验证核心概念的可行性，初步解决痛点，收集用户反馈。</p>
        </div>
        
        <h3 class="text-xl font-bold text-pink-800 mt-6 mb-3">商业价值评估</h3>
        
        <div class="info-card">
            <div class="info-card-title">
                <i class="fas fa-chart-line card-icon"></i> 价值与商业可行性
            </div>
            <p class="mb-3">本项目即便主要用于自用，其内在价值体现在情感投入和用户体验的提升。</p>
            <p class="mb-3">若考虑商业化可行性，虽然直接盈利模式在MVP阶段不明显，但其用户粘性（情侣用户）和互动频率具有潜在商业价值。</p>
            <p class="mb-3">后期可以考虑以下商业模式：</p>
            <ul class="list-disc pl-5 mb-4">
                <li>高级券面设计或功能付费</li>
                <li>周边商品或礼物推荐</li>
                <li>特殊节日活动</li>
                <li>与线下场景或商家合作</li>
            </ul>
        </div>
    `;
}
