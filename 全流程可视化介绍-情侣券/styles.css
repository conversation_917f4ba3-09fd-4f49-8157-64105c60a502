/* Additional custom styles for the document */

/* Typography improvements */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
}

/* Active navigation item styles */
.nav-item.active a {
    color: #ec4899; /* Tailwind pink-500 */
    border-bottom: 2px solid #ec4899;
    font-weight: 600;
}

/* Section styling */
.section-content {
    scroll-margin-top: 100px; /* Offset for sticky header when scrolling to anchors */
}

/* Prose content styling for better readability */
.prose {
    max-width: 65ch;
    margin-left: auto;
    margin-right: auto;
}

.prose h3 {
    color: #9d174d; /* Tailwind pink-800 */
    margin-top: 1.5em;
    margin-bottom: 0.75em;
    font-size: 1.25rem;
}

.prose p {
    margin-bottom: 1.25em;
}

.prose ul {
    margin-top: 0.5em;
    margin-bottom: 1em;
    padding-left: 1.5em;
    list-style-type: disc;
}

.prose li {
    margin-bottom: 0.5em;
}

/* SVG container styling */
.svg-container {
    width: 100%;
    overflow-x: auto;
    margin: 2em 0;
    padding: 1em;
    background: #f9fafb; /* Light gray background */
    border-radius: 0.5rem;
}

/* Code blocks */
.code-block {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    overflow-x: auto;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 0.9rem;
}

/* Tabs styling */
.tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1rem;
}

.tab {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-bottom: 2px solid transparent;
}

.tab.active {
    border-bottom-color: #ec4899;
    color: #ec4899;
    font-weight: 600;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Card styling */
.info-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    background-color: #f9fafb;
}

.info-card-title {
    display: flex;
    align-items: center;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #4b5563;
}

.card-icon {
    margin-right: 0.5rem;
    color: #ec4899;
}
