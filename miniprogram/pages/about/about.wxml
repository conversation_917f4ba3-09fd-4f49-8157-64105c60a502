<!--about.wxml-->
<view class="container">
  <!-- 应用信息 -->
  <view class="app-info-section">
    <view class="app-logo">
      <image class="logo-image" src="/images/app-logo1.svg" mode="aspectFit"></image>
    </view>
    <view class="app-details">
      <text class="app-name">情侣信誉券</text>
      <text class="app-slogan">记录我们的美好约定</text>
      <text class="app-version">版本 {{appVersion}}</text>
    </view>
  </view>

  <!-- 应用介绍 */
  <view class="intro-section">
    <view class="section-header">
      <text class="section-title">关于我们</text>
    </view>
    <view class="intro-content">
      <text class="intro-text">情侣信誉券是一款专为情侣设计的互动小程序，通过创建和兑现"信誉券"的方式，让承诺变得更加有趣和有意义。</text>
      <text class="intro-text">我们相信，每一个承诺都值得被认真对待，每一次兑现都能增进彼此的信任。让技术为爱情服务，让约定成为美好回忆。</text>
    </view>
  </view>

  <!-- 功能特色 */
  <view class="features-section">
    <view class="section-header">
      <text class="section-title">功能特色</text>
    </view>
    <view class="features-grid">
      <view class="feature-item">
        <view class="feature-icon">✨</view>
        <text class="feature-title">创建信誉券</text>
        <text class="feature-desc">自定义各种承诺券，让爱意具象化</text>
      </view>
      <view class="feature-item">
        <view class="feature-icon">🎁</view>
        <text class="feature-title">互相兑现</text>
        <text class="feature-desc">兑现彼此的承诺，增进感情</text>
      </view>
      <view class="feature-item">
        <view class="feature-icon">📱</view>
        <text class="feature-title">消息提醒</text>
        <text class="feature-desc">及时的通知让沟通更顺畅</text>
      </view>
      <view class="feature-item">
        <view class="feature-icon">🔒</view>
        <text class="feature-title">隐私安全</text>
        <text class="feature-desc">专属于两人的私密空间</text>
      </view>
    </view>
  </view>

  <!-- 开发团队 -->
  <view class="team-section">
    <view class="section-header">
      <text class="section-title">开发团队</text>
    </view>
    <view class="team-info">
      <view class="team-member">
        <view class="member-avatar">👨‍💻</view>
        <view class="member-info">
          <text class="member-name">灵心</text>
          <text class="member-role">负责产品设计和用户体验</text>
        </view>
      </view>
      <view class="team-member">
        <view class="member-avatar">👨‍💻</view>
        <view class="member-info">
          <text class="member-name">莹超</text>
          <text class="member-role">负责小程序全栈开发</text>
        </view>
      </view>
      
    </view>
  </view>

  <!-- 更新日志 -->
  <view class="changelog-section">
    <view class="section-header">
      <text class="section-title">版本日志</text>
    </view>
    <view class="changelog-list">
      <view 
        wx:for="{{changelogList}}" 
        wx:key="version"
        class="changelog-item"
      >
        <view class="changelog-header">
          <text class="version-number">v{{item.version}}</text>
          <text class="release-date">{{item.date}}</text>
        </view>
        <view class="changelog-content">
          <view 
            wx:for="{{item.changes}}" 
            wx:key="*this"
            wx:for-item="change"
            class="change-item"
          >
            <text class="change-type {{change.type}}">{{getChangeTypeLabel(change.type)}}</text>
            <text class="change-desc">{{change.description}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系信息 */
  <view class="contact-section">
    <view class="section-header">
      <text class="section-title">联系我们</text>
    </view>
    <view class="contact-info">
      <view class="contact-item" bindtap="copyEmail">
        <view class="contact-icon">📧</view>
        <text class="contact-text">{{contactEmail}}</text>
      </view>
      <view class="contact-item" bindtap="copyWechat">
        <view class="contact-icon">💬</view>
        <text class="contact-text">微信客服: {{wechatService}}</text>
      </view>
      <view class="contact-item">
        <view class="contact-icon">🌐</view>
        <text class="contact-text">{{officialWebsite}}</text>
      </view>
    </view>
  </view>

  <!-- 法律声明 -->
  <!-- <view class="legal-section">
    <view class="section-header">
      <text class="section-title">法律信息</text>
    </view>
    <view class="legal-links">
      <navigator class="legal-link" bindtap="viewPrivacyPolicy">隐私政策</navigator>
      <navigator class="legal-link" bindtap="viewTermsOfService">服务条款</navigator>
      <navigator class="legal-link" bindtap="viewUserAgreement">用户协议</navigator>
    </view>
  </view> -->

  <!-- 感谢信息 -->
  <view class="thanks-section">
    <view class="thanks-content">
      <text class="thanks-title">特别感谢</text>
      <text class="thanks-text">感谢每一对使用我们产品的情侣，是你们的信任和支持让我们不断前进。</text>
      <text class="thanks-text">愿每一份爱情都能被温柔对待，愿每一个承诺都能被认真兑现。</text>
      <view class="sponsor-info">
        <text class="sponsor-text">技术支持：微信云开发 | 腾讯云</text>
      </view>
    </view>
  </view>
</view>
  <!-- 底部信息 */
  <view class="footer-section">
    <text class="copyright">© 2024 情侣信誉券团队. All rights reserved.</text>
    <text class="build-info">Build {{buildNumber}} | {{buildDate}}</text>
  </view>
</view>

