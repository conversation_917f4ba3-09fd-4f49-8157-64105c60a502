/**
 * 日期时间处理工具函数
 * 包括日期格式化、时间前缀显示等功能
 */

/**
 * 格式化日期
 * @param {Date|string|number} date - 日期对象、时间戳或时间字符串
 * @param {string} formatString - 格式字符串，如 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, formatString = 'YYYY-MM-DD') {
    if (!date) return '';
    
    // 统一转换为 Date 对象
    let dateObj;
    if (date instanceof Date) {
        dateObj = date;
    } else if (typeof date === 'string' || typeof date === 'number') {
        dateObj = new Date(date);
    } else {
        return '';
    }
    
    // 检查日期是否有效
    if (isNaN(dateObj.getTime())) {
        return '';
    }
    
    // 获取各个时间分量
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');
    
    // 替换格式字符串
    return formatString
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 显示相对时间（几分钟前、几小时前等）
 * @param {Date|string|number} date - 日期
 * @returns {string} 相对时间描述
 */
function timeAgo(date) {
    if (!date) return '';
    
    let dateObj;
    if (date instanceof Date) {
        dateObj = date;
    } else if (typeof date === 'string' || typeof date === 'number') {
        dateObj = new Date(date);
    } else {
        return '';
    }
    
    if (isNaN(dateObj.getTime())) {
        return '';
    }
    
    const now = new Date();
    const diff = now.getTime() - dateObj.getTime();
    
    // 时间差的各个单位（毫秒）
    const minute = 60 * 1000;
    const hour = 60 * minute;
    const day = 24 * hour;
    const week = 7 * day;
    const month = 30 * day;
    const year = 365 * day;
    
    if (diff < minute) {
        return '刚刚';
    } else if (diff < hour) {
        const minutes = Math.floor(diff / minute);
        return `${minutes}分钟前`;
    } else if (diff < day) {
        const hours = Math.floor(diff / hour);
        return `${hours}小时前`;
    } else if (diff < week) {
        const days = Math.floor(diff / day);
        return `${days}天前`;
    } else if (diff < month) {
        const weeks = Math.floor(diff / week);
        return `${weeks}周前`;
    } else if (diff < year) {
        const months = Math.floor(diff / month);
        return `${months}个月前`;
    } else {
        const years = Math.floor(diff / year);
        return `${years}年前`;
    }
}

/**
 * 获取今天的开始时间
 * @returns {Date} 今天 00:00:00
 */
function getToday() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
}

/**
 * 获取明天的开始时间
 * @returns {Date} 明天 00:00:00
 */
function getTomorrow() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
}

/**
 * 检查日期是否是今天
 * @param {Date|string|number} date - 要检查的日期
 * @returns {boolean} 是否是今天
 */
function isToday(date) {
    if (!date) return false;
    
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return false;
    
    const today = new Date();
    
    return dateObj.getFullYear() === today.getFullYear() &&
           dateObj.getMonth() === today.getMonth() &&
           dateObj.getDate() === today.getDate();
}

/**
 * 检查日期是否过期
 * @param {Date|string|number} date - 要检查的日期
 * @returns {boolean} 是否过期
 */
function isExpired(date) {
    if (!date) return false;
    
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return false;
    
    return dateObj.getTime() < new Date().getTime();
}

/**
 * 计算两个日期之间的天数差
 * @param {Date|string|number} date1 - 开始日期
 * @param {Date|string|number} date2 - 结束日期
 * @returns {number} 天数差（负数表示date1在date2之后）
 */
function daysBetween(date1, date2) {
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    
    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
        return 0;
    }
    
    const timeDiff = d2.getTime() - d1.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

/**
 * 添加指定天数到日期
 * @param {Date|string|number} date - 基础日期
 * @param {number} days - 要添加的天数
 * @returns {Date} 新的日期对象
 */
function addDays(date, days) {
    const result = new Date(date);
    if (isNaN(result.getTime())) {
        return new Date();
    }
    
    result.setDate(result.getDate() + days);
    return result;
}

/**
 * 获取指定日期的星期几
 * @param {Date|string|number} date - 日期
 * @returns {string} 星期几
 */
function getWeekDay(date) {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';
    
    const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    return weekDays[dateObj.getDay()];
}

/**
 * 格式化为用户友好的日期时间显示
 * @param {Date|string|number} date - 日期
 * @returns {string} 友好的日期时间描述
 */
function friendlyDateTime(date) {
    if (!date) return '';
    
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';
    
    if (isToday(dateObj)) {
        return '今天 ' + formatDate(dateObj, 'HH:mm');
    }
    
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (dateObj.getFullYear() === yesterday.getFullYear() &&
        dateObj.getMonth() === yesterday.getMonth() &&
        dateObj.getDate() === yesterday.getDate()) {
        return '昨天 ' + formatDate(dateObj, 'HH:mm');
    }
    
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    
    if (dateObj > threeDaysAgo) {
        return getWeekDay(dateObj) + ' ' + formatDate(dateObj, 'HH:mm');
    }
    
    return formatDate(dateObj, 'MM-DD HH:mm');
}
