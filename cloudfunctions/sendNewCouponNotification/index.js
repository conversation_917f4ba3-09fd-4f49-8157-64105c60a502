/**
 * 发送新券通知云函数
 * 当有新的信任券被创建并发送时触发此云函数
 */

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数运行上下文
 * @returns {Object} 执行结果
 */
exports.main = async (event, context) => {
  console.log('开始执行发送新券通知云函数:', event);
  
  try {
    // 参数验证
    const { couponId, recipientId, senderInfo } = event;
    
    if (!couponId || !recipientId) {
      throw new Error('缺少必要参数: couponId 或 recipientId');
    }

    // 获取当前调用者的openid
    const { OPENID } = cloud.getWXContext();
    console.log('调用者openid:', OPENID);
    
    // 获取券详细信息
    const couponQuery = await db.collection('coupons').doc(couponId).get();
    if (!couponQuery.data) {
      throw new Error('券信息不存在');
    }
    
    const coupon = couponQuery.data;
    console.log('券信息:', {
      _id: coupon._id,
      name: coupon.name,
      creatorId: coupon.creatorId,
      recipientId: coupon.recipientId,
      status: coupon.status
    });
    
    // 验证券状态：现在券创建后直接为received状态
    if (coupon.status !== 'received') {
      console.warn(`券状态不正确，当前状态: ${coupon.status}，仍继续发送通知`);
    }

    // 验证接收方是否正确
    if (coupon.recipientId !== recipientId) {
      throw new Error('接收方ID与券信息不匹配');
    }

    // 获取接收方用户信息
    const receiverQuery = await db.collection('users').where({
      _openid: recipientId
    }).get();
    
    if (receiverQuery.data.length === 0) {
      throw new Error('接收方用户不存在');
    }
    
    const receiverInfo = receiverQuery.data[0];
    console.log('接收方信息:', {
      _id: receiverInfo._id,
      nickname: receiverInfo.nickname
    });

    // 构建通知内容
    const senderNickname = senderInfo?.nickname || '您的伴侣';
    const notificationTitle = '收到新的信任券';
    const notificationContent = `${senderNickname} 向您发送了一张信任券：${coupon.name}`;
    
    console.log('准备发送通知:', {
      title: notificationTitle,
      content: notificationContent,
      recipientId: recipientId
    });

    // 创建通知记录
    const notification = {
      title: notificationTitle,
      content: notificationContent,
      type: 'new_coupon',
      recipientId: recipientId,
      senderId: coupon.creatorId,
      couponId: couponId,
      couponName: coupon.name,
      isRead: false,
      createTime: new Date(),
      // 新增状态字段
      notificationStatus: 'active', // active: 活跃显示, hidden: 隐藏, moved: 已转移
      couponStatus: 'received', // 券的当前状态
      userRole: 'recipient' // recipient: 接收方, creator: 创建方
    };

    const notificationResult = await db.collection('notifications').add({
      data: notification
    });

    console.log('新券通知处理完成:', {
      couponId,
      couponName: coupon.name,
      recipientId,
      receiverNickname: receiverInfo.nickname,
      senderNickname,
      notificationId: notificationResult._id
    });

    // 返回成功结果
    return {
      success: true,
      message: '新券通知发送成功',
      data: {
        couponId,
        couponName: coupon.name,
        recipientId,
        receiverNickname: receiverInfo.nickname,
        senderNickname,
        notificationId: notificationResult._id,
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('发送新券通知云函数执行失败:', error);
    
    return {
      success: false,
      message: '云函数执行失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};
