/* about.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 50%, #F0F8FF 100%);
  padding: 30rpx;
  box-sizing: border-box;
  padding-bottom: 100rpx;
}

/* 应用信息区域 */
.app-info-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 30rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.app-logo {
  margin-bottom: 30rpx;
}

.logo-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}

.app-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.app-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  background: linear-gradient(45deg, #FF69B4, #9C27B0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-slogan {
  font-size: 28rpx;
  color: #666;
  font-style: italic;
}

.app-version {
  font-size: 24rpx;
  color: #999;
  font-family: monospace;
}

/* 通用区域样式 */
.intro-section, .features-section, .team-section, 
.changelog-section, .stats-section, .contact-section, 
.legal-section, .thanks-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
}

.section-header {
  margin-bottom: 30rpx;
  text-align: center;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 3rpx;
  background: linear-gradient(45deg, #FF69B4, #FFB6C1);
  border-radius: 2rpx;
}

/* 介绍内容 */
.intro-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.intro-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.7;
  text-align: justify;
}

/* 功能特色 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.feature-item {
  background: rgba(255, 105, 180, 0.05);
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  border: 1rpx solid rgba(255, 105, 180, 0.1);
}

.feature-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}

.feature-title {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}

.feature-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.5;
}

/* 团队信息 */
.team-info {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.team-member {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 24rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.member-avatar {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.member-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.member-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.member-role {
  font-size: 22rpx;
  color: #666;
}

/* 更新日志 */
.changelog-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.changelog-item {
  border-left: 4rpx solid #FF69B4;
  padding-left: 20rpx;
  background: #f8f9fa;
  border-radius: 0 12rpx 12rpx 0;
  padding: 20rpx;
}

.changelog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.version-number {
  font-size: 26rpx;
  font-weight: 600;
  color: #FF69B4;
  font-family: monospace;
}

.release-date {
  font-size: 22rpx;
  color: #999;
}

.changelog-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.change-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.change-type {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  flex-shrink: 0;
}

.change-type.new {
  background: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
}

.change-type.fix {
  background: rgba(33, 150, 243, 0.1);
  color: #2196F3;
}

.change-type.improve {
  background: rgba(255, 152, 0, 0.1);
  color: #FF9800;
}

.change-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  flex: 1;
}

/* 统计信息 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, rgba(255, 105, 180, 0.1), rgba(156, 39, 176, 0.1));
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 105, 180, 0.2);
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

/* 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: background-color 0.3s ease;
}

.contact-item:active {
  background: #e9ecef;
}

.contact-icon {
  font-size: 32rpx;
  width: 50rpx;
  text-align: center;
}

.contact-text {
  font-size: 26rpx;
  color: #333;
  font-family: monospace;
}

/* 法律声明 */
.legal-links {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 20rpx;
}

.legal-link {
  font-size: 24rpx;
  color: #FF69B4;
  text-decoration: underline;
  padding: 12rpx 20rpx;
  border: 1rpx solid #FF69B4;
  border-radius: 20rpx;
  background: rgba(255, 105, 180, 0.05);
  transition: all 0.3s ease;
}

.legal-link:active {
  background: rgba(255, 105, 180, 0.1);
}

/* 感谢信息 */
.thanks-content {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.thanks-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #FF69B4;
  margin-bottom: 10rpx;
}

.thanks-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.sponsor-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background: rgba(255, 105, 180, 0.05);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 105, 180, 0.1);
}

.sponsor-text {
  font-size: 22rpx;
  color: #999;
  font-style: italic;
}

/* 底部信息 */
.footer-section {
  text-align: center;
  padding: 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.copyright {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.build-info {
  font-size: 20rpx;
  color: #ccc;
  font-family: monospace;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 20rpx;
  }
  
  .features-grid, .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .legal-links {
    flex-direction: column;
    align-items: center;
  }
}

/* 特殊动画效果 */
.app-logo .logo-image {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

