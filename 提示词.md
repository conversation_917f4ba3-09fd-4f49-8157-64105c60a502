配色网站：https://www.bairesdev.com/tools/ai-colors （APP、网页的配色都有，滑倒下边点击复制即可）

你是一位资深APP UI设计师，现需根据以下产品需求创建UI方案，最终生成HTML完整代码：你需要作为产品经理规划这页面，作为设计师思考这些原型页面的设计；

## 产品需求：
用户指定的文档

## 技术规格：
▸ 单个HTML每行为6个横向排列的页面预览，可以有多行；代码需要包含所有功能页面；
▸ 画板尺寸：375x812（带1px描边模拟手机边框）；
▸ 必须包含：
矢量图标系统（使用<symbol>定义）
动态折线图（stroke-dasharray动画）
卡片悬浮效果（通过filter实现）
图片占位使用<image>标签外链unsplash
APP的单页布局尽量饱满，多增加一些好看的卡片布局或者图表样式

## 配色
*   **主色 (Primary Color):** `#FF6D96` (温暖的粉色系，象征浪漫、甜蜜)
    *   应用场景：核心操作按钮（如创建、绑定）、重要状态提示（如待使用券背景/边框）、品牌元素。
*   **辅色 (Secondary Color):** `#FFE4E1` (淡雅的浅粉色，与主色形成深浅对比)
    *   应用场景：背景填充、分割线、辅助元素的底色。
*   **点缀色 (Accent Color):** `#FFD700` (少量使用，用于突出强调)
    *   应用场景：特定状态图标（如兑换成功）、重要提醒文字、活动标记。
*   **中性色 (Neutral Colors):**
    *   `#333333` (深灰色，用于主要文本、标题，保证可读性)
    *   `#666666` (中灰色，用于辅助文本、描述信息)
    *   `#CCCCCC` (浅灰色，用于边框、分割线、禁用状态的元素)
    *   `#FFFFFF` (白色，用于背景、卡片、可点击区域)
      
      

## 新视觉风格：
玻璃拟态背景：
使用半透明磨砂玻璃质感，背景模糊处理，搭配柔和的光影效果，营造未来感和高级感。
低饱和配色：
主色调采用温暖的米白色背景，搭配深色文字（如深灰），辅以橙色作为点缀色，整体配色高级且富有层次感。
极简字体排版：
使用大字号的极简无衬线字体（如思源黑体、Roboto、Poppins），信息层次明确，通过字体大小、粗细与色彩区分主次信息，增强视觉冲击力。
表单优化：
表单及输入框去除边框线，仅保留流畅圆角背景，减少视觉噪音，提升整体界面的简洁与精致感。
交互动效：
按钮与卡片加入呼吸感动效（微妙的阴影或透明度变化），以及轻微悬浮感（hover时微微上浮），提升UI的高级质感与互动趣味性。
请基于上述优化后的提示词，设计出符合要求的高品质UI方案，并最终生成完整的HTML代码。