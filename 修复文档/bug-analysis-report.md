# 情侣信誉券小程序 - 字段不一致问题分析报告

## 🚨 严重字段不一致问题总结

### 1. 券字段名称不一致 (影响：券创建和显示功能)

**问题位置：**
- **PRD文档规范**: `name` (券名称)
- **数据库结构文档**: `title` (券标题)  
- **createOrUpdateCoupon云函数**: 使用 `name` 存储 (第115行)
- **getCoupons云函数**: 返回时尝试访问 `c.title` (第137行)
- **前端createCoupon**: 发送 `name` 字段 (第165行)

**影响：** 券创建成功，但列表显示时无法正确获取券名称

### 2. 过期时间字段不一致 (影响：券过期判断功能)

**问题位置：**
- **createOrUpdateCoupon云函数**: 使用 `expiryDate` (第119行)
- **前端myCoupons页面**: 期望 `expiresAt` 字段 (第136行)
- **updateCouponStatus云函数**: 使用 `expiryDate` (第63行)

**影响：** 前端无法正确显示和判断券过期状态

### 3. 用户伴侣绑定字段不一致 (影响：核心绑定功能)

**问题位置：**
- **数据库结构文档users表**: 没有 `partnerId` 字段，但有 `partnerId`
- **createOrUpdateCoupon云函数**: 使用 `partnerId` (第69-74行)
- **couples数据库结构**: 使用 `userId1`, `userId2`，但云函数期望不同的结构

**影响：** 情侣绑定功能可能完全无法正常工作

### 4. 券状态值不一致 (影响：券状态流转)

**问题位置：**
- **PRD文档**: 状态值为 `pending`, `used`, `expired`, `revoked`
- **数据库结构**: 状态值为 `active`, `redeemed`, `expired`, `cancelled`
- **云函数**: 使用 `sent`, `received`, `pending_redeem`, `redeemed`

**影响：** 券状态流转逻辑混乱，可能导致状态不正确

### 5. 通知字段映射不一致 (影响：通知系统)

**问题位置：**
- 部分云函数使用 `receiverId`，部分使用 `recipientId`
- 时间字段有使用 `createdTime` 和 `createTime` 的不一致

**影响：** 通知系统可能无法正常工作

## 🔧 核心修复策略

### 统一字段标准化方案

#### 券相关字段统一
```javascript
// 统一使用以下字段名
{
  name: "券名称",           // 统一使用name，废弃title
  description: "券描述",
  expiryDate: "过期时间",   // 统一使用expiryDate，废弃expiresAt
  status: "券状态",         // 统一使用新的状态系统
  creatorId: "创建者openid",
  recipientId: "接收者openid",
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

#### 用户绑定字段统一
```javascript
// users表添加partnerId字段，或修改绑定逻辑
{
  _openid: "用户openid",
  partnerId: "伴侣用户ID或关系ID"  // 需要统一定义
}
```

#### 状态值统一
```javascript
// 券状态统一使用：
// - sent: 已发送（待接收）
// - received: 已接收（可申请兑现）  
// - pending_redeem: 申请兑现中
// - redeemed: 已兑现
// - cancelled: 已取消
// - expired: 已过期
```

## 📋 修复优先级

### P0 (立即修复 - 阻塞基本功能)
1. 券名称字段不一致 (createOrUpdateCoupon vs getCoupons)
2. 用户绑定逻辑不一致 (partnerId字段缺失)
3. 过期时间字段不一致 (expiryDate vs expiresAt)

### P1 (优先修复 - 影响用户体验)
1. 券状态值标准化
2. 通知字段统一
3. 时间字段标准化

### P2 (后续优化)
1. 数据库索引优化
2. 错误处理完善
3. 代码注释更新

## 🎯 修复验证方案

1. **创建券测试**: 创建 → 发送 → 显示完整性
2. **绑定测试**: 用户绑定 → 券发送接收流程
3. **状态流转测试**: 发送 → 接收 → 申请兑现 → 批准/拒绝
4. **通知测试**: 各环节通知正确发送和显示
5. **过期处理测试**: 过期券的正确识别和处理

