<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情侣信誉券小程序规划文档</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- Header -->
    <header class="bg-gradient-to-r from-pink-400 to-purple-500 text-white shadow-md">
        <div class="container mx-auto px-4 py-6">
            <h1 class="text-3xl font-bold mb-2">情侣信誉券小程序</h1>
            <p class="text-lg opacity-90">规划与设计文档</p>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="sticky top-0 bg-white shadow-md z-10 border-b border-gray-200">
        <div class="container mx-auto px-4">
            <ul class="flex flex-wrap text-sm md:text-base">
                <li class="nav-item" data-section="feasibility">
                    <a href="#feasibility" class="block py-4 px-2 md:px-4 hover:text-pink-500 transition-colors">
                        <i class="fas fa-chart-line mr-1"></i> 可行性研究
                    </a>
                </li>
                <li class="nav-item" data-section="requirements">
                    <a href="#requirements" class="block py-4 px-2 md:px-4 hover:text-pink-500 transition-colors">
                        <i class="fas fa-clipboard-list mr-1"></i> 产品需求
                    </a>
                </li>
                <li class="nav-item" data-section="user-journey">
                    <a href="#user-journey" class="block py-4 px-2 md:px-4 hover:text-pink-500 transition-colors">
                        <i class="fas fa-route mr-1"></i> 用户旅程
                    </a>
                </li>
                <li class="nav-item" data-section="business-process">
                    <a href="#business-process" class="block py-4 px-2 md:px-4 hover:text-pink-500 transition-colors">
                        <i class="fas fa-project-diagram mr-1"></i> 业务流程
                    </a>
                </li>
                <li class="nav-item" data-section="technology">
                    <a href="#technology" class="block py-4 px-2 md:px-4 hover:text-pink-500 transition-colors">
                        <i class="fas fa-code mr-1"></i> 技术选型
                    </a>
                </li>
                <li class="nav-item" data-section="interaction">
                    <a href="#interaction" class="block py-4 px-2 md:px-4 hover:text-pink-500 transition-colors">
                        <i class="fas fa-exchange-alt mr-1"></i> 交互逻辑
                    </a>
                </li>
                <li class="nav-item" data-section="uiux">
                    <a href="#uiux" class="block py-4 px-2 md:px-4 hover:text-pink-500 transition-colors">
                        <i class="fas fa-palette mr-1"></i> UI/UX建议
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Introduction -->
        <section class="mb-12">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-2xl font-bold mb-4 text-pink-600">项目概览</h2>
                <p class="mb-4">本文档整合了"情侣信誉券"小程序的早期规划和设计信息，旨在为后续开发提供清晰的指导。文档按照逻辑结构划分为多个章节，包括可行性研究、产品需求、用户旅程、业务流程、技术选型、交互逻辑以及UI/UX建议等。</p>
                <p>通过本文档，开发团队可以全面了解项目的初始构想和规划全貌，明确开发工作的先后次序和重点关注事项，确保项目开发朝着正确的方向前进。</p>
            </div>
        </section>

        <!-- Feasibility Study Section -->
        <section id="feasibility" class="mb-12 section-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-chart-line text-2xl text-pink-500 mr-3"></i>
                    <h2 class="text-2xl font-bold text-gray-800">可行性研究</h2>
                </div>
                <div id="feasibility-content" class="prose max-w-none">
                    <!-- Content will be loaded via JS -->
                    <div class="flex justify-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Early PRD Section -->
        <section id="requirements" class="mb-12 section-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-clipboard-list text-2xl text-pink-500 mr-3"></i>
                    <h2 class="text-2xl font-bold text-gray-800">早期产品需求文档 (PRD)</h2>
                </div>
                <div id="requirements-content" class="prose max-w-none">
                    <!-- Content will be loaded via JS -->
                    <div class="flex justify-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- User Journey Section -->
        <section id="user-journey" class="mb-12 section-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-route text-2xl text-pink-500 mr-3"></i>
                    <h2 class="text-2xl font-bold text-gray-800">用户旅程</h2>
                </div>
                <div id="user-journey-content" class="prose max-w-none">
                    <!-- Content will be loaded via JS -->
                    <div class="flex justify-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Business Process Section -->
        <section id="business-process" class="mb-12 section-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-project-diagram text-2xl text-pink-500 mr-3"></i>
                    <h2 class="text-2xl font-bold text-gray-800">相关业务流程</h2>
                </div>
                <div id="business-process-content" class="prose max-w-none">
                    <!-- Content will be loaded via JS -->
                    <div class="flex justify-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technology Selection Section -->
        <section id="technology" class="mb-12 section-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-code text-2xl text-pink-500 mr-3"></i>
                    <h2 class="text-2xl font-bold text-gray-800">技术选型</h2>
                </div>
                <div id="technology-content" class="prose max-w-none">
                    <!-- Content will be loaded via JS -->
                    <div class="flex justify-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Interaction Logic Section -->
        <section id="interaction" class="mb-12 section-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-exchange-alt text-2xl text-pink-500 mr-3"></i>
                    <h2 class="text-2xl font-bold text-gray-800">交互逻辑</h2>
                </div>
                <div id="interaction-content" class="prose max-w-none">
                    <!-- Content will be loaded via JS -->
                    <div class="flex justify-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- UI/UX Suggestions Section -->
        <section id="uiux" class="mb-12 section-content">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-palette text-2xl text-pink-500 mr-3"></i>
                    <h2 class="text-2xl font-bold text-gray-800">UI/UX建议和输出</h2>
                </div>
                <div id="uiux-content" class="prose max-w-none">
                    <!-- Content will be loaded via JS -->
                    <div class="flex justify-center">
                        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500"></div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-6">
        <div class="container mx-auto px-4 text-center">
            <p>© 2023 情侣信誉券小程序规划文档</p>
            <p class="text-gray-400 text-sm mt-2">本文档整合了情侣信誉券小程序的早期规划和设计信息，仅供项目开发参考使用。</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="feasibility.js"></script>
    <script src="requirements.js"></script>
    <script src="userJourney.js"></script>
    <script src="businessProcess.js"></script>
    <script src="technologySelection.js"></script>
    <script src="interactionLogic.js"></script>
    <script src="uiux.js"></script>
    <script src="main.js"></script>
</body>
</html>
