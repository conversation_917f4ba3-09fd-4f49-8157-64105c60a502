/**
 * 通用通知发送云函数
 * 提供统一的通知发送接口，支持多种通知类型
 */

const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event 云函数调用参数
 * @param {string} event.type 通知类型
 * @param {string} event.recipientId 接收方用户ID
 * @param {string} event.title 通知标题
 * @param {string} event.content 通知内容
 * @param {string} event.couponId 关联的券ID（可选）
 * @param {Object} event.data 额外数据（可选）
 * @param {Object} context 云函数运行上下文
 */
exports.main = async (event, context) => {
  console.log('开始执行通知发送云函数:', event);
  
  try {
    const {
      type,
      recipientId,
      title,
      content,
      couponId = null,
      data = {},
      sendPush = false // 是否发送推送消息
    } = event;

    // 参数验证
    if (!type || !recipientId || !title || !content) {
      throw new Error('缺少必要参数: type, recipientId, title, content');
    }

    // 获取微信调用上下文
    const wxContext = cloud.getWXContext();
    const { OPENID } = wxContext;

    const currentTime = new Date();

    // 构建通知数据
    const notificationData = {
      type: type,
      recipientId: recipientId,
      senderId: OPENID || null,
      title: title,
      content: content,
      couponId: couponId,
      data: data,
      isRead: false,
      createTime: currentTime,
      updateTime: currentTime,
      notificationStatus: 'active' // 确保通知状态为活跃
    };

    // 验证接收方用户是否存在
    const userQuery = await db.collection('users').where({
      _openid: recipientId
    }).get();

    if (userQuery.data.length === 0) {
      throw new Error('接收方用户不存在');
    }

    const userInfo = userQuery.data[0];

    // 检查用户通知设置
    if (userInfo.settings && userInfo.settings.allowNotifications === false) {
      console.log(`用户 ${recipientId} 已关闭通知，跳过发送`);
      return {
        success: true,
        message: '用户已关闭通知，跳过发送',
        data: {
          type: type,
          recipientId: recipientId,
          skipped: true,
          timestamp: currentTime.toISOString()
        }
      };
    }

    // 保存通知到数据库
    const saveResult = await db.collection('notifications').add({
      data: notificationData
    });

    if (!saveResult._id) {
      throw new Error('通知保存失败');
    }

    console.log(`通知已保存: ${saveResult._id}, 类型: ${type}, 接收方: ${recipientId}`);

    // 准备返回结果
    const result = {
      success: true,
      message: '通知发送成功',
      data: {
        notificationId: saveResult._id,
        type: type,
        recipientId: recipientId,
        title: title,
        content: content,
        couponId: couponId,
        createTime: currentTime.toISOString(),
        timestamp: currentTime.toISOString()
      }
    };

    // 如果需要发送推送消息（后续可扩展订阅消息功能）
    if (sendPush) {
      try {
        // TODO: 这里可以调用微信订阅消息API
        // await sendSubscribeMessage(recipientId, type, notificationData);
        console.log('推送消息功能待实现');
        result.data.pushSent = false;
        result.data.pushMessage = '推送消息功能待实现';
      } catch (pushError) {
        console.error('发送推送消息失败:', pushError);
        result.data.pushSent = false;
        result.data.pushError = pushError.message;
      }
    }

    return result;

  } catch (error) {
    console.error('通知发送云函数执行失败:', error);
    
    return {
      success: false,
      message: '通知发送失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}; 