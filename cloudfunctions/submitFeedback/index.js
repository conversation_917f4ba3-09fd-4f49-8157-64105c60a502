const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 提交用户反馈
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    const { 
      type, 
      content, 
      rating, 
      contactInfo, 
      deviceInfo,
      appVersion,
      screenshots 
    } = event
    
    // 参数验证
    if (!type || !content) {
      return {
        success: false,
        error: 'MISSING_PARAMS',
        message: '缺少必要参数：type, content'
      }
    }
    
    // 验证反馈类型
    const validTypes = ['bug', 'suggestion', 'complaint', 'praise', 'other']
    if (!validTypes.includes(type)) {
      return {
        success: false,
        error: 'INVALID_TYPE',
        message: '无效的反馈类型'
      }
    }
    
    // 验证内容长度
    if (content.length > 1000) {
      return {
        success: false,
        error: 'CONTENT_TOO_LONG',
        message: '反馈内容不能超过1000字'
      }
    }
    
    // 获取用户信息
    const userResult = await db.collection('users')
      .where({
        _openid: openid
      })
      .get()
    
    const user = userResult.data[0] || {}
    
    // 构建反馈数据
    const feedbackData = {
              userOpenid: openid,
      userNickName: user.nickName || '匿名用户',
      type: type,
      content: content.trim(),
      rating: rating || null,
      contactInfo: contactInfo || '',
      deviceInfo: deviceInfo || {},
      appVersion: appVersion || '',
      screenshots: screenshots || [],
      status: 'pending', // pending, processing, resolved, closed
      priority: 'normal', // low, normal, high, urgent
      createTime: new Date(),
      updateTime: new Date()
    }
    
    // 根据反馈类型和内容自动设置优先级
    if (type === 'bug' || content.includes('崩溃') || content.includes('无法使用')) {
      feedbackData.priority = 'high'
    } else if (type === 'complaint') {
      feedbackData.priority = 'high'
    }
    
    // 保存反馈到数据库
    const addResult = await db.collection('feedbacks')
      .add({
        data: feedbackData
      })
    
    // 可选：发送管理员通知
    try {
      await db.collection('adminNotifications')
        .add({
          data: {
            type: 'new_feedback',
            title: `新${getTypeName(type)}反馈`,
            message: `用户${user.nickName || '匿名'}提交了${getTypeName(type)}反馈`,
            feedbackId: addResult._id,
            priority: feedbackData.priority,
            isRead: false,
            createTime: new Date()
          }
        })
    } catch (adminNotificationError) {
      console.warn('发送管理员通知失败:', adminNotificationError)
      // 不影响主流程
    }
    
    return {
      success: true,
      message: '反馈提交成功，我们会认真处理您的意见',
      feedbackId: addResult._id
    }
    
  } catch (error) {
    console.error('提交反馈失败:', error)
    return {
      success: false,
      error: 'SYSTEM_ERROR',
      message: '系统错误，请稍后重试'
    }
  }
}

/**
 * 获取反馈类型中文名称
 * @param {string} type 反馈类型
 * @returns {string} 中文名称
 */
function getTypeName(type) {
  const typeNames = {
    'bug': 'Bug',
    'suggestion': '建议',
    'complaint': '投诉',
    'praise': '表扬',
    'other': '其他'
  }
  return typeNames[type] || '其他'
} 