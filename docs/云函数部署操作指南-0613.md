# 云函数部署操作指南

## 📋 准备工作

### 1. 环境检查
- [ ] 确认微信开发者工具已更新到最新版本
- [ ] 确认小程序AppID已配置正确
- [ ] 确认云开发环境已开通并选择正确
- [ ] 确认当前用户具有云函数部署权限

### 2. 项目状态确认
- [ ] 确认代码已保存并同步
- [ ] 确认新增的9个云函数文件夹存在
- [ ] 确认每个云函数都包含 `index.js` 和 `package.json`

## 🚀 部署步骤详解

### 第一步：打开云开发控制台

1. 在微信开发者工具中打开项目
2. 点击顶部工具栏的"云开发"按钮
3. 进入云开发控制台界面
4. 选择"云函数"标签页

### 第二步：逐个部署新增云函数

#### 方式一：通过开发者工具部署（推荐）

**部署 markNotificationAsRead**
1. 在左侧文件树中找到 `cloudfunctions/markNotificationAsRead` 文件夹
2. 右键点击该文件夹
3. 选择"上传并部署：云端安装依赖"
4. 等待上传完成（约1-2分钟）
5. 查看控制台输出，确认无错误信息

**部署 markAllNotificationsAsRead**
1. 右键点击 `cloudfunctions/markAllNotificationsAsRead` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

**部署 deleteNotification**
1. 右键点击 `cloudfunctions/deleteNotification` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

**部署 clearAllNotifications**
1. 右键点击 `cloudfunctions/clearAllNotifications` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

**部署 getNotificationCount**
1. 右键点击 `cloudfunctions/getNotificationCount` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

**部署 unbindCouple**
1. 右键点击 `cloudfunctions/unbindCouple` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

**部署 getCouponDetail**
1. 右键点击 `cloudfunctions/getCouponDetail` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

**部署 getAppStats**
1. 右键点击 `cloudfunctions/getAppStats` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

**部署 submitFeedback**
1. 右键点击 `cloudfunctions/submitFeedback` 文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

#### 方式二：通过云开发控制台部署

1. 在云开发控制台的"云函数"页面
2. 点击"新建云函数"
3. 选择"从本地上传"
4. 选择对应的云函数文件夹
5. 点击"确定"并等待部署

### 第三步：验证部署结果

#### 3.1 检查云函数列表
在云开发控制台的云函数页面，确认以下9个新函数已出现：
- [ ] markNotificationAsRead
- [ ] markAllNotificationsAsRead
- [ ] deleteNotification
- [ ] clearAllNotifications
- [ ] getNotificationCount
- [ ] unbindCouple
- [ ] getCouponDetail
- [ ] getAppStats
- [ ] submitFeedback

#### 3.2 检查部署状态
确认每个云函数的状态都显示为"正常"或"部署成功"

#### 3.3 查看部署日志
1. 点击任意一个新部署的云函数
2. 查看"日志"标签页
3. 确认没有部署错误信息

## 🧪 功能测试指南

### 测试前准备
1. 确保小程序能正常运行
2. 确保已有测试用户数据
3. 准备测试用例和预期结果

### 通知功能测试

#### 测试 getNotificationCount
```javascript
// 在小程序中调用测试
wx.cloud.callFunction({
  name: 'getNotificationCount',
  success: res => {
    console.log('获取通知数量成功:', res)
    // 检查返回数据格式
    // { success: true, data: { unreadCount: 0, totalCount: 0 } }
  },
  fail: err => {
    console.error('获取通知数量失败:', err)
  }
})
```

#### 测试 markNotificationAsRead
```javascript
// 需要先有通知ID，可以通过getNotifications获取
wx.cloud.callFunction({
  name: 'markNotificationAsRead',
  data: {
    notificationId: 'test_notification_id'
  },
  success: res => {
    console.log('标记已读成功:', res)
  }
})
```

#### 测试 markAllNotificationsAsRead
```javascript
wx.cloud.callFunction({
  name: 'markAllNotificationsAsRead',
  success: res => {
    console.log('标记所有已读成功:', res)
    // 检查返回的更新数量
  }
})
```

### 业务功能测试

#### 测试 getCouponDetail
```javascript
wx.cloud.callFunction({
  name: 'getCouponDetail',
  data: {
    couponId: 'test_coupon_id'
  },
  success: res => {
    console.log('获取券详情成功:', res)
    // 检查返回的详细信息是否完整
  }
})
```

#### 测试 getAppStats
```javascript
wx.cloud.callFunction({
  name: 'getAppStats',
  success: res => {
    console.log('获取应用统计成功:', res)
    // 检查统计数据是否合理
  }
})
```

#### 测试 submitFeedback
```javascript
wx.cloud.callFunction({
  name: 'submitFeedback',
  data: {
    type: 'suggestion',
    content: '这是一个测试反馈',
    rating: 5
  },
  success: res => {
    console.log('提交反馈成功:', res)
  }
})
```

## ⚠️ 常见问题及解决方案

### 部署失败问题

#### 1. 依赖安装失败
**症状**: 部署时提示"npm install failed"
**解决方案**:
- 检查 package.json 格式是否正确
- 尝试使用"上传并部署：不校验依赖"
- 检查网络连接是否正常

#### 2. 权限问题
**症状**: 提示"没有部署权限"
**解决方案**:
- 确认当前微信账号是小程序管理员
- 检查云开发环境是否正确选择
- 联系小程序管理员添加权限

#### 3. 代码语法错误
**症状**: 部署成功但调用失败
**解决方案**:
- 检查 index.js 代码语法
- 查看云函数日志获取错误详情
- 检查 exports.main 函数是否正确导出

### 调用失败问题

#### 1. 函数不存在
**症状**: 调用时提示"cloud function not found"
**解决方案**:
- 确认云函数已成功部署
- 检查调用的函数名是否正确
- 刷新开发者工具并重试

#### 2. 参数错误
**症状**: 返回参数验证失败错误
**解决方案**:
- 检查传递的参数是否符合要求
- 查看云函数代码中的参数验证逻辑
- 确认参数类型和格式正确

#### 3. 权限不足
**症状**: 返回权限验证失败错误
**解决方案**:
- 确认用户已登录
- 检查用户是否有相应操作权限
- 查看云函数权限验证逻辑

## 📊 部署进度追踪

使用以下清单追踪部署进度：

### 云函数部署状态
- [ ] markNotificationAsRead - 部署完成
- [ ] markAllNotificationsAsRead - 部署完成
- [ ] deleteNotification - 部署完成
- [ ] clearAllNotifications - 部署完成
- [ ] getNotificationCount - 部署完成
- [ ] unbindCouple - 部署完成
- [ ] getCouponDetail - 部署完成
- [ ] getAppStats - 部署完成
- [ ] submitFeedback - 部署完成

### 功能测试状态
- [ ] 通知系统功能测试完成
- [ ] 券管理功能测试完成
- [ ] 用户管理功能测试完成
- [ ] 统计功能测试完成
- [ ] 反馈功能测试完成

### 集成测试状态
- [ ] 前端页面调用测试完成
- [ ] 数据流转测试完成
- [ ] 异常情况测试完成
- [ ] 性能测试完成

## 📈 性能监控

### 关键指标监控
部署完成后，需要关注以下性能指标：

1. **云函数调用量**: 每个函数的日调用次数
2. **响应时间**: 平均响应时间应控制在1秒内
3. **错误率**: 错误率应控制在1%以下
4. **并发量**: 高峰期并发调用数

### 监控方法
1. 在云开发控制台查看云函数统计
2. 设置关键指标告警
3. 定期查看云函数日志
4. 关注用户反馈和使用情况

---

**创建时间**: 2024年6月13日  
**文档版本**: v1.0  
**适用项目**: 情侣信誉券小程序 