/**
 * 用户状态管理辅助工具
 * 本地存储封装，用于获取/设置用户信息、登录态token等
 */

// 存储键名常量
const STORAGE_KEYS = {
    USER_INFO: 'user_info',
    TOKEN: 'access_token',
    COUPLE_INFO: 'couple_info',
    LOGIN_TIME: 'login_time',
    SETTINGS: 'app_settings'
};

/**
 * 设置用户信息
 * @param {Object} userInfo - 用户信息对象
 */
function setUserInfo(userInfo) {
    if (!userInfo) return;
    
    try {
        // 在微信小程序中使用 wx.setStorageSync
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify({
            ...userInfo,
            updatedAt: new Date().toISOString()
        }));
        console.log('用户信息已保存');
    } catch (error) {
        console.error('保存用户信息失败:', error);
    }
}

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息对象
 */
function getUserInfo() {
    try {
        // 在微信小程序中使用 wx.getStorageSync
        const userInfoStr = localStorage.getItem(STORAGE_KEYS.USER_INFO);
        if (!userInfoStr) return null;
        
        const userInfo = JSON.parse(userInfoStr);
        
        // 检查用户信息是否过期（7天）
        if (userInfo.updatedAt) {
            const updatedTime = new Date(userInfo.updatedAt).getTime();
            const now = new Date().getTime();
            const sevenDays = 7 * 24 * 60 * 60 * 1000;
            
            if (now - updatedTime > sevenDays) {
                console.log('用户信息已过期');
                clearUserInfo();
                return null;
            }
        }
        
        return userInfo;
    } catch (error) {
        console.error('获取用户信息失败:', error);
        return null;
    }
}

/**
 * 清除用户信息
 */
function clearUserInfo() {
    try {
        // 在微信小程序中使用 wx.removeStorageSync
        localStorage.removeItem(STORAGE_KEYS.USER_INFO);
        console.log('用户信息已清除');
    } catch (error) {
        console.error('清除用户信息失败:', error);
    }
}

/**
 * 设置访问令牌
 * @param {string} token - 访问令牌
 */
function setToken(token) {
    if (!token) return;
    
    try {
        localStorage.setItem(STORAGE_KEYS.TOKEN, token);
        localStorage.setItem(STORAGE_KEYS.LOGIN_TIME, new Date().toISOString());
        console.log('Token已保存');
    } catch (error) {
        console.error('保存Token失败:', error);
    }
}

/**
 * 获取访问令牌
 * @returns {string|null} 访问令牌
 */
function getToken() {
    try {
        const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
        const loginTime = localStorage.getItem(STORAGE_KEYS.LOGIN_TIME);
        
        if (!token || !loginTime) return null;
        
        // 检查Token是否过期（24小时）
        const loginTimeStamp = new Date(loginTime).getTime();
        const now = new Date().getTime();
        const twentyFourHours = 24 * 60 * 60 * 1000;
        
        if (now - loginTimeStamp > twentyFourHours) {
            console.log('Token已过期');
            clearToken();
            return null;
        }
        
        return token;
    } catch (error) {
        console.error('获取Token失败:', error);
        return null;
    }
}

/**
 * 清除访问令牌
 */
function clearToken() {
    try {
        localStorage.removeItem(STORAGE_KEYS.TOKEN);
        localStorage.removeItem(STORAGE_KEYS.LOGIN_TIME);
        console.log('Token已清除');
    } catch (error) {
        console.error('清除Token失败:', error);
    }
}

/**
 * 设置情侣信息
 * @param {Object} coupleInfo - 情侣信息对象
 */
function setCoupleInfo(coupleInfo) {
    if (!coupleInfo) return;
    
    try {
        localStorage.setItem(STORAGE_KEYS.COUPLE_INFO, JSON.stringify({
            ...coupleInfo,
            updatedAt: new Date().toISOString()
        }));
        console.log('情侣信息已保存');
    } catch (error) {
        console.error('保存情侣信息失败:', error);
    }
}

/**
 * 获取情侣信息
 * @returns {Object|null} 情侣信息对象
 */
function getCoupleInfo() {
    try {
        const coupleInfoStr = localStorage.getItem(STORAGE_KEYS.COUPLE_INFO);
        if (!coupleInfoStr) return null;
        
        return JSON.parse(coupleInfoStr);
    } catch (error) {
        console.error('获取情侣信息失败:', error);
        return null;
    }
}

/**
 * 清除情侣信息
 */
function clearCoupleInfo() {
    try {
        localStorage.removeItem(STORAGE_KEYS.COUPLE_INFO);
        console.log('情侣信息已清除');
    } catch (error) {
        console.error('清除情侣信息失败:', error);
    }
}

/**
 * 设置应用设置
 * @param {Object} settings - 设置对象
 */
function setAppSettings(settings) {
    if (!settings) return;
    
    try {
        localStorage.setItem(STORAGE_KEYS.SETTINGS, JSON.stringify(settings));
        console.log('应用设置已保存');
    } catch (error) {
        console.error('保存应用设置失败:', error);
    }
}

/**
 * 获取应用设置
 * @returns {Object} 设置对象
 */
function getAppSettings() {
    try {
        const settingsStr = localStorage.getItem(STORAGE_KEYS.SETTINGS);
        if (!settingsStr) {
            // 返回默认设置
            return {
                notifications: true,
                sound: true,
                vibration: true,
                theme: 'auto'
            };
        }
        
        return JSON.parse(settingsStr);
    } catch (error) {
        console.error('获取应用设置失败:', error);
        return {
            notifications: true,
            sound: true,
            vibration: true,
            theme: 'auto'
        };
    }
}

/**
 * 更新用户状态
 * @param {Object} updates - 要更新的字段
 */
function updateUserInfo(updates) {
    if (!updates) return;
    
    const currentInfo = getUserInfo();
    if (!currentInfo) {
        console.log('没有找到现有用户信息');
        return;
    }
    
    const updatedInfo = {
        ...currentInfo,
        ...updates,
        updatedAt: new Date().toISOString()
    };
    
    setUserInfo(updatedInfo);
}

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
function isLoggedIn() {
    const token = getToken();
    const userInfo = getUserInfo();
    return !!(token && userInfo);
}

/**
 * 检查是否已绑定情侣
 * @returns {boolean} 是否已绑定
 */
function isCoupleBindded() {
    const userInfo = getUserInfo();
    const coupleInfo = getCoupleInfo();
    return !!(userInfo && userInfo.coupleId && coupleInfo);
}

/**
 * 获取情侣对象的用户信息
 * @returns {Object|null} 情侣的用户信息
 */
function getPartnerInfo() {
    const coupleInfo = getCoupleInfo();
    const userInfo = getUserInfo();
    
    if (!coupleInfo || !userInfo) return null;
    
    const myOpenId = userInfo.openid;
    
    // 返回情侣中的另一方信息
    if (coupleInfo.user1 && coupleInfo.user1.openid === myOpenId) {
        return coupleInfo.user2;
    } else if (coupleInfo.user2 && coupleInfo.user2.openid === myOpenId) {
        return coupleInfo.user1;
    }
    
    return null;
}

/**
 * 清除所有本地数据
 */
function clearAllData() {
    try {
        Object.values(STORAGE_KEYS).forEach(key => {
            localStorage.removeItem(key);
        });
        console.log('所有本地数据已清除');
    } catch (error) {
        console.error('清除本地数据失败:', error);
    }
}

/**
 * 导出用户数据
 * @returns {Object} 所有用户相关数据
 */
function exportUserData() {
    return {
        userInfo: getUserInfo(),
        coupleInfo: getCoupleInfo(),
        settings: getAppSettings(),
        hasToken: !!getToken(),
        exportTime: new Date().toISOString()
    };
}
