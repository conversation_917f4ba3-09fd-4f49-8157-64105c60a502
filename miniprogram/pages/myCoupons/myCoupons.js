// pages/myCoupons/myCoupons.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    coupons: [],
    filteredCoupons: [],
    isLoading: true,
    
    // 简化的tab设计
    activeMainTab: 'received', // 'received' | 'sent'
    activeSubTab: 'pending', // 仅对received有效：'pending' | 'redeemed' | 'expired'
    
    // Tab配置
    mainTabs: [
      { key: 'received', label: '我收到的' },
      { key: 'sent', label: '我发送的' }
    ],
    
    subTabs: [
      { key: 'pending', label: '待兑现' },
      { key: 'redeemed', label: '已兑现' },
      { key: 'expired', label: '已失效' }
    ],
    
    // 数据统计
    couponStats: {
      received: { pending: 0, redeemed: 0, expired: 0, total: 0 },
      sent: { total: 0 }
    },
    
    themeGradient: app.globalData.gradientBackground,
    
    // 动画状态
    tabAnimationClass: '',
    contentAnimationClass: '',
  },

  onLoad: function (options) {
    console.log("我的券页面加载");
    this.setData({ 
      userInfo: app.globalData.userInfo,
      themeGradient: app.globalData.gradientBackground,
    });
    
    // 添加全局事件监听
    this.globalRefreshHandler = (data) => {
      console.log("我的券页面接收到全局刷新事件:", data);
      this.setData({
        userInfo: data.userInfo
      });
      this.loadCouponsWithRetry();
    };
    app.addEventListener('globalDataRefresh', this.globalRefreshHandler);

    // 添加切换到"我发送的"页签的事件监听
    this.switchToSentTabHandler = (data) => {
      console.log("我的券页面收到切换到发送页签事件:", data);
      this.setData({
        activeMainTab: 'sent',
        activeSubTab: '' // 发送的券没有子tab
      });
      // 重新应用过滤器
      setTimeout(() => {
        this.applyFilter();
      }, 100);
    };
    app.addEventListener('switchToSentTab', this.switchToSentTabHandler);
  },

  onShow: function () {
    console.log("我的券页面显示，当前tab:", this.data.activeMainTab, this.data.activeSubTab);
    this.setData({
      userInfo: app.globalData.userInfo,
    });

    // 检查未读消息数量，确保徽标实时更新
    if (app.globalData.userInfo) {
      app.checkUnreadMessages();
    }

    // 确保在数据加载完成后应用正确的过滤器
    this.loadCouponsWithRetry().then(() => {
      // 数据加载完成后，确保应用正确的过滤器
      setTimeout(() => {
        console.log("数据加载完成，重新应用过滤器");
        this.applyFilter();
      }, 100);
    });
  },

  /**
   * 主Tab切换
   */
  switchMainTab: function (e) {
    const newTab = e.currentTarget.dataset.tab;
    if (newTab === this.data.activeMainTab) return;
    
    console.log("切换主Tab:", newTab);
    
    // 添加切换动画
    this.setData({
      tabAnimationClass: 'tab-switching',
      contentAnimationClass: 'content-fade-out'
    });
    
    setTimeout(() => {
      this.setData({
        activeMainTab: newTab,
        activeSubTab: newTab === 'received' ? 'pending' : '', // 只有收到的才有子tab
        tabAnimationClass: '',
        contentAnimationClass: 'content-fade-in'
      });
      
      // 立即应用过滤器
      this.applyFilter();
      
      // 清除动画类
      setTimeout(() => {
        this.setData({ contentAnimationClass: '' });
      }, 300);
    }, 150);
  },

  /**
   * 子Tab切换（仅对收到的券有效）
   */
  switchSubTab: function (e) {
    const newTab = e.currentTarget.dataset.tab;
    if (newTab === this.data.activeSubTab || this.data.activeMainTab !== 'received') return;
    
    console.log("切换子Tab:", newTab);
    
    // 添加切换动画
    this.setData({
      contentAnimationClass: 'content-slide-out'
    });
    
    setTimeout(() => {
      this.setData({
        activeSubTab: newTab,
        contentAnimationClass: 'content-slide-in'
      });
      
      // 立即应用过滤器
      this.applyFilter();
      
      // 清除动画类
      setTimeout(() => {
        this.setData({ contentAnimationClass: '' });
      }, 300);
    }, 150);
  },

  /**
   * 带重试机制的数据加载
   */
  loadCouponsWithRetry: async function () {
    const maxRetries = 3;
    let retryCount = 0;
    
    while (retryCount < maxRetries) {
      try {
        console.log(`尝试加载券数据，第${retryCount + 1}次`);
        
        // 检查用户信息
        if (!app.globalData.userInfo) {
          console.log("用户信息不存在，尝试重新获取");
          await this.waitForUserInfo();
        }
        
        // 加载券数据
        await this.loadCouponsAsync();
        console.log("券数据加载成功");
        return;
        
      } catch (error) {
        retryCount++;
        console.error(`第${retryCount}次加载失败:`, error);
        
        if (retryCount >= maxRetries) {
          console.error("达到最大重试次数，加载失败");
          wx.showToast({ 
            title: "数据加载失败，请下拉刷新", 
            icon: "none" 
          });
          this.setData({ 
            coupons: [],
            isLoading: false,
          });
          this.applyFilter();
        } else {
          await this.sleep(1000 * retryCount);
        }
      }
    }
  },

  /**
   * 等待用户信息可用
   */
  waitForUserInfo: function () {
    return new Promise((resolve, reject) => {
      const checkUserInfo = () => {
        if (app.globalData.userInfo) {
          this.setData({ userInfo: app.globalData.userInfo });
          resolve(app.globalData.userInfo);
        } else {
          app.performLogin((error, userInfo) => {
            if (error) {
              reject(error);
            } else {
              this.setData({ userInfo: userInfo });
              resolve(userInfo);
            }
          });
        }
      };
      
      checkUserInfo();
    });
  },

  /**
   * 异步版本的加载券数据
   */
  loadCouponsAsync: function () {
    return new Promise((resolve, reject) => {
      if (!this.data.userInfo) {
        reject(new Error("用户信息不存在"));
        return;
      }
      
      this.setData({ isLoading: true });
      
      console.log("调用getCoupons云函数，用户ID:", this.data.userInfo._openid);
      
      wx.cloud.callFunction({
        name: "getCoupons",
        data: {
          filter: "all"
        },
        success: (res) => {
          console.log("获取券数据结果:", res);
          
          if (res.result && res.result.success) {
            const rawCoupons = res.result.data.coupons || [];
            console.log("原始券数据数量:", rawCoupons.length);
            
            // 处理券数据
            const processedCoupons = rawCoupons.map((coupon) => {
              return this.processCouponData(coupon);
            });
            
            console.log("处理后的券数据数量:", processedCoupons.length);
            
            this.setData({ 
              coupons: processedCoupons,
              isLoading: false,
            });
            
            // 计算统计数据
            this.calculateCouponStats(processedCoupons);
            
            // 应用过滤器
            this.applyFilter();
            resolve(processedCoupons);
          } else {
            const errorMsg = res.result?.message || "获取券失败";
            console.error("获取券失败:", errorMsg);
            reject(new Error(errorMsg));
          }
        },
        fail: (err) => {
          console.error("获取券网络错误:", err);
          this.setData({ isLoading: false });
          reject(err);
        },
      });
    });
  },

  /**
   * 计算券统计数据
   */
  calculateCouponStats: function (coupons) {
    const stats = {
      received: { pending: 0, redeemed: 0, expired: 0, total: 0 },
      sent: { total: 0 }
    };
    
    const userOpenid = this.data.userInfo._openid;
    const now = new Date().getTime();
    
    coupons.forEach(coupon => {
      if (!coupon) return;
      
      const isReceived = coupon.recipientId === userOpenid;
      const isSent = coupon.creatorId === userOpenid;
      
      if (isReceived && coupon.status !== 'draft') {
        stats.received.total++;
        
        // 判断券状态
        if (coupon.status === 'redeemed') {
          stats.received.redeemed++;
        } else {
          const expiryTime = coupon.expiryDate || coupon.expiresAt;
          const isExpired = expiryTime && new Date(expiryTime).getTime() <= now;
          
          if (isExpired || coupon.status === 'expired' || coupon.status === 'invalid') {
            stats.received.expired++;
          } else if (coupon.status === 'received' || coupon.status === 'pending_redeem') {
            stats.received.pending++;
          }
        }
      } else if (isSent && coupon.status !== 'draft') {
        stats.sent.total++;
      }
    });
    
    console.log("计算的统计数据:", stats);
    this.setData({ couponStats: stats });
  },

  /**
   * 工具函数：延迟等待
   */
  sleep: function (ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * 处理券数据，添加显示所需的字段
   */
  processCouponData: function (coupon) {
    if (!coupon || !this.data.userInfo) return coupon;
    
    const userOpenid = this.data.userInfo._openid;
    
    // 计算角色文本和样式
    let roleText = "";
    let roleClass = "";
    
    if (coupon.creatorId === userOpenid) {
      roleText = "我送出的";
      roleClass = "sent-by-me";
    } else if (coupon.recipientId === userOpenid) {
      roleText = "我收到的";
      roleClass = "received-by-me";
    }
    
    // 计算状态文本
    const statusText = this.getCouponStatusText(coupon);
    
    // 计算过期时间文本（对于我收到的券）
    const expiryText = this.formatDate(coupon.expiryDate || coupon.expiresAt);
    
    // 计算发送时间文本（对于我发送的券）
    const sentTimeText = this.formatSentTime(coupon.createTime);
    
    return {
      ...coupon,
      roleText,
      roleClass,
      statusText,
      expiryText,
      sentTimeText,
    };
  },

  /**
   * 新的过滤逻辑 - 修复过滤问题
   */
  applyFilter: function () {
    const { coupons, activeMainTab, activeSubTab, userInfo } = this.data;
    
    console.log("应用过滤器:", { activeMainTab, activeSubTab, couponsCount: coupons?.length });
    
    if (!userInfo || !Array.isArray(coupons)) {
      console.log("用户信息或券数据不存在，设置空列表");
      this.setData({ filteredCoupons: [] });
      return;
    }

    let filtered = [];
    const now = new Date().getTime();
    const userOpenid = userInfo._openid;

    try {
      // 首先按主Tab过滤
      if (activeMainTab === 'received') {
        console.log("过滤我收到的券");
        const receivedCoupons = coupons.filter(c => {
          const isReceived = c && c.recipientId === userOpenid && c.status !== 'draft';
          if (isReceived) {
            console.log("收到的券:", c.title, c.recipientId, userOpenid);
          }
          return isReceived;
        });
        
        console.log("收到的券数量:", receivedCoupons.length);
        
        // 然后按子Tab过滤
        filtered = receivedCoupons.filter(c => {
          if (!c) return false;
          
          const expiryTime = c.expiryDate || c.expiresAt;
          const isExpired = expiryTime && new Date(expiryTime).getTime() <= now;
          
          switch (activeSubTab) {
            case 'pending':
              return !isExpired &&
                     c.status !== 'redeemed' &&
                     c.status !== 'expired' &&
                     c.status !== 'invalid';
            case 'redeemed':
              return c.status === 'redeemed';
            case 'expired':
              // 修复：已兑现的券不应该出现在过期列表中
              // 只有未兑现且过期的券，或者明确标记为过期/失效的券才算过期
              return (isExpired && c.status !== 'redeemed') ||
                     c.status === 'expired' ||
                     c.status === 'invalid';
            default:
              return true;
          }
        });
      } else if (activeMainTab === 'sent') {
        console.log("过滤我发送的券");
        // 我发送的券，不区分状态，显示所有
        filtered = coupons.filter(c => {
          const isSent = c && c.creatorId === userOpenid && c.status !== 'draft';
          if (isSent) {
            console.log("发送的券:", c.title, c.creatorId, userOpenid);
          }
          return isSent;
        });
      }
      
      console.log(`过滤结果: ${activeMainTab}-${activeSubTab}`, filtered.length);
      console.log("过滤后的券:", filtered.map(c => ({ title: c.title, status: c.status })));
    } catch (error) {
      console.error("过滤券数据失败:", error);
      filtered = [];
    }
    
    this.setData({ filteredCoupons: filtered });
  },

  /**
   * 获取券状态文本
   */
  getCouponStatusText: function (coupon) {
    if (!coupon) return "未知";
    
    const now = new Date().getTime();
    const expiryTime = coupon.expiryDate || coupon.expiresAt;
    
    if (expiryTime && new Date(expiryTime).getTime() <= now && 
        coupon.status !== "redeemed" && coupon.status !== "cancelled") {
      return "已失效";
    }
    
    const statusMap = {
      draft: "草稿",
      received: "待兑现",
      pending_redeem: "申请兑现中",
      redeemed: "已兑现",
      cancelled: "已作废",
      expired: "已失效",
      invalid: "已失效",
    };
    
    return statusMap[coupon.status] || "未知状态";
  },

  /**
   * 格式化日期 - 用于过期时间
   */
  formatDate: function (dateString) {
    if (!dateString) return "";
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "";
      
      const now = new Date();
      const diffTime = date.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays < 0) {
        return "已过期";
      } else if (diffDays === 0) {
        return "今天到期";
      } else if (diffDays === 1) {
        return "明天到期";
      } else if (diffDays <= 7) {
        return `${diffDays}天后到期`;
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日到期`;
      }
    } catch (error) {
      return "";
    }
  },

  /**
   * 格式化发送时间 - 用于我发送的券
   */
  formatSentTime: function (dateString) {
    if (!dateString) return "";
    
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "";
      
      const now = new Date();
      const diffTime = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
      const diffMinutes = Math.floor(diffTime / (1000 * 60));
      
      if (diffMinutes < 1) {
        return "刚刚发送";
      } else if (diffMinutes < 60) {
        return `${diffMinutes}分钟前发送`;
      } else if (diffHours < 24) {
        return `${diffHours}小时前发送`;
      } else if (diffDays < 30) {
        return `${diffDays}天前发送`;
      } else {
        return `${date.getMonth() + 1}月${date.getDate()}日发送`;
      }
    } catch (error) {
      return "";
    }
  },

  /**
   * 点击券卡片
   */
  onCouponTap: function (e) {
    const couponId = e.currentTarget.dataset.id;
    console.log("点击券:", couponId);
    
    if (!couponId) {
      wx.showToast({
        title: "券信息异常",
        icon: "none",
      });
      return;
    }
    
    wx.navigateTo({
      url: `/pages/couponDetail/couponDetail?id=${couponId}`,
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    console.log("下拉刷新");
    this.loadCouponsWithRetry().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面卸载
   */
  onUnload: function () {
    const app = getApp();
    if (this.globalRefreshHandler) {
      app.removeEventListener('globalDataRefresh', this.globalRefreshHandler);
    }
    if (this.switchToSentTabHandler) {
      app.removeEventListener('switchToSentTab', this.switchToSentTabHandler);
    }
  },
});
