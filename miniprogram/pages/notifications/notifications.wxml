<!--notifications.wxml-->
<view class="page-container" style="background: {{themeGradient}};">
  
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-main">
      <view class="header-left" style="width: 350rpx; display: flex; box-sizing: border-box">
        <text class="page-title">消息通知</text>
        <text class="message-count">{{totalCount || 0}}条消息</text>
      </view>
      <view class="header-right" style="width: auto; box-sizing: border-box">
        <button 
          class="action-btn mark-all-btn {{unreadCount > 0 ? '' : 'disabled'}}" 
          bindtap="markAllAsRead"
         style="width: 139rpx; display: flex; box-sizing: border-box; left: 0rpx; top: 0rpx; height: 33rpx">
          <text class="btn-icon">💕</text>
          <text class="btn-text">全部已读</text>
        </button>
        <button 
          class="action-btn clear-btn" 
          bindtap="clearAllNotifications"
         style="width: 97rpx; display: flex; box-sizing: border-box; left: 0rpx; top: 0rpx; height: 33rpx">
          <text class="btn-icon">🧹</text>
          <text class="btn-text">清空</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tabs-container">
    <!-- 一级标签 -->
    <scroll-view scroll-x class="filter-tabs-scroll">
      <view class="filter-tabs">
        <view 
          wx:for="{{filterTabs}}" 
          wx:key="type"
          class="filter-tab {{currentFilter === item.type ? 'active' : ''}}"
          bindtap="switchFilter"
          data-filter="{{item.type}}"
        >
          <text class="tab-label">{{item.label}}</text>
          <view class="tab-count">{{item.count || 0}}</view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 二级标签 -->
    <view wx:if="{{currentMainTab && currentMainTab.hasSubTabs}}" class="sub-tabs-container">
      <scroll-view scroll-x class="sub-tabs-scroll">
        <view class="sub-tabs">
          <view 
            wx:for="{{currentMainTab.subTabs}}" 
            wx:key="type"
            class="sub-tab {{currentSubFilter === item.type ? 'active' : ''}}"
            bindtap="switchSubFilter"
            data-filter="{{item.type}}"
          >
            <text class="sub-tab-label">{{item.label}}</text>
            <view class="sub-tab-count">{{item.count || 0}}</view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-container">
    
    <!-- 加载状态 -->
    <view wx:if="{{isLoading && filteredNotifications.length === 0}}" class="loading-container">
      <view class="loading-animation">
        <view class="loading-heart">💌</view>
        <view class="loading-dots">
          <view class="dot dot1"></view>
          <view class="dot dot2"></view>
          <view class="dot dot3"></view>
        </view>
      </view>
      <text class="loading-text">正在加载消息...</text>
    </view>
    
    <!-- 空状态 -->
    <view wx:elif="{{filteredNotifications.length === 0}}" class="empty-container">
      <view class="empty-animation">
        <view class="empty-icon">
          <text wx:if="{{currentFilter === 'all'}}">📭</text>
          <text wx:elif="{{currentFilter === 'new_coupon'}}">🎫</text>
          <text wx:elif="{{currentFilter === 'redeem_request'}}">🔔</text>
          <text wx:elif="{{currentFilter === 'coupon_redeemed'}}">🎉</text>
          <text wx:elif="{{currentFilter === 'coupon_expiry'}}">⚠️</text>
          <text wx:else>📨</text>
        </view>
        <view class="empty-rings">
          <view class="ring ring1"></view>
          <view class="ring ring2"></view>
          <view class="ring ring3"></view>
        </view>
      </view>
      <text class="empty-title">暂无{{currentFilterLabel}}消息</text>
      <text class="empty-subtitle">当有新的{{currentFilterLabel}}消息时，会在这里显示</text>
    </view>
    
    <!-- 消息列表 -->
    <scroll-view wx:else scroll-y class="notifications-scroll">
      <view class="notifications-list">
        <view 
          wx:for="{{filteredNotifications}}" 
          wx:key="_id"
          class="notification-item-wrapper"
        >
          <view 
            class="notification-card {{!item.isRead ? 'unread' : ''}}"
            bindtap="handleNotificationTap"
            data-notification="{{item}}"
          >
            <!-- 消息主体 -->
            <view class="notification-main">
              <view class="notification-icon-container">
                <text class="notification-icon">
                  <text wx:if="{{item.type === 'new_coupon'}}">🎫</text>
                  <text wx:elif="{{item.type === 'redeem_request'}}">🔔</text>
                  <text wx:elif="{{item.type === 'coupon_redeemed'}}">🎉</text>
                  <text wx:elif="{{item.type === 'coupon_expiry'}}">⚠️</text>
                  <text wx:else>📨</text>
                </text>
                <view class="icon-glow"></view>
              </view>
              
              <view class="notification-content">
                <view class="notification-header">
                  <text class="notification-title">{{item.notificationTitle}}</text>
                  <text class="notification-time">{{item.formattedTime}}</text>
                </view>
                
                <text class="notification-message">{{item.notificationMessage}}</text>
                
                <view wx:if="{{item.couponInfo}}" class="coupon-preview">
                  <view class="coupon-info-row">
                    <text class="coupon-name-text">{{item.couponInfo.name}}</text>
                    <view class="coupon-status-tag {{item.couponStatusClass}}">
                      <text class="coupon-status-text">{{item.couponStatusText}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 未读标识 -->
            <view wx:if="{{!item.isRead}}" class="unread-indicator">
              <view class="unread-dot"></view>
            </view>
            
            <!-- 装饰元素 -->
            <view class="notification-decorations">
              <view class="decoration-heart heart1">💕</view>
              <view class="decoration-heart heart2">💖</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view 
        wx:if="{{hasMore && !isLoading}}" 
        class="load-more-container"
        bindtap="loadMore"
      >
        <view class="load-more-btn">
          <text class="load-more-icon">⬇️</text>
          <text class="load-more-text">加载更多</text>
        </view>
      </view>
      
      <!-- 底部加载状态 -->
      <view wx:if="{{isLoading && filteredNotifications.length > 0}}" class="bottom-loading">
        <view class="loading-dots">
          <view class="dot dot1"></view>
          <view class="dot dot2"></view>
          <view class="dot dot3"></view>
        </view>
        <text class="loading-text">{{loadingText}}</text>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="bottom-safe-area"></view>
    </scroll-view>
    
  </view>

  <!-- 下拉刷新提示 -->
  <view wx:if="{{isRefreshing}}" class="refresh-indicator">
    <view class="refresh-animation">
      <view class="refresh-heart">💌</view>
    </view>
    <text class="refresh-text">刷新中...</text>
  </view>
</view>

