// pages/index/index.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    coupleInfo: null,
    couponsFromPartner: [], // Coupons received from partner
    featuredCoupons: [], // Maybe some template/public coupons
    activeFilter: "partner", // 'partner', 'featured'
    isLoading: false,
    themeGradient: "linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 50%, #F0F8FF 100%)",
  },

  onLoad: function (options) {
    console.log("首页加载，参数:", options);
    this.initializePage();

    // 添加全局事件监听 - 修复问题4
    this.globalRefreshHandler = (data) => {
      console.log("首页接收到全局刷新事件:", data);
      this.refreshPageData();
    };
    app.addEventListener('globalDataRefresh', this.globalRefreshHandler);

    // 添加情侣信息刷新监听器
    this.coupleRefreshHandler = (data) => {
      console.log("首页接收到情侣信息刷新事件:", data);
      this.getCoupleInfo();
    };
    app.addEventListener('refreshCoupleInfo', this.coupleRefreshHandler);

    // 添加伴侣资料更新通知监听器
    this.partnerProfileUpdateHandler = (notificationData) => {
      console.log('首页收到伴侣资料更新通知:', notificationData);

      if (notificationData.type === 'profile_updated' && notificationData.data?.shouldRefreshCache) {
        console.log('伴侣更新了资料，清除本地缓存并重新获取情侣信息');

        // 清除情侣信息缓存
        wx.removeStorageSync('coupleInfo');
        app.globalData.coupleInfo = null;

        // 重新获取情侣信息
        this.getCoupleInfo();
      }
    };

    // 监听通知事件
    app.addEventListener('notificationReceived', this.partnerProfileUpdateHandler);
  },

  onShow: function () {
    console.log("首页显示");

    // 强制重新检查绑定状态 - 修复问题1
    this.forceCheckBindingStatus();

    // 检查未读消息数量，确保徽标实时更新
    if (app.globalData.userInfo) {
      app.checkUnreadMessages();
    }
  },

  /**
   * 强制检查绑定状态（修复编译后状态显示问题）
   */
  forceCheckBindingStatus: async function () {
    const app = getApp();
    
    try {
      console.log("强制重新检查绑定状态...");
      
      // 如果用户未登录，先尝试自动登录
      if (!app.globalData.userInfo) {
        console.log("用户未登录，执行自动登录");
        await this.performAutoLogin();
      }
      
      // 重新检查情侣绑定状态
      const result = await this.checkCoupleStatusAsync();
      console.log("绑定状态检查结果:", result);
      
      // 强制刷新页面数据
      this.refreshPageData();
      
    } catch (error) {
      console.error("强制检查绑定状态失败:", error);
      // 即使失败也要刷新页面数据
      this.refreshPageData();
    }
  },

  /**
   * 异步版本的自动登录
   */
  performAutoLogin: function () {
    return new Promise((resolve, reject) => {
      const app = getApp();
      
      app.performLogin((error, userInfo) => {
        if (error) {
          console.error("自动登录失败:", error);
          reject(error);
        } else {
          console.log("自动登录成功:", userInfo);
          resolve(userInfo);
        }
      });
    });
  },

  /**
   * 异步版本的情侣状态检查
   */
  checkCoupleStatusAsync: function () {
    return new Promise((resolve, reject) => {
      const app = getApp();
      
      app.checkCoupleStatus((error, result) => {
        if (error) {
          reject(error);
        } else {
          resolve(result);
        }
      });
    });
  },

  /**
   * 初始化页面
   */
  initializePage: function () {
    const app = getApp();
    this.setData({
      themeGradient: app.globalData.gradientBackground,
    });
    
    // 初始化模板数据
    this.initializeFeaturedCoupons();
    
    // 检查用户登录状态，如果未登录则自动登录
    if (!app.globalData.userInfo) {
      console.log("用户未登录，开始自动登录...");
      this.handleAutoLogin();
    } else {
      this.loadUserData();
    }
  },

  /**
   * 自动登录处理
   */
  handleAutoLogin: function () {
    const app = getApp();
    
    console.log("执行自动登录...");
    
    app.performLogin((error, userInfo) => {
      if (error) {
        console.error("自动登录失败:", error);
        // 登录失败时仍然刷新页面数据，显示未登录状态
        this.refreshPageData();
      } else {
        console.log("自动登录成功:", userInfo);
        // 登录成功后刷新页面数据
        this.refreshPageData();
        
        // 检查情侣绑定状态
        app.checkCoupleStatus((err, status) => {
          if (!err && status) {
            console.log("检查绑定状态完成");
            this.refreshPageData();
          }
        });
      }
    });
  },

  /**
   * 初始化创作灵感模板
   */
  initializeFeaturedCoupons: function () {
    const templates = [
      {
        id: 'template_1',
        title: '爱心早餐券',
        description: '为你准备一顿温馨的早餐，让美好的一天从甜蜜开始',
        couponType: 'promise',
        style: 'gradient',
        icon: '🍳',
        category: '日常关怀'
      },
      {
        id: 'template_2',
        title: '按摩放松券',
        description: '累了一天的你，让我为你按摩放松，缓解疲劳',
        couponType: 'promise',
        style: 'gradient',
        icon: '💆',
        category: '贴心服务'
      },
      {
        id: 'template_3',
        title: '家务代劳券',
        description: '今天的家务全包了！你只需要美美地休息就好',
        couponType: 'task',
        style: 'gradient',
        icon: '🧹',
        category: '家务分担'
      },
      {
        id: 'template_4',
        title: '电影约会券',
        description: '一起看一场浪漫的电影，享受属于我们的时光',
        couponType: 'reward',
        style: 'gradient',
        icon: '🎬',
        category: '浪漫约会'
      },
      {
        id: 'template_5',
        title: '购物陪伴券',
        description: '陪你逛街购物，做你最贴心的小跟班',
        couponType: 'promise',
        style: 'gradient',
        icon: '🛍️',
        category: '陪伴时光'
      },
      {
        id: 'template_6',
        title: '深夜谈心券',
        description: '深夜时分，听你分享心事，陪你度过每个失眠的夜晚',
        couponType: 'special',
        style: 'gradient',
        icon: '🌙',
        category: '情感交流'
      },
      {
        id: 'template_7',
        title: '撒娇特权券',
        description: '今天可以无限撒娇，我会满足你的所有小任性',
        couponType: 'special',
        style: 'gradient',
        icon: '🥺',
        category: '特殊权利'
      },
      {
        id: 'template_8',
        title: '道歉免责券',
        description: '犯了小错误？这张券让你免于责备，但要记得下次注意哦',
        couponType: 'apology',
        style: 'gradient',
        icon: '🙏',
        category: '宽容理解'
      },
      {
        id: 'template_9',
        title: '惊喜礼物券',
        description: '准备一份神秘的小礼物，给你意想不到的惊喜',
        couponType: 'reward',
        style: 'gradient',
        icon: '🎁',
        category: '惊喜时刻'
      },
      {
        id: 'template_10',
        title: '专属厨师券',
        description: '今天我是你的专属厨师，想吃什么尽管说',
        couponType: 'promise',
        style: 'gradient',
        icon: '👨‍🍳',
        category: '美食制作'
      }
    ];
    
    this.setData({
      featuredCoupons: templates
    });
  },

  /**
   * 加载用户数据
   */
  loadUserData: function () {
    const app = getApp();
    console.log("加载用户数据");
    
    this.setData({
      userInfo: app.globalData.userInfo,
      coupleInfo: app.globalData.coupleInfo,
    });
    
    // 如果已登录且已绑定，加载券数据
    if (app.globalData.userInfo && app.globalData.coupleInfo) {
      this.loadCouponsFromPartner();
    }
  },

  /**
   * 刷新页面数据
   */
  refreshPageData: function () {
    const app = getApp();
    console.log("刷新页面数据，用户信息:", app.globalData.userInfo);
    console.log("情侣信息:", app.globalData.coupleInfo);
    
    this.setData({
      userInfo: app.globalData.userInfo,
      coupleInfo: app.globalData.coupleInfo,
    });
    
    // 如果已登录且已绑定，加载券数据
    if (app.globalData.userInfo && app.globalData.coupleInfo) {
      this.loadCouponsFromPartner();
    }
  },

  /**
   * 加载来自伴侣的券
   */
  loadCouponsFromPartner: function () {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true });
    
    // 修复：使用正确的过滤参数来获取对方发给我的券
    wx.cloud.callFunction({
      name: "getCoupons",
      data: {
        filterType: "received", // 获取我收到的券（即对方发给我的）
        limit: 20,
        skip: 0,
      },
      success: (res) => {
        console.log("获取对方发给我的券成功:", res.result);
        
        if (res.result && res.result.success) {
          const allCoupons = res.result.data.coupons || [];
          console.log("对方发给我的券数据:", allCoupons.map(c => ({
            _id: c._id,
            name: c.name,
            creatorId: c.creatorId,
            recipientId: c.recipientId,
            status: c.status
          })));

          // 修复问题3：只显示待兑现、兑现中的券
          const activeCoupons = allCoupons.filter(coupon => {
            // 检查是否过期（但不包括已兑现的券）
            const now = new Date().getTime();
            const expiryTime = coupon.expiryDate || coupon.expiresAt;
            const isExpired = expiryTime && new Date(expiryTime).getTime() <= now;

            // 只显示待兑现和申请兑现中的券
            // 排除：已兑现、过期、失效、取消、草稿状态的券
            return (coupon.status === 'received' || coupon.status === 'pending_redeem') &&
                   !isExpired &&
                   coupon.status !== 'expired' &&
                   coupon.status !== 'invalid' &&
                   coupon.status !== 'cancelled' &&
                   coupon.status !== 'draft';
          });

          console.log("过滤后的活跃券数据（仅待兑现和兑现中）:", activeCoupons.map(c => ({
            _id: c._id,
            name: c.name,
            status: c.status,
            expiryDate: c.expiryDate
          })));

          this.setData({
            couponsFromPartner: activeCoupons,
            isLoading: false,
          });
        } else {
          console.error("获取对方发给我的券失败:", res.result);
          this.setData({
            couponsFromPartner: [],
            isLoading: false,
          });
        }
      },
      fail: (err) => {
        console.error("获取对方发给我的券失败:", err);
        this.setData({
          couponsFromPartner: [],
          isLoading: false,
        });
      },
    });
  },

  /**
   * 切换筛选标签
   */
  switchFilter: function (e) {
    const filter = e.currentTarget.dataset.filter;
    if (filter === this.data.activeFilter) return;
    
    console.log("切换筛选:", filter);
    this.setData({ activeFilter: filter });
    
    // 如果切换到伴侣券，重新加载数据
    if (filter === "partner") {
      this.loadCouponsFromPartner();
    }
  },

  /**
   * 点击券卡片
   */
  onCouponTap: function (e) {
    console.log("券卡片点击事件:", e);
    
    // 从dataset中获取券ID
    let couponId = null;
    
    if (e.currentTarget && e.currentTarget.dataset && e.currentTarget.dataset.id) {
      couponId = e.currentTarget.dataset.id;
    }
    
    if (!couponId) {
      console.error("券ID不存在", e);
      wx.showToast({
        title: "券信息错误",
        icon: "none",
      });
      return;
    }
    
    console.log("跳转到券详情:", couponId);
    
    wx.navigateTo({
      url: `/pages/couponDetail/couponDetail?id=${couponId}`,
      fail: (err) => {
        console.error("跳转到券详情失败:", err);
        wx.showToast({
          title: "跳转失败",
          icon: "none",
        });
      },
    });
  },

  /**
   * 使用模板
   */
  useTemplate: function (e) {
    const template = e.currentTarget.dataset.template;
    
    if (!template) {
      console.error("模板数据不存在");
      return;
    }
    
    console.log("使用模板:", template);
    
    // 检查登录状态
    if (!this.data.userInfo) {
      wx.showModal({
        title: "请先登录",
        content: "使用模板需要先登录账号",
        confirmText: "去登录",
        success: (res) => {
          if (res.confirm) {
            this.handleAutoLogin();
          }
        },
      });
      return;
    }
    
    // 跳转到创建券页面，并传递模板数据
    const templateData = encodeURIComponent(JSON.stringify({
      title: template.title,
      description: template.description,
      couponType: template.couponType,
      icon: template.icon,
      style: template.style
    }));
    
    wx.navigateTo({
      url: `/pages/createCoupon/createCoupon?template=${templateData}`,
      fail: (err) => {
        console.error("跳转到创建券页面失败:", err);
        wx.showToast({
          title: "跳转失败",
          icon: "none",
        });
      },
    });
  },

  /**
   * 跳转到创建券页面
   */
  goToCreateCoupon: function () {
    const app = getApp();
    
    if (!app.globalData.userInfo) {
      this.handleAutoLogin();
      return;
    }
    
    if (!app.globalData.coupleInfo) {
      wx.showModal({
        title: "需要绑定情侣",
        content: "发券需要先绑定你的另一半",
        confirmText: "去绑定",
        success: (res) => {
          if (res.confirm) {
            this.goToBinding();
          }
        },
      });
      return;
    }
    
    wx.navigateTo({
      url: "/pages/createCoupon/createCoupon",
    });
  },

  /**
   * 跳转到绑定页面
   */
  goToBinding: function () {
    const app = getApp();
    
    if (!app.globalData.userInfo) {
      this.handleAutoLogin();
      return;
    }
    
    wx.navigateTo({
      url: "/pages/binding/binding",
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage: function () {
    return {
      title: "情侣信誉券 - 用券传递爱意",
      path: "/pages/index/index",
      imageUrl: "/images/share-cover.jpg",
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: "情侣信誉券 - 让每一份承诺都有迹可循",
      imageUrl: "/images/share-cover.jpg",
    };
  },

  /**
   * 检查绑定状态变化（保留原有逻辑作为备用）
   */
  checkBindingStatusChange: function () {
    const app = getApp();
    const currentCoupleInfo = this.data.coupleInfo;
    const globalCoupleInfo = app.globalData.coupleInfo;
    
    // 如果全局状态和页面状态不一致，说明状态有变化
    const hasStatusChange = (!currentCoupleInfo && globalCoupleInfo) || 
                           (currentCoupleInfo && !globalCoupleInfo) ||
                           (currentCoupleInfo && globalCoupleInfo && currentCoupleInfo.partnerId !== globalCoupleInfo.partnerId);
    
    if (hasStatusChange) {
      console.log("检测到绑定状态变化，重新检查状态");
      
      // 重新检查情侣状态
      app.checkCoupleStatus((err, data) => {
        if (!err && data) {
          console.log("重新检查状态结果:", data);
          this.refreshPageData();
        }
      });
    }
  },

  onUnload: function () {
    // 移除全局事件监听
    const app = getApp();
    if (this.globalRefreshHandler) {
      app.removeEventListener('globalDataRefresh', this.globalRefreshHandler);
    }
    if (this.coupleRefreshHandler) {
      app.removeEventListener('refreshCoupleInfo', this.coupleRefreshHandler);
    }
    if (this.partnerProfileUpdateHandler) {
      app.removeEventListener('notificationReceived', this.partnerProfileUpdateHandler);
    }
  },

  /**
   * 背景图片加载成功事件
   */
  onBackgroundImageLoad: function (e) {
    console.log("背景图片加载成功:", e);
  },

  /**
   * 背景图片加载失败事件
   */
  onBackgroundImageError: function (e) {
    console.error("背景图片加载失败:", e);
    
    // 尝试使用备用方案
    this.setData({
      useDefaultBackground: true
    });
  },
});
