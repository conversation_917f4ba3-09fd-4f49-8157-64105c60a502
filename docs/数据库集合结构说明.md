# 数据库集合结构说明

## 🗃️ 集合列表和数据结构

### 1. users - 用户信息集合

```javascript
{
  _id: "用户唯一ID",
  _openid: "微信openid",
  nickName: "用户昵称",
  avatarUrl: "头像URL",
  gender: 1, // 0:未知, 1:男, 2:女
  city: "城市",
  province: "省份", 
  country: "国家",
  createdTime: "2024-01-01T00:00:00.000Z",
  lastLoginTime: "2024-01-01T00:00:00.000Z",
  partnerId: "情侣对象的用户ID（绑定后才有）",
  status: "active" // active, inactive
}
```

### 2. couples - 情侣绑定关系集合

```javascript
{
  _id: "关系唯一ID",
  _openid: "创建者openid",
  userId1: "用户1的ID",
  userId2: "用户2的ID", 
  inviteCode: "邀请码",
  status: "active", // pending, active, inactive
  createdTime: "2024-01-01T00:00:00.000Z",
  bindTime: "绑定确认时间",
  relationshipStartDate: "恋爱开始日期",
  anniversary: "纪念日",
  coupleNickname: "情侣昵称"
}
```

### 3. coupons - 信任券集合

```javascript
{
  _id: "券唯一ID",
  _openid: "创建者openid",
  coupleId: "所属情侣关系ID",
  title: "券标题",
  description: "券描述", 
  value: "券面值/内容",
  type: "promise", // promise, task, reward, apology
  status: "active", // active, redeemed, expired, cancelled
  createdBy: "创建者用户ID",
  recipientId: "接收者用户ID",
  createdTime: "2024-01-01T00:00:00.000Z",
  expiryDate: "过期时间",
  redeemedTime: "兑换时间",
  redemptionNote: "兑换备注",
  priority: "normal", // low, normal, high
  tags: ["浪漫", "承诺"], // 标签数组
  attachments: ["图片URL1", "图片URL2"] // 附件数组
}
```

### 4. notifications - 通知消息集合

```javascript
{
  _id: "通知唯一ID",
  _openid: "接收者openid",
  userId: "接收者用户ID",
  type: "coupon_received", // coupon_received, coupon_redeemed, partner_bound, etc.
  title: "通知标题",
  content: "通知内容",
  data: { // 相关数据
    couponId: "相关券ID",
    partnerId: "相关用户ID"
  },
  status: "unread", // unread, read
  createdTime: "2024-01-01T00:00:00.000Z",
  readTime: "阅读时间"
}
```

## 🔐 权限设置说明

### 为什么都选择"仅创建者可读写"？

1. **数据安全**：用户只能访问自己创建的数据
2. **隐私保护**：情侣信息、券信息都是私密数据  
3. **云函数控制**：跨用户数据访问通过云函数实现

### 云函数如何访问数据？

云函数运行在服务端，具有管理员权限，可以：
- 读取任何集合的数据
- 跨用户操作数据
- 实现复杂的业务逻辑

### 数据访问流程

```
用户操作 → 小程序前端 → 调用云函数 → 云函数操作数据库 → 返回结果
```

## 📈 索引建议

创建集合后，建议添加以下索引以提高查询性能：

### users 集合
- `_openid` (已自动创建)
- `partnerId` 

### couples 集合  
- `userId1, userId2` (复合索引)
- `inviteCode` (唯一索引)
- `status`

### coupons 集合
- `coupleId, status` (复合索引)  
- `createdBy`
- `recipientId`
- `expiryDate`

### notifications 集合
- `userId, status` (复合索引)
- `createdTime`

## 🚀 下一步操作

1. ✅ 创建4个基础集合
2. 🔄 测试云函数数据库操作
3. 📊 根据需要添加索引
4. 🔧 优化查询性能 