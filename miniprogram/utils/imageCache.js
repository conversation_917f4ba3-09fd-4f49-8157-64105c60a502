/**
 * 图片缓存工具类
 * 用于优化图片加载性能
 */
class ImageCache {
  constructor() {
    this.cache = new Map();
    this.maxCacheSize = 50; // 最大缓存数量
  }

  /**
   * 获取缓存的图片路径
   * @param {string} imageUrl 图片URL
   * @returns {Promise<string>} 本地图片路径
   */
  async getImage(imageUrl) {
    if (!imageUrl) {
      return '/images/default-avatar.png';
    }

    // 检查是否已缓存
    if (this.cache.has(imageUrl)) {
      const cachedPath = this.cache.get(imageUrl);
      try {
        // 验证缓存文件是否还存在
        await this.checkFileExists(cachedPath);
        return cachedPath;
      } catch (error) {
        // 缓存文件不存在，删除缓存记录
        this.cache.delete(imageUrl);
      }
    }

    // 下载并缓存图片
    try {
      const localPath = await this.downloadImage(imageUrl);
      this.addToCache(imageUrl, localPath);
      return localPath;
    } catch (error) {
      console.error('图片下载失败:', error);
      return '/images/default-avatar.png';
    }
  }

  /**
   * 下载图片到本地
   * @param {string} imageUrl 图片URL
   * @returns {Promise<string>} 本地图片路径
   */
  downloadImage(imageUrl) {
    return new Promise((resolve, reject) => {
      wx.downloadFile({
        url: imageUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.tempFilePath);
          } else {
            reject(new Error(`下载失败: ${res.statusCode}`));
          }
        },
        fail: reject
      });
    });
  }

  /**
   * 检查文件是否存在
   * @param {string} filePath 文件路径
   * @returns {Promise<void>}
   */
  checkFileExists(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileInfo({
        filePath: filePath,
        success: resolve,
        fail: reject
      });
    });
  }

  /**
   * 添加到缓存
   * @param {string} url 图片URL
   * @param {string} localPath 本地路径
   */
  addToCache(url, localPath) {
    // 如果缓存已满，删除最早的缓存
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(url, localPath);
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      urls: Array.from(this.cache.keys())
    };
  }
}

// 创建全局实例
const imageCache = new ImageCache();

module.exports = imageCache; 