<!--settings.wxml-->
<view class="container">
  <!-- 通知设置 -->
  <view class="settings-group">
    <text class="group-title">通知设置</text>
    
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">新券提醒</text>
        <text class="setting-desc">收到新信任券时推送消息</text>
      </view>
      <switch 
        checked="{{notificationSettings.newCoupon}}"
        bindchange="onNotificationChange"
        data-type="newCoupon"
        color="#FF69B4"
      />
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">接收确认提醒</text>
        <text class="setting-desc">对方确认接收券时推送消息</text>
      </view>
      <switch 
        checked="{{notificationSettings.couponReceived}}"
        bindchange="onNotificationChange"
        data-type="couponReceived"
        color="#FF69B4"
      />
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">兑现申请提醒</text>
        <text class="setting-desc">收到兑现申请时推送消息</text>
      </view>
      <switch 
        checked="{{notificationSettings.redeemRequest}}"
        bindchange="onNotificationChange"
        data-type="redeemRequest"
        color="#FF69B4"
      />
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">兑现完成提醒</text>
        <text class="setting-desc">券兑现完成时推送消息</text>
      </view>
      <switch 
        checked="{{notificationSettings.couponRedeemed}}"
        bindchange="onNotificationChange"
        data-type="couponRedeemed"
        color="#FF69B4"
      />
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">过期提醒</text>
        <text class="setting-desc">券即将过期时推送消息</text>
      </view>
      <switch 
        checked="{{notificationSettings.expiryReminder}}"
        bindchange="onNotificationChange"
        data-type="expiryReminder"
        color="#FF69B4"
      />
    </view>

    <view class="setting-item action-item" bindtap="manageSubscribeMessages">
      <view class="setting-info">
        <text class="setting-label">订阅消息管理</text>
        <text class="setting-desc">管理微信订阅消息权限</text>
      </view>
      <text class="setting-arrow">→</text>
    </view>
  </view>

  <!-- 隐私设置 -->
  <view class="settings-group">
    <text class="group-title">隐私设置</text>
    
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">数据分析</text>
        <text class="setting-desc">允许收集使用数据改进服务</text>
      </view>
      <switch 
        checked="{{privacySettings.analytics}}"
        bindchange="onPrivacyChange"
        data-type="analytics"
        color="#FF69B4"
      />
    </view>

    <view class="setting-item action-item" bindtap="viewPrivacyPolicy">
      <view class="setting-info">
        <text class="setting-label">隐私政策</text>
        <text class="setting-desc">查看我们的隐私保护条款</text>
      </view>
      <text class="setting-arrow">→</text>
    </view>

    <view class="setting-item action-item" bindtap="viewUserAgreement">
      <view class="setting-info">
        <text class="setting-label">用户协议</text>
        <text class="setting-desc">查看服务条款和用户协议</text>
      </view>
      <text class="setting-arrow">→</text>
    </view>
  </view>

  <!-- 应用设置 -->
  <view class="settings-group">
    <text class="group-title">应用设置</text>
    
    <view class="setting-item action-item" bindtap="clearCache">
      <view class="setting-info">
        <text class="setting-label">清理缓存</text>
        <text class="setting-desc">当前缓存: {{cacheSize}}MB</text>
      </view>
      <text class="setting-arrow">→</text>
    </view>

    <view class="setting-item action-item" bindtap="checkUpdate">
      <view class="setting-info">
        <text class="setting-label">检查更新</text>
        <text class="setting-desc">当前版本: {{appVersion}}</text>
      </view>
      <text class="setting-arrow">→</text>
    </view>

    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">自动更新</text>
        <text class="setting-desc">发现新版本时自动更新</text>
      </view>
      <switch 
        checked="{{appSettings.autoUpdate}}"
        bindchange="onAppSettingsChange"
        data-type="autoUpdate"
        color="#FF69B4"
      />
    </view>
  </view>

  <!-- 数据管理 -->
  <view class="settings-group">
    <text class="group-title">数据管理</text>
    
    <view class="setting-item action-item" bindtap="exportData">
      <view class="setting-info">
        <text class="setting-label">导出我的数据</text>
        <text class="setting-desc">下载个人数据备份</text>
      </view>
      <text class="setting-arrow">→</text>
    </view>

    <view class="setting-item action-item" bindtap="deleteAccount">
      <view class="setting-info">
        <text class="setting-label">注销账号</text>
        <text class="setting-desc">永久删除账号和所有数据</text>
      </view>
      <text class="setting-arrow">→</text>
    </view>
  </view>

  <!-- 账号安全 -->
  <view class="settings-group">
    <text class="group-title">账号安全</text>
    
    <view class="setting-item action-item" bindtap="bindPhone">
      <view class="setting-info">
        <text class="setting-label">绑定手机号</text>
        <text class="setting-desc">{{phoneNumber || '未绑定，建议绑定以保障账号安全'}}</text>
      </view>
      <text class="setting-arrow">→</text>
    </view>

    <view class="setting-item action-item" bindtap="viewLoginHistory">
      <view class="setting-info">
        <text class="setting-label">登录记录</text>
        <text class="setting-desc">查看最近登录设备和时间</text>
      </view>
      <text class="setting-arrow">→</text>
    </view>
  </view>

  <!-- 确认弹窗 -->
  <view wx:if="{{showConfirmModal}}" class="modal-overlay" bindtap="hideConfirmModal">
    <view class="modal-content" catchtap="preventClose">
      <view class="modal-header">
        <text class="modal-title">{{confirmModal.title}}</text>
      </view>
      <view class="modal-body">
        <text class="modal-message">{{confirmModal.message}}</text>
      </view>
      <view class="modal-actions">
        <button class="cancel-btn" bindtap="hideConfirmModal">取消</button>
        <button class="confirm-btn" bindtap="confirmAction">确认</button>
      </view>
    </view>
  </view>

  <!-- 隐私政策弹窗 -->
  <view wx:if="{{showPrivacyModal}}" class="modal-overlay" bindtap="hidePrivacyModal">
    <view class="modal-content large" catchtap="preventClose">
      <view class="modal-header">
        <text class="modal-title">隐私政策</text>
        <button class="close-btn" bindtap="hidePrivacyModal">✕</button>
      </view>
      <scroll-view class="modal-scroll" scroll-y="true">
        <view class="privacy-content">
          <text class="privacy-section-title">1. 信息收集与使用</text>
          <text class="privacy-text">我们收集您提供的个人信息用于提供情侣信任券服务，包括但不限于头像、昵称等基本信息。</text>
          
          <text class="privacy-section-title">2. 信息保护</text>
          <text class="privacy-text">我们采用行业标准的安全措施保护您的个人信息，确保数据安全。</text>
          
          <text class="privacy-section-title">3. 信息共享</text>
          <text class="privacy-text">除法律法规要求外，我们不会向第三方分享您的个人信息。</text>
          
          <text class="privacy-section-title">4. 数据存储</text>
          <text class="privacy-text">您的数据存储在腾讯云服务器中，享受企业级安全保障。</text>
          
          <text class="privacy-section-title">5. 联系我们</text>
          <text class="privacy-text">如有隐私相关问题，请通过应用内反馈功能联系我们。</text>
        </view>
      </scroll-view>
      <view class="modal-actions">
        <button class="primary-btn" bindtap="hidePrivacyModal">我知道了</button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>
</view>

