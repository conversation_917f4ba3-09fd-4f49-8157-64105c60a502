// app.js
App({
  onLaunch: function () {
    // Initialize Cloud
    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
    } else {
      wx.cloud.init({
        env: "cloudbase-9grne1oy03cbd581", // 请替换为你的云开发环境 ID
        traceUser: true,
      });
    }

    this.globalData = {
      userInfo: null, // 不再从缓存读取，每次启动时重新验证
      coupleInfo: null, // 不再从缓存读取，每次启动时重新验证
      // Primary theme color from previous_code
      themeColor: "#FF69B4",
      // Gradient background style from previous_code's container
      gradientBackground:
        "linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 50%, #F0F8FF 100%)",
      // 未读消息数量
      unreadCount: 0,
      // 全局事件监听器（修复绑定后页面刷新问题）
      eventListeners: {},
    };

    // Check login status
    this.checkLogin();
  },

  onShow: function () {
    console.log("应用恢复显示");
    
    // 应用恢复时检查未读消息数量
    if (this.globalData.userInfo) {
      // 延迟一点时间确保页面状态稳定
      setTimeout(() => {
        this.checkUnreadMessages();
      }, 500);
    }
  },

  checkLogin: function () {
    // 从缓存中尝试读取用户信息，但需要验证
    const cachedUserInfo = wx.getStorageSync("userInfo");
    
    if (!cachedUserInfo || !cachedUserInfo._id) {
      // 没有缓存的用户信息，需要登录
      console.log("User not logged in or no user info in cache.");
      this.globalData.userInfo = null;
      this.globalData.coupleInfo = null;
    } else {
      // 有缓存信息，先设置到全局，但需要验证
      console.log("Found cached user info, will verify:", cachedUserInfo);
      this.globalData.userInfo = cachedUserInfo;
      
      // 验证用户信息和情侣状态
      this.verifyUserAndCoupleStatus();
    }
  },

  /**
   * 验证用户信息和情侣状态（每次启动时调用）
   */
  verifyUserAndCoupleStatus: function () {
    console.log("开始验证用户信息和情侣状态...");
    
    // 先尝试验证用户信息
    wx.cloud.callFunction({
      name: "checkCoupleStatus", // 这个云函数会同时验证用户和情侣状态
      data: {},
      success: (res) => {
        console.log("验证结果:", res);
        
        if (res.result && res.result.success) {
          const data = res.result.data;
          
          // 更新用户信息
          if (data.userInfo) {
            this.globalData.userInfo = data.userInfo;
            wx.setStorageSync("userInfo", data.userInfo);
          }
          
          // 更新情侣信息
          if (data.isBound && data.coupleInfo) {
            console.log("验证确认：用户已绑定情侣");
            const formattedCoupleInfo = this.formatCoupleInfo(
              data.coupleInfo,
              data.userInfo
            );
            this.globalData.coupleInfo = formattedCoupleInfo;
            wx.setStorageSync("coupleInfo", formattedCoupleInfo);
          } else {
            console.log("验证确认：用户未绑定情侣");
            this.globalData.coupleInfo = null;
            wx.removeStorageSync("coupleInfo");
          }
          
          // 验证完成后检查未读消息
          this.checkUnreadMessages();
        } else {
          console.log("验证失败，清除缓存信息");
          // 验证失败，清除缓存
          this.globalData.userInfo = null;
          this.globalData.coupleInfo = null;
          wx.removeStorageSync("userInfo");
          wx.removeStorageSync("coupleInfo");
        }
      },
      fail: (err) => {
        console.error("验证用户状态失败:", err);
        // 网络错误等情况，保留缓存信息，但标记需要重新验证
        // this.globalData.needsVerification = true;
      },
    });
  },

  // Perform login with auto-generated profile
  performLogin: function (callback) {
    wx.showLoading({ title: "加载中..." });
    
    // 生成随机昵称和默认头像
    const randomProfile = this.generateRandomProfile();
    
    wx.cloud.callFunction({
      name: "loginOrCreateUser",
      data: {
        autoProfile: randomProfile, // 传递自动生成的资料
      },
      success: (res) => {
        wx.hideLoading();
        if (res.result && res.result.success) {
          const userInfo = res.result.data.userInfo;
          this.globalData.userInfo = userInfo;
          wx.setStorageSync("userInfo", userInfo);
          console.log("Login successful, user info:", userInfo);
          
          // 显示欢迎信息
          if (userInfo.isNewUser) {
            wx.showToast({
              title: `欢迎 ${userInfo.nickName}！`,
              icon: "success",
              duration: 2000,
            });
          }
          
          if (typeof callback === "function") {
            callback(null, userInfo);
          }
          // Fetch couple info after successful login
          this.checkCoupleStatus();
          // Check unread messages
          this.checkUnreadMessages();
        } else {
          console.error("Login failed:", res.result.message);
          wx.showToast({
            title: res.result.message || "登录失败",
            icon: "none",
          });
          if (typeof callback === "function") {
            callback(new Error(res.result.message || "登录失败"));
          }
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error("Login cloud function call failed:", err);
        wx.showToast({ title: "登录请求失败", icon: "none" });
        if (typeof callback === "function") {
          callback(err);
        }
      },
    });
  },

  // 生成随机用户资料
  generateRandomProfile: function () {
    const coupleNicknames = [
      "甜心宝贝",
      "小可爱",
      "心动时刻",
      "温柔如你",
      "星光点点",
      "微笑天使",
      "暖心小屋",
      "柔情似水",
      "浪漫时光",
      "甜蜜相伴",
      "心有灵犀",
      "温暖怀抱",
      "美好时光",
      "幸福时刻",
      "甜蜜恋人",
      "心动不已",
      "温柔岁月",
      "浪漫情怀",
      "甜美如初",
      "幸福相守",
      "心花怒放",
      "温馨时光",
      "甜蜜约定",
      "美好相遇",
      "幸福满满",
    ];
    
    const defaultAvatars = [
      "/images/avatars/couple1.svg",
      "/images/avatars/couple2.svg",
      "/images/avatars/couple3.svg",
      "/images/avatars/couple4.svg",
      "/images/avatars/couple5.svg",
      "/images/avatars/heart1.svg",
      "/images/avatars/heart2.svg",
      "/images/avatars/heart3.svg",
    ];
    
    const randomNickname =
      coupleNicknames[Math.floor(Math.random() * coupleNicknames.length)];
    const randomAvatar =
      defaultAvatars[Math.floor(Math.random() * defaultAvatars.length)];
    
    return {
      nickName: randomNickname,
      avatarUrl: randomAvatar,
    };
  },

  // Get user profile (nickname, avatar)
  getUserProfile: function (callback) {
    console.log("开始获取用户资料授权");

    wx.getUserProfile({
      desc: "用于完善会员资料和情侣信息展示",
      success: (res) => {
        console.log("用户授权成功，获取到的用户信息:", res.userInfo);

        wx.showLoading({ title: "更新资料中..." });
        wx.cloud.callFunction({
          name: "updateUserProfile",
          data: {
            nickName: res.userInfo.nickName,
            avatarUrl: res.userInfo.avatarUrl,
            gender: res.userInfo.gender, // 0:未知, 1:男, 2:女
            city: res.userInfo.city,
            province: res.userInfo.province,
            country: res.userInfo.country,
          },
          success: (updateRes) => {
            console.log("云函数调用成功，返回结果:", updateRes);
            wx.hideLoading();

            if (updateRes.result && updateRes.result.success) {
              const updatedUserInfo = updateRes.result.data.userInfo;
              this.globalData.userInfo = updatedUserInfo;
              wx.setStorageSync("userInfo", updatedUserInfo);
              console.log("用户资料更新成功:", updatedUserInfo);

              if (typeof callback === "function") {
                callback(null, updatedUserInfo);
              }
            } else {
              console.error("资料更新失败:", updateRes.result);

              let errorMsg = "资料更新失败";
              if (
                updateRes.result &&
                updateRes.result.code === "USER_NOT_FOUND"
              ) {
                errorMsg = "用户信息不存在，请重新登录";
                // 清除本地用户信息，触发重新登录
                this.globalData.userInfo = null;
                wx.removeStorageSync("userInfo");
              } else if (updateRes.result && updateRes.result.message) {
                errorMsg = updateRes.result.message;
              }

              wx.showToast({
                title: errorMsg,
                icon: "none",
                duration: 2000,
              });

              if (typeof callback === "function") {
                callback(new Error(errorMsg));
              }
            }
          },
          fail: (err) => {
            console.error("云函数调用失败:", err);
            wx.hideLoading();
            wx.showToast({
              title: "网络请求失败，请检查网络连接",
              icon: "none",
              duration: 2000,
            });
            if (typeof callback === "function") {
              callback(err);
            }
          },
        });
      },
      fail: (err) => {
        console.log("用户拒绝授权或授权失败:", err);

        let errorMsg = "获取资料失败";
        if (err.errMsg && err.errMsg.includes("auth deny")) {
          errorMsg = "您拒绝了授权，无法获取微信资料";
        } else if (err.errMsg && err.errMsg.includes("auth cancel")) {
          errorMsg = "您取消了授权";
        }

        wx.showToast({
          title: errorMsg,
          icon: "none",
          duration: 2000,
        });

        if (typeof callback === "function") {
          callback(new Error(errorMsg));
        }
      },
    });
  },

  // Check couple status
  checkCoupleStatus: function (callback) {
    console.log("开始检查情侣绑定状态...");
    console.log("当前用户信息:", this.globalData.userInfo);
    
    if (!this.globalData.userInfo) {
      console.log("用户未登录，返回未绑定状态");
      if (typeof callback === "function") callback(null, { isBound: false });
      return;
    }
    
    wx.cloud.callFunction({
      name: "checkCoupleStatus",
      data: {},
      success: (res) => {
        console.log("checkCoupleStatus 云函数返回结果:", res);
        
        if (res.result && res.result.success) {
          const data = res.result.data;
          console.log("解析后的数据:", data);
          
          if (data.isBound && data.coupleInfo) {
            console.log("用户已绑定情侣，更新全局状态:", data.coupleInfo);
            
            // 格式化情侣信息
            const formattedCoupleInfo = this.formatCoupleInfo(
              data.coupleInfo,
              data.userInfo
            );
            
            this.globalData.coupleInfo = formattedCoupleInfo;
            wx.setStorageSync("coupleInfo", formattedCoupleInfo);
          } else {
            console.log("用户未绑定情侣，清空全局状态");
            this.globalData.coupleInfo = null;
            wx.removeStorageSync("coupleInfo");
          }
          
          if (typeof callback === "function") callback(null, data);
        } else {
          console.error("checkCoupleStatus 云函数执行失败:", res.result);
          this.globalData.coupleInfo = null;
          wx.removeStorageSync("coupleInfo");
          if (typeof callback === "function")
            callback(null, { isBound: false });
        }
      },
      fail: (err) => {
        console.error("检查情侣状态云函数调用失败:", err);
        this.globalData.coupleInfo = null;
        wx.removeStorageSync("coupleInfo");
        if (typeof callback === "function") callback(err);
      },
    });
  },

  navigateToWithLoginCheck: function (url) {
    if (!this.globalData.userInfo) {
      wx.showModal({
        title: "请先登录",
        content: "登录后才能访问该页面。",
        confirmText: "去登录",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            // Assuming index page handles login UI
            wx.switchTab({
              url: "/pages/index/index",
              success: () => {
                // Optionally, pass a redirect or flag to index page
              },
            });
          }
        },
      });
    } else {
      wx.navigateTo({ url });
    }
  },

  /**
   * 格式化情侣信息数据
   */
  formatCoupleInfo: function (coupleData, userData) {
    if (!coupleData || !coupleData.partner) {
      return null;
    }
    
    const partner = coupleData.partner;
    let togetherDays = 0;
    
    // 计算相恋天数
    if (coupleData.relationshipStartDate) {
      const startDate = new Date(coupleData.relationshipStartDate);
      const today = new Date();
      const timeDiff = today.getTime() - startDate.getTime();
      togetherDays = Math.max(0, Math.floor(timeDiff / (1000 * 3600 * 24)));
    }
    
    return {
      partnerId: partner._id,
      partnerNickname: partner.nickName || "用户",
      partnerAvatar: partner.avatarUrl || "/images/default-avatar.png",
      relationshipStartDate: coupleData.relationshipStartDate,
      togetherDays: togetherDays,
      status: coupleData.status || "active",
    };
  },

  isCoupleBound: function () {
    return this.globalData.coupleInfo && this.globalData.coupleInfo.partnerId;
  },

  /**
   * 检查未读消息数量
   */
  checkUnreadMessages: function () {
    if (!this.globalData.userInfo) {
      console.log("用户未登录，跳过检查未读消息");
      return;
    }

    console.log("开始检查未读消息数量...");

    // 增加延迟重试机制 - 修复问题3
    this.checkUnreadMessagesWithRetry();

    // 同时检查是否有伴侣资料更新的通知
    this.checkPartnerProfileUpdateNotifications();

    // 修复问题2：检查是否有解绑状态同步通知
    this.checkUnbindSyncNotifications();
  },

  /**
   * 带重试机制的未读消息检查
   */
  checkUnreadMessagesWithRetry: function (retryCount = 0) {
    const maxRetries = 3;

    wx.cloud.callFunction({
      name: "getNotificationCount",
      data: {},
      success: (res) => {
        console.log("获取未读消息数量结果:", res);

        if (res.result && res.result.success) {
          const unreadCount = res.result.data.unreadCount || 0;
          console.log("未读消息数量:", unreadCount);

          this.globalData.unreadCount = unreadCount;
          this.updateMessageBadge(unreadCount);

          // 延迟再次检查，确保徽章更新成功
          if (retryCount === 0) {
            setTimeout(() => {
              this.checkUnreadMessagesWithRetry(1);
            }, 1000);
          } else if (retryCount === 1) {
            setTimeout(() => {
              this.checkUnreadMessagesWithRetry(2);
            }, 2000);
          }
        } else {
          console.error("获取未读消息数量失败:", res.result);
          // 失败时也要清除可能存在的错误徽章
          this.updateMessageBadge(0);
        }
      },
      fail: (err) => {
        console.error("检查未读消息失败:", err);

        if (retryCount < maxRetries) {
          setTimeout(() => {
            this.checkUnreadMessagesWithRetry(retryCount + 1);
          }, 1000 * (retryCount + 1));
        } else {
          // 网络错误时也要清除可能存在的错误徽章
          this.updateMessageBadge(0);
        }
      },
    });
  },

  /**
   * 检查伴侣资料更新通知
   */
  checkPartnerProfileUpdateNotifications: function () {
    if (!this.globalData.userInfo) {
      return;
    }

    console.log("检查伴侣资料更新通知...");

    wx.cloud.callFunction({
      name: "getNotifications",
      data: {
        type: 'profile_updated',
        unreadOnly: true,
        pageSize: 5
      },
      success: (res) => {
        if (res.result && res.result.success && res.result.data.notifications) {
          const profileUpdateNotifications = res.result.data.notifications;

          profileUpdateNotifications.forEach(notification => {
            if (notification.data && notification.data.shouldRefreshCache) {
              console.log('发现伴侣资料更新通知，触发刷新事件:', notification);

              // 触发通知接收事件
              this.emitEvent('notificationReceived', notification);

              // 修复问题3：移除自动标记已读，让用户手动点击时才标记
              // 不再自动标记为已读
            }
          });
        }
      },
      fail: (err) => {
        console.error("检查伴侣资料更新通知失败:", err);
      }
    });
  },

  /**
   * 检查解绑状态同步通知 - 修复问题2
   */
  checkUnbindSyncNotifications: function () {
    if (!this.globalData.userInfo) {
      return;
    }

    console.log("检查解绑状态同步通知...");

    wx.cloud.callFunction({
      name: "getNotifications",
      data: {
        type: 'couple_unbound_sync',
        unreadOnly: true,
        pageSize: 5
      },
      success: (res) => {
        if (res.result && res.result.success && res.result.data.notifications) {
          const unbindSyncNotifications = res.result.data.notifications;

          console.log('找到解绑状态同步通知数量:', unbindSyncNotifications.length);

          unbindSyncNotifications.forEach(notification => {
            if (notification.data && notification.data.shouldSyncUnbindStatus) {
              console.log('处理解绑状态同步通知:', notification);

              // 立即清除所有相关数据
              this.globalData.coupleInfo = null;
              this.globalData.userInfo = null;
              this.globalData.unreadCount = 0;
              wx.removeStorageSync('coupleInfo');
              wx.removeStorageSync('userInfo');
              wx.removeStorageSync('myCoupons');
              wx.removeStorageSync('notifications');

              // 清除消息徽章
              wx.removeTabBarBadge({
                index: 2
              });

              // 触发全局刷新事件
              this.emitEvent('globalDataRefresh', {
                reason: 'couple_unbound_sync',
                timestamp: Date.now()
              });

              // 标记通知为已读
              wx.cloud.callFunction({
                name: 'markNotificationAsRead',
                data: { notificationId: notification._id },
                success: (markRes) => {
                  console.log('标记解绑同步通知为已读:', markRes.result?.success);
                }
              });

              // 显示提示
              wx.showModal({
                title: '解绑通知',
                content: '您的伴侣已解除情侣关系，相关数据已清除。',
                showCancel: false,
                confirmText: '知道了'
              });
            }
          });
        }
      },
      fail: (err) => {
        console.error("检查解绑状态同步通知失败:", err);
      }
    });
  },

  /**
   * 更新消息徽章
   */
  updateMessageBadge: function (count) {
    console.log("=== 更新消息徽章 ===");
    console.log("新的未读数量:", count);
    console.log("当前全局未读数量:", this.globalData.unreadCount);

    // 确保count是数字类型
    const unreadCount = parseInt(count) || 0;

    // 更新全局状态
    this.globalData.unreadCount = unreadCount;

    if (unreadCount > 0) {
      const badgeText = unreadCount > 99 ? "99+" : String(unreadCount);
      console.log("设置徽章文本:", badgeText);
      this.setTabBarBadgeWithRetry(badgeText);
    } else {
      console.log("移除徽章");
      this.removeTabBarBadgeWithRetry();
    }
  },

  /**
   * 带重试机制的设置徽章（修复徽章显示不稳定问题）
   */
  setTabBarBadgeWithRetry: function (badgeText, retryCount = 0) {
    const maxRetries = 3;
    
    wx.setTabBarBadge({
      index: 2, // 消息tab的索引
      text: badgeText,
      success: () => {
        console.log("设置消息徽章成功:", badgeText);
      },
      fail: (err) => {
        console.error("设置消息徽章失败:", err, "重试次数:", retryCount);
        
        if (retryCount < maxRetries) {
          // 延迟后重试
          setTimeout(() => {
            // 先尝试移除，再设置
            wx.removeTabBarBadge({
              index: 2,
              complete: () => {
                setTimeout(() => {
                  this.setTabBarBadgeWithRetry(badgeText, retryCount + 1);
                }, 200);
              }
            });
          }, 500 * (retryCount + 1));
        } else {
          console.error("设置消息徽章达到最大重试次数");
        }
      }
    });
  },

  /**
   * 带重试机制的移除徽章
   */
  removeTabBarBadgeWithRetry: function (retryCount = 0) {
    const maxRetries = 3;

    console.log("尝试移除消息徽章，重试次数:", retryCount);

    wx.removeTabBarBadge({
      index: 2,
      success: () => {
        console.log("移除消息徽章成功");
        this.globalData.unreadCount = 0; // 确保状态同步
      },
      fail: (err) => {
        console.error("移除消息徽章失败:", err, "重试次数:", retryCount);

        // 如果是因为没有徽章而失败，不需要重试
        if (err.errMsg && (err.errMsg.includes('fail not TabBar') || err.errMsg.includes('no badge'))) {
          console.log("TabBar不存在或没有徽章，忽略错误");
          this.globalData.unreadCount = 0;
          return;
        }

        if (retryCount < maxRetries) {
          setTimeout(() => {
            this.removeTabBarBadgeWithRetry(retryCount + 1);
          }, 500 * (retryCount + 1));
        } else {
          console.error("移除消息徽章达到最大重试次数，强制重置状态");
          this.globalData.unreadCount = 0;
        }
      }
    });
  },

  /**
   * 全局事件机制 - 添加事件监听器
   */
  addEventListener: function (eventName, callback) {
    if (!this.globalData.eventListeners[eventName]) {
      this.globalData.eventListeners[eventName] = [];
    }
    this.globalData.eventListeners[eventName].push(callback);
    console.log(`添加事件监听器: ${eventName}`);
  },

  /**
   * 全局事件机制 - 移除事件监听器
   */
  removeEventListener: function (eventName, callback) {
    if (this.globalData.eventListeners[eventName]) {
      const index = this.globalData.eventListeners[eventName].indexOf(callback);
      if (index > -1) {
        this.globalData.eventListeners[eventName].splice(index, 1);
        console.log(`移除事件监听器: ${eventName}`);
      }
    }
  },

  /**
   * 全局事件机制 - 触发事件
   */
  emitEvent: function (eventName, data) {
    console.log(`触发全局事件: ${eventName}`, data);
    
    if (this.globalData.eventListeners[eventName]) {
      this.globalData.eventListeners[eventName].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`事件监听器执行错误:`, error);
        }
      });
    }
  },

  /**
   * 通知所有页面刷新数据（修复绑定后页面刷新问题）
   */
  notifyAllPagesRefresh: function () {
    console.log("通知所有页面刷新数据");
    
    // 触发全局刷新事件
    this.emitEvent('globalDataRefresh', {
      userInfo: this.globalData.userInfo,
      coupleInfo: this.globalData.coupleInfo,
      timestamp: Date.now()
    });
    
    // 同时使用原有的页面刷新机制作为备用
    const pages = getCurrentPages();
    pages.forEach(page => {
      if (page.refreshPageData && typeof page.refreshPageData === 'function') {
        console.log('通知页面刷新:', page.route);
        try {
          page.refreshPageData();
        } catch (error) {
          console.error('页面刷新失败:', page.route, error);
        }
      }
    });
    
    // 延迟再次检查未读消息
    setTimeout(() => {
      this.checkUnreadMessages();
    }, 1000);
  },
});
