/**
 * 发送核销请求通知云函数
 * 当券的持有方申请核销时，向发券方发送核销请求通知
 */

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数运行上下文
 * @returns {Object} 执行结果
 */
exports.main = async (event, context) => {
  console.log('开始执行发送核销请求通知云函数:', event);
  
  try {
    // 参数验证
    const { couponId, redeemNote } = event;
    
    if (!couponId) {
      throw new Error('缺少必要参数: couponId');
    }

    // 获取当前调用者的openid
    const { OPENID } = cloud.getWXContext();
    
    // 获取券详细信息
    const couponQuery = await db.collection('coupons').doc(couponId).get();
    if (!couponQuery.data) {
      throw new Error('券信息不存在');
    }
    
    const coupon = couponQuery.data;
    console.log('券信息:', {
      _id: coupon._id,
      creatorId: coupon.creatorId,
      recipientId: coupon.recipientId,
      status: coupon.status
    });
    
    // 权限验证：只有券的接收方(持有方)可以发起核销请求
    if (coupon.recipientId !== OPENID) {
      throw new Error('无权限发起此券的核销请求');
    }

    // 验证券状态：只有已接收状态的券才能申请核销
    if (coupon.status !== 'received') {
      throw new Error(`券状态不正确，当前状态: ${coupon.status}，应该为 received`);
    }

    // 检查券是否已过期
    if (coupon.expiresAt && new Date(coupon.expiresAt) <= new Date()) {
      throw new Error('券已过期，无法申请核销');
    }

    console.log('准备发送核销请求通知:', {
      couponId,
      couponTitle: coupon.title,
      creatorId: coupon.creatorId,
      recipientId: coupon.recipientId,
      redeemNote: redeemNote || '无备注'
    });

    // 更新券状态为待核销
    await db.collection('coupons').doc(couponId).update({
      data: {
        status: 'pending_redeem',
        redeemApplyTime: new Date(),
        redeemNote: redeemNote || '',
        updateTime: new Date()
      }
    });

    // 获取申请人信息
    const userQuery = await db.collection('users').where({
      _openid: OPENID
    }).get();
    
    const userInfo = userQuery.data[0] || {};

    // 保存通知记录到数据库
    await db.collection('notifications').add({
      data: {
        type: 'redeem_request',
        title: '收到兑现申请',
        content: `${userInfo.nickName || '用户'} 申请兑现信任券：${coupon.title}`,
        recipientId: coupon.creatorId,
        senderId: OPENID,
        relatedId: couponId,
        relatedType: 'coupon',
        isRead: false,
        createTime: new Date(),
        data: {
          couponId: couponId,
          couponTitle: coupon.title,
          redeemNote: redeemNote || '',
          applicantNickname: userInfo.nickName || '用户'
        }
      }
    });

    console.log('核销请求通知已保存到数据库');

    // 返回成功结果
    return {
      success: true,
      message: '核销请求通知发送成功',
      data: {
        couponId,
        couponTitle: coupon.title,
        creatorId: coupon.creatorId,
        recipientId: coupon.recipientId,
        redeemNote: redeemNote || '',
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('发送核销请求通知云函数执行失败:', error);

    return {
      success: false,
      message: '云函数执行失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};
