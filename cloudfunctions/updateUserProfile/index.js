/**
 * 更新用户资料
 * 更新用户的昵称、头像等信息
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  console.log("updateUserProfile 事件参数:", event);
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  console.log('更新用户资料请求 - openid:', openid);
  
  try {
    // 首先查找用户是否存在 - 使用正确的字段名
    const userResult = await db.collection('users').where({
      _openid: openid  // 修复：使用_openid与其他云函数保持一致
    }).get();

    console.log('用户查询结果:', userResult);
    
    if (userResult.data.length === 0) {
      console.log('用户不存在，openid:', openid);
      return {
        success: false,
        message: '用户不存在，请先登录',
        code: 'USER_NOT_FOUND'
      };
    }
    
    const user = userResult.data[0];
    const userId = user._id;
    
    console.log('找到用户:', userId, '开始更新资料');
    
    // 准备更新的数据
    const updateData = {};
    
    // 只更新提供的字段
    if (event.nickName !== undefined) {
      updateData.nickName = event.nickName;
    }
    
    if (event.avatarUrl !== undefined) {
      updateData.avatarUrl = event.avatarUrl;
    }
    
    if (event.gender !== undefined) {
      updateData.gender = event.gender;
    }
    
    if (event.city !== undefined) {
      updateData.city = event.city;
    }
    
    if (event.province !== undefined) {
      updateData.province = event.province;
    }
    
    if (event.country !== undefined) {
      updateData.country = event.country;
    }
    
    // 添加新的字段支持
    if (event.signature !== undefined) {
      updateData.signature = event.signature;
    }
    
    if (event.isAutoGenerated !== undefined) {
      updateData.isAutoGenerated = event.isAutoGenerated;
    }
    
    if (event.isProfileComplete !== undefined) {
      updateData.isProfileComplete = event.isProfileComplete;
    }
    
    // 添加更新时间
    updateData.updateTime = new Date();
    
    // 如果没有要更新的数据
    if (Object.keys(updateData).length <= 1) { // 只有updateTime
      return {
        success: false,
        message: '没有要更新的数据',
        code: 'NO_DATA_TO_UPDATE'
      };
    }
    
    console.log('准备更新的数据:', updateData);
    
    // 更新用户信息
    const updateResult = await db.collection('users').doc(userId).update({
      data: updateData
    });
    
    console.log('用户资料更新结果:', updateResult);
    
    // 获取更新后的用户信息
    const updatedUserResult = await db.collection('users').doc(userId).get();
    
    console.log('更新后的用户信息:', updatedUserResult.data);
    
    return {
      success: true,
      message: '用户资料更新成功',
      data: {
        userInfo: updatedUserResult.data,
        updatedFields: Object.keys(updateData).filter(key => key !== 'updateTime')
      }
    };
    
  } catch (error) {
    console.error('更新用户资料失败 - 错误详情:', error);
    return {
      success: false,
      message: '更新用户资料失败: ' + error.message,
      error: error.message,
      code: 'UPDATE_FAILED'
    };
  }
}; 