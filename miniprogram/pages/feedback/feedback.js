// feedback.js
const app = getApp();

Page({
  data: {
    faqList: [
      {
        id: 1,
        question: '如何创建信任券？',
        answer: '点击首页的"创建信任券"按钮，填写券名称、描述、使用规则和有效期，然后提交即可。创建完成后可以发送给您的另一半。',
        expanded: false
      },
      {
        id: 2,
        question: '信任券过期了怎么办？',
        answer: '过期的信任券将无法使用，建议在有效期内及时使用。如果有特殊情况，可以与另一半协商重新创建类似的券。',
        expanded: false
      },
      {
        id: 3,
        question: '如何与另一半建立绑定关系？',
        answer: '在个人中心点击"情侣绑定"，选择创建邀请或输入邀请码。一方创建邀请码，另一方输入邀请码即可完成绑定。',
        expanded: false
      },
      {
        id: 4,
        question: '可以修改已发送的信任券吗？',
        answer: '已发送的信任券无法修改，这是为了保证承诺的严肃性。如需修改，建议撤回后重新创建。',
        expanded: false
      },
      {
        id: 5,
        question: '忘记密码或无法登录怎么办？',
        answer: '本应用使用微信授权登录，无需单独设置密码。如果无法登录，请检查微信授权状态或重新安装小程序。',
        expanded: false
      }
    ],
    feedbackTypes: [
      { value: 'bug', label: '功能异常', icon: '🐛' },
      { value: 'suggestion', label: '功能建议', icon: '💡' },
      { value: 'ui', label: '界面问题', icon: '🎨' },
      { value: 'performance', label: '性能问题', icon: '⚡' },
      { value: 'content', label: '内容问题', icon: '📝' },
      { value: 'other', label: '其他问题', icon: '❓' }
    ],
    selectedType: '',
    formData: {
      description: '',
      contact: ''
    },
    errors: {},
    screenshots: [],
    maxImages: 3,
    isFormValid: false,
    isSubmitting: false,
    isLoading: false,
    loadingText: '',
    showSuccessModal: false,
    serviceEmail: '<EMAIL>',
    servicePhone: '************'
  },

  onLoad: function() {
    console.log('帮助与反馈页面加载');
  },

  /**
   * 展开/收起FAQ
   */
  toggleFaq(e) {
    const index = e.currentTarget.dataset.index;
    const faqList = [...this.data.faqList];
    faqList[index].expanded = !faqList[index].expanded;
    
    console.log('切换FAQ展开状态:', index);
    this.setData({ faqList });
  },

  /**
   * 选择反馈类型
   */
  selectFeedbackType(e) {
    const type = e.currentTarget.dataset.type;
    console.log('选择反馈类型:', type);
    
    this.setData({ 
      selectedType: type,
      'errors.type': ''
    });
    this.validateForm();
  },

  /**
   * 问题描述输入
   */
  onDescriptionInput(e) {
    const value = e.detail.value.trim();
    this.setData({
      'formData.description': value,
      'errors.description': ''
    });
    this.validateForm();
  },

  /**
   * 联系方式输入
   */
  onContactInput(e) {
    const value = e.detail.value.trim();
    this.setData({
      'formData.contact': value
    });
  },

  /**
   * 选择图片
   */
  chooseImages() {
    const remainCount = this.data.maxImages - this.data.screenshots.length;

    if (remainCount <= 0) {
      wx.showToast({
        title: `最多只能上传${this.data.maxImages}张图片`,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        this.uploadImages(tempFilePaths);
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
      }
    });
  },

  /**
   * 上传图片到云存储
   */
  async uploadImages(filePaths) {
    wx.showLoading({ title: '上传图片中...' });
    
    const uploadTasks = filePaths.map(async (filePath, index) => {
      try {
        const cloudPath = `feedback-images/${Date.now()}_${index}.jpg`;
        const result = await wx.cloud.uploadFile({
          cloudPath: cloudPath,
          filePath: filePath
        });
        return result.fileID;
      } catch (error) {
        console.error('上传图片失败:', error);
        return null;
      }
    });

    try {
      const fileIDs = await Promise.all(uploadTasks);
      const validFileIDs = fileIDs.filter(id => id !== null);
      
      this.setData({
        screenshots: [...this.data.screenshots, ...validFileIDs]
      });
      
      wx.hideLoading();
      wx.showToast({
        title: '图片上传成功',
        icon: 'success',
        duration: 1500
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '图片上传失败',
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 删除图片
   */
  removeImage(e) {
    const index = e.currentTarget.dataset.index;
    const screenshots = [...this.data.screenshots];
    screenshots.splice(index, 1);
    this.setData({ screenshots });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const index = e.currentTarget.dataset.index;
    const { screenshots } = this.data;
    
    wx.previewImage({
      urls: screenshots,
      current: screenshots[index]
    });
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { selectedType, formData } = this.data;
    const errors = {};
    let isValid = true;

    // 验证反馈类型
    if (!selectedType) {
      errors.type = '请选择反馈类型';
      isValid = false;
    }

    // 验证问题描述
    if (!formData.description || formData.description.length < 10) {
      errors.description = '问题描述至少需要10个字符';
      isValid = false;
    } else if (formData.description.length > 500) {
      errors.description = '问题描述不能超过500个字符';
      isValid = false;
    }

    this.setData({
      errors: errors,
      isFormValid: isValid
    });

    return isValid;
  },

  /**
   * 提交反馈
   */
  submitFeedback(e) {
    console.log('提交反馈');

    if (!this.validateForm()) {
      wx.showToast({
        title: '请检查表单内容',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    this.setData({ isSubmitting: true });

    // 准备提交数据
    const submitData = {
      type: this.data.selectedType,
      description: this.data.formData.description,
      contact: this.data.formData.contact,
      screenshots: this.data.screenshots,
      deviceInfo: this.getDeviceInfo(),
      timestamp: new Date().toISOString()
    };

    console.log('提交数据:', submitData);

    // 调用云函数提交反馈
    wx.cloud.callFunction({
      name: 'submitFeedback',
      data: submitData,
      success: (res) => {
        console.log('提交反馈成功:', res.result);
        this.setData({ isSubmitting: false });
        
        if (res.result.success) {
          // 重置表单
          this.resetForm();
          
          // 显示成功弹窗
          this.setData({ showSuccessModal: true });
        } else {
          wx.showToast({
            title: res.result.message || '提交失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (error) => {
        console.error('提交反馈失败:', error);
        this.setData({ isSubmitting: false });
        wx.showToast({
          title: '提交失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      selectedType: '',
      formData: {
        description: '',
        contact: ''
      },
      errors: {},
      screenshots: [],
      isFormValid: false
    });
  },

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      return {
        model: systemInfo.model,
        system: systemInfo.system,
        platform: systemInfo.platform,
        version: systemInfo.version,
        SDKVersion: systemInfo.SDKVersion
      };
    } catch (error) {
      console.warn('获取系统信息失败:', error);
      return {
        model: 'unknown',
        system: 'unknown',
        platform: 'unknown',
        version: 'unknown',
        SDKVersion: 'unknown'
      };
    }
  },

  /**
   * 隐藏成功弹窗
   */
  hideSuccessModal() {
    this.setData({ showSuccessModal: false });
  },

  /**
   * 防止事件冒泡
   */
  preventClose() {
    // 空函数，防止事件冒泡
  },

  /**
   * 复制文本
   */
  copyText(e) {
    const text = e.currentTarget.dataset.text;
    console.log('复制文本:', text);

    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '已复制到剪贴板',
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 加入QQ群
   */
  joinQRGroup() {
    console.log('加入用户交流群');
    
    wx.showModal({
      title: '用户交流群',
      content: '请添加客服微信号: couplelove_cs，备注"交流群"，会邀请您进入官方用户交流群。',
      confirmText: '复制微信号',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: 'couplelove_cs',
            success: () => {
              wx.showToast({
                title: '微信号已复制',
                icon: 'success',
                duration: 2000
              });
            }
          });
        }
      }
    });
  },

  /**
   * 拨打客服电话
   */
  callService() {
    const phone = this.data.servicePhone;
    console.log('拨打客服电话:', phone);

    wx.makePhoneCall({
      phoneNumber: phone,
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (error) => {
        console.error('拨打电话失败:', error);
        wx.showToast({
          title: '拨号失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '情侣信任券 - 帮助与反馈',
      path: '/pages/index/index'
    };
  }
});

