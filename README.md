# 情侣信誉券小程序 v3.0.0 - 消息通知系统完整重构版

## 📦 项目概述

这是一款专为情侣设计的信誉券小程序，让情侣之间可以通过发放和兑换各种有趣的"券"来增进感情，记录美好时光。

## ✨ 最新更新 v3.0.0 (2025.1.19)

### 🔄 消息通知系统完整重构
- **二级tab导航**：兑现申请(我申请的/我接收的)、兑现完成(我申请的/我兑现的)，信息组织更清晰
- **完整状态流转**：发券→申请兑现→兑现完成→过期，消息自动在不同tab间迁移
- **角色驱动体验**：根据用户角色(申请方/审批方)显示不同文案和内容
- **智能状态管理**：新增updateNotificationStatus云函数，统一管理消息状态变化
- **精准消息分类**：每个阶段的消息都在正确的tab显示，避免混乱

### 🎯 核心业务逻辑优化
- **新券提醒**：收到券时显示，申请兑现后自动隐藏
- **兑现申请**：申请方看到"等待对方兑现"，审批方看到"收到兑现申请"
- **兑现完成**：受益方看到"已成功兑现"，处理方看到"您已兑现爱人申请的券"
- **过期处理**：券过期时隐藏所有相关消息，统一在过期提醒显示

### 🛠 技术架构升级
- **状态机设计**：明确的状态转换规则，防止数据不一致
- **云函数协同**：券状态管理+通知状态管理双重保障
- **角色权限系统**：recipient/creator/applicant/approver/beneficiary/processor六种角色
- **智能筛选算法**：支持多级筛选，状态感知，高性能计数

## ✨ 历史更新 v2.7.3 (2025.1.19)

### 🔧 消息页面按钮布局和筛选功能终极修复
- **按钮尺寸精确控制**：大幅缩小按钮padding(6rpx 10rpx)和间距(8rpx)，彻底解决按钮过宽遮挡问题
- **布局稳定性保证**：全部已读按钮常驻显示，通过disabled状态控制，避免布局跳动
- **筛选功能完全修复**：修正通知类型映射(redeem_result、expiry统一)，兑现申请/完成/过期提醒tab正常显示数据
- **智能交互优化**：无未读消息时点击全部已读显示友好提示，按钮状态视觉反馈清晰

### 🎯 技术修复亮点
- **精确尺寸控制**：多层次优化(padding+gap+font-size+border-radius)实现真正自适应
- **完整类型映射**：建立云函数与前端通知类型对应表，支持一对多映射(expiry→两种过期类型)
- **稳定布局系统**：flex布局+防压缩+防溢出，确保各种状态下布局稳定
- **性能优化**：减少DOM变化，优化筛选算法，提升渲染性能

## ✨ 历史更新 v2.7.2 (2025.1.19)

### 🎨 消息页面头部布局完美优化
- **智能按钮显示**：全部已读按钮只在有未读消息时显示，避免界面冗余
- **完美左右对齐**：左侧标题+消息条数，右侧操作按钮，布局更合理
- **自适应按钮尺寸**：按钮宽度高度跟随内容自适应，不再固定过宽
- **情侣主题图标**：全部已读使用💕爱心图标，清空使用🧹扫帚图标，更符合主题
- **响应式布局**：针对小屏幕设备优化，确保各种屏幕下显示完美

### 🎯 技术优化亮点
- **智能状态管理**：动态计算未读消息状态，多处同步更新
- **灵活布局系统**：flex布局+自适应尺寸+响应式设计
- **主题一致性**：图标选择和颜色搭配完美融入情侣券温馨主题
- **用户体验**：按需显示、操作清晰、视觉层次丰富

## ✨ 历史更新 v2.7.1 (2025.1.19)

### 🔧 消息页面和个人中心优化
- **消息页面头部优化**：消息条数、全部已读、清空功能常驻展示，布局更合理
- **修复消息跳转问题**：解决点击消息提示"券信息不存在"的bug，优化云函数关联查询
- **简化消息操作**：移除消息卡片的操作按钮，只保留未读标记和清空功能
- **未读标记优化**：重新设计未读标记样式，更明显的视觉效果和动画
- **个人中心精简**：移除设置和消息通知模块，调整我的数据模块位置

### 🎯 用户体验提升
- **布局优化**：消息页面头部功能一行展示，视觉更简洁
- **交互简化**：移除复杂的消息操作，只保留核心功能
- **数据关联**：修复消息与券信息的关联查询，确保跳转正常
- **模块调整**：个人中心功能模块重新排列，更符合使用习惯

## ✨ 历史更新 v2.5.0 (2025.1.19)

### 🎨 券卡片设计全面重构
- **真实卡片比例**：采用1.59:1的真实卡片比例，更符合实际券卡设计
- **券编号系统**：新增券编号字段，格式为"No.YY+4位数字"（如No.25001）
- **券类型分类**：支持5种券类型（承诺券、任务券、奖励券、道歉券、特别券）
- **多种视觉风格**：渐变、玻璃拟态、简约、3D效果等多种风格
- **精美装饰元素**：装饰孔、装饰线、背景图案等真实券卡元素

### 🎯 券类型系统
1. **承诺券** (💕) - 粉色渐变：用于承诺类内容
2. **任务券** (📝) - 蓝色渐变：用于任务分配
3. **奖励券** (🎁) - 金色渐变：用于奖励兑换
4. **道歉券** (🙏) - 紫色渐变：用于道歉表达
5. **特别券** (✨) - 彩虹渐变：用于特殊场合

### 🎪 视觉效果升级
- **动画效果**：图标浮动、彩虹渐变、脉冲动画等
- **交互反馈**：悬停效果、点击反馈、选中状态
- **响应式设计**：适配不同屏幕尺寸，支持网格布局
- **深色模式**：完整的深色模式适配

### 🛠 技术特性
- **现代CSS**：aspect-ratio、backdrop-filter、CSS动画
- **组件化设计**：高度可复用的券卡片组件
- **数据兼容性**：向后兼容现有券数据
- **性能优化**：GPU加速动画、合理的重绘策略

## 🏗 项目架构

### 前端页面 (10个)
- **index** - 首页展示
- **binding** - 情侣绑定
- **createCoupon** - 创建券（新增券类型选择器）
- **myCoupons** - 我的券（使用新券卡片组件）
- **couponDetail** - 券详情
- **notifications** - 消息通知
- **profile** - 个人资料
- **editProfile** - 编辑资料
- **settings** - 设置页面
- **about** - 关于页面

### 核心组件
- **couponCard** - 券卡片组件（全面重构）
- **customModal** - 自定义弹窗
- **emptyState** - 空状态展示
- **loadingIndicator** - 加载指示器
- **listItem** - 列表项组件

### 云函数 (26个)
- **createOrUpdateCoupon** - 创建/更新券（支持新字段）
- **getCoupons** - 获取券列表
- **getCouponDetail** - 获取券详情
- **updateCouponStatus** - 更新券状态
- **loginOrCreateUser** - 用户登录/创建
- **updateUserProfile** - 更新用户资料
- 其他通知、统计、管理类云函数...

## 🎨 券卡片设计规范

### 设计理念
- **突出"券"概念**：采用真实券卡的设计元素
- **情侣主题**：温馨浪漫的色彩搭配
- **现代感**：使用最新的CSS技术和动画效果

### 卡片结构
```
┌─────────────────────────────────┐
│ [券类型]              [券编号] │
│                                 │
│           [图标]               │
│          [标题]                │
│         [描述]                 │
│                                 │
│ [发券人]          [状态指示器] │
│ [有效期]                       │
└─────────────────────────────────┘
```

### 装饰元素
- **装饰孔**：左右两侧的圆形装饰孔
- **装饰线**：顶部和底部的渐变装饰线
- **背景图案**：点状和线条组合的背景纹理
- **阴影效果**：模糊阴影营造立体感

## 🚀 部署指南

### 环境要求
- 微信开发者工具
- 微信小程序账号
- 微信云开发环境

### 部署步骤
1. **克隆项目**
```bash
git clone [项目地址]
cd qinglvquan2
```

2. **导入微信开发者工具**
   - 打开微信开发者工具
   - 选择"导入项目"
   - 选择项目目录

3. **配置云开发**
   - 在微信开发者工具中开通云开发
   - 创建数据库集合（详见docs/数据库集合结构说明.md）

4. **部署云函数**
   - 右键每个云函数文件夹
   - 选择"创建并部署：云端安装依赖"

5. **配置项目**
   - 修改project.config.json中的appid
   - 配置合法域名

## 📱 功能特性

### 核心功能
- ✅ 情侣账号绑定
- ✅ 券的创建、发送、接收
- ✅ 券的兑换申请和审批
- ✅ 实时消息通知系统
- ✅ 个人资料管理
- ✅ 券的筛选和管理

### 券管理功能
- ✅ 5种券类型分类管理
- ✅ 券编号自动生成
- ✅ 券状态实时跟踪
- ✅ 券过期自动处理
- ✅ 券使用统计分析

### 用户体验
- ✅ 精美的券卡片设计
- ✅ 流畅的动画效果
- ✅ 响应式布局适配
- ✅ 完整的错误处理
- ✅ 友好的交互反馈

## 🎯 使用说明

### 创建券
1. 选择券类型（承诺券、任务券等）
2. 填写券标题和描述
3. 选择图标和颜色
4. 设置有效期（可选）
5. 发送给对方

### 管理券
- **筛选查看**：按状态筛选券（全部、可使用、待核销等）
- **详情查看**：点击券卡片查看详细信息
- **快捷操作**：直接在列表中进行快速操作

### 券的状态流转
```
创建 → 已发送 → 已接收 → 申请兑换 → 已兑换/已拒绝
```

## 🔧 开发指南

### 添加新券类型
1. 在`couponCard.js`中添加类型配置
2. 在`couponCard.wxss`中添加对应样式
3. 在`createCoupon.js`中更新类型选项
4. 在云函数中添加验证逻辑

### 自定义券风格
1. 在`couponCard.wxss`中添加新的风格类
2. 通过`style`属性应用风格
3. 支持的风格：gradient、glass、minimal、3d

## 📊 数据统计

### 项目规模
- **代码文件**：200+ 个文件
- **代码行数**：15,000+ 行
- **云函数**：26 个
- **前端页面**：10 个
- **组件**：5 个

### 功能完成度
- ✅ 核心功能：100%
- ✅ 用户体验：100%
- ✅ 券卡片设计：100%
- ✅ 数据管理：100%
- ✅ 错误处理：100%

## 📝 更新日志

### v2.5.0 (2025.1.19) - 券卡片设计重构
- 🎨 全新券卡片设计，采用真实卡片比例
- 🏷️ 新增券编号和券类型系统
- ✨ 多种视觉风格和动画效果
- 📱 响应式设计和深色模式支持

### v2.4.1 (2025.1.19) - 登录体验优化
- 🔐 彻底优化登录流程，实现真正一键登录
- 💝 重新设计个人资料编辑页面
- 🎨 UI/UX全面重构，多层渐变背景
- 🛠️ 技术架构优化，使用最新微信API

### v2.4.0 (2024.12.17) - 核心功能完善
- 📝 完善券的创建和管理功能
- 🔔 优化消息通知系统
- 🐛 修复已知问题和性能优化

## 🤝 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目！

## 📄 许可证

本项目采用 MIT 许可证。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 微信群讨论

---

**情侣信誉券小程序** - 用券传递爱意，让每一份承诺都有迹可循 💕
