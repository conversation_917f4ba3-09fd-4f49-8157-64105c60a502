// Content for the User Journey section

function renderUserJourney() {
    return `
        <div class="mb-6">
            <p class="mb-4">用户旅程图展示了用户从发现、登录、理解价值、伴侣绑定、核心体验（发券/收券/查券）、核销/历史的完整路径，包含用户的行为、接触点、想法、情绪及痛点/机会。</p>

            <div class="svg-container">
                <svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
                  <style>
                    .stage { fill: #E0F7FA; stroke: #00ACC1; stroke-width: 2; }
                    .arrow { stroke: #00ACC1; stroke-width: 2; marker-end: url(#arrowhead); }
                    .text { font-family: sans-serif; font-size: 12px; fill: #333; }
                    .header { font-weight: bold; font-size: 14px; fill: #00796B; }
                    .pain-point { fill: #FFEBEE; stroke: #E57373; stroke-width: 1; }
                    .opportunity { fill: #E8F5E9; stroke: #81C784; stroke-width: 1; }
                    .feeling { font-style: italic; fill: #555; }
                    #arrowhead { fill: #00ACC1; }
                  </style>
                  <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                      <polygon points="0 0, 10 3.5, 0 7"></polygon>
                    </marker>
                  </defs>

                  <!-- Stages -->
                  <rect x="50" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
                  <text x="110" y="80" text-anchor="middle" class="header">发现/访问</text>

                  <rect x="200" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
                  <text x="260" y="80" text-anchor="middle" class="header">注册/登录</text>

                  <rect x="350" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
                  <text x="410" y="80" text-anchor="middle" class="header">理解价值</text>

                  <rect x="500" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
                  <text x="560" y="80" text-anchor="middle" class="header">伴侣绑定</text>

                  <rect x="650" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
                  <text x="710" y="80" text-anchor="middle" class="header">核心体验</text>

                  <rect x="800" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
                  <text x="860" y="80" text-anchor="middle" class="header">核销/历史</text>

                  <!-- Rows -->
                  <text x="10" y="150" class="header">阶段</text>
                  <text x="10" y="250" class="header">行为</text>
                  <text x="10" y="350" class="header">接触点</text>
                  <text x="10" y="450" class="header">想法</text>
                  <text x="10" y="550" class="header">情绪</text>
                  <text x="10" y="650" class="header">痛点/机会</text>

                  <!-- Content -->
                  <!-- Stage 1: 发现/访问 -->
                  <text x="110" y="150" text-anchor="middle" class="text">发现/访问</text>
                  <text x="110" y="250" text-anchor="middle" class="text">了解产品</text>
                  <text x="110" y="350" text-anchor="middle" class="text">亲友推荐、社交媒体、应用商店</text>
                  <text x="110" y="450" text-anchor="middle" class="text">"这是什么？有趣吗？"</text>
                  <text x="110" y="550" text-anchor="middle" class="feeling">好奇</text>
                  <rect x="60" y="640" width="100" height="50" rx="5" ry="5" class="opportunity"></rect>
                  <text x="110" y="665" text-anchor="middle" class="text">营销触达，引导体验</text>

                  <!-- Stage 2: 注册/登录 -->
                  <text x="260" y="150" text-anchor="middle" class="text">注册/登录</text>
                  <text x="260" y="250" text-anchor="middle" class="text">微信授权</text>
                  <text x="260" y="350" text-anchor="middle" class="text">小程序授权页面</text>
                  <text x="260" y="450" text-anchor="middle" class="text">"希望快速方便"</text>
                  <text x="260" y="550" text-anchor="middle" class="feeling">期待顺利</text>
                  <rect x="210" y="640" width="100" height="50" rx="5" ry="5" class="pain-point"></rect>
                  <text x="260" y="665" text-anchor="middle" class="text">授权流程卡顿</text>

                  <!-- Stage 3: 理解价值 -->
                  <text x="410" y="150" text-anchor="middle" class="text">理解价值</text>
                  <text x="410" y="250" text-anchor="middle" class="text">浏览产品介绍/指引</text>
                  <text x="410" y="350" text-anchor="middle" class="text">首页、新手指引页</text>
                  <text x="410" y="450" text-anchor="middle" class="text">"这个券怎么用？有什么好处？"</text>
                  <text x="410" y="550" text-anchor="middle" class="feeling">求知</text>
                  <rect x="360" y="640" width="100" height="50" rx="5" ry="5" class="opportunity"></rect>
                  <text x="410" y="665" text-anchor="middle" class="text">清晰价值主张与指引</text>

                  <!-- Stage 4: 伴侣绑定 -->
                  <text x="560" y="150" text-anchor="middle" class="text">伴侣绑定</text>
                  <text x="560" y="250" text-anchor="middle" class="text">发起/接受邀请，分享链接/码</text>
                  <text x="560" y="350" text-anchor="middle" class="text">邀请页面、微信分享</text>
                  <text x="560" y="450" text-anchor="middle" class="text">"怎么邀请？对方会收到吗？"</text>
                  <text x="560" y="550" text-anchor="middle" class="feeling">忐忑、期待回应</text>
                  <rect x="510" y="640" width="100" height="50" rx="5" ry="5" class="pain-point"></rect>
                  <text x="560" y="665" text-anchor="middle" class="text">邀请流程复杂</text>
                   <rect x="510" y="700" width="100" height="50" rx="5" ry="5" class="opportunity"></rect>
                  <text x="560" y="725" text-anchor="middle" class="text">清晰指引，分享便捷</text>

                  <!-- Stage 5: 核心体验 (发券/收券/查券) -->
                  <text x="710" y="150" text-anchor="middle" class="text">核心体验</text>
                  <text x="710" y="250" text-anchor="middle" class="text">创建券、填写信息、选择伴侣、发送；接收通知、查看列表/详情</text>
                  <text x="710" y="350" text-anchor="middle" class="text">发券页、券列表、券详情</text>
                  <text x="710" y="450" text-anchor="middle" class="text">"我要发什么券？对方收到会开心吗？我有什么券？"</text>
                  <text x="710" y="550" text-anchor="middle" class="feeling">创意、期待、满足</text>
                  <rect x="660" y="640" width="100" height="50" rx="5" ry="5" class="pain-point"></rect>
                  <text x="710" y="665" text-anchor="middle" class="text">发券内容填写繁琐</text>
                   <rect x="660" y="700" width="100" height="50" rx="5" ry="5" class="opportunity"></rect>
                  <text x="710" y="725" text-anchor="middle" class="text">预设模板、快速发送</text>

                  <!-- Stage 6: 核销/历史 -->
                  <text x="860" y="150" text-anchor="middle" class="text">核销/历史</text>
                  <text x="860" y="250" text-anchor="middle" class="text">在合适时机使用券，发起核销；查看已使用/已过期券</text>
                  <text x="860" y="350" text-anchor="middle" class="text">券详情、历史记录页</text>
                  <text x="860" y="450" text-anchor="middle" class="text">"现在是时候使用了！这次核销顺利吗？我们发过多少券了？"</text>
                  <text x="860" y="550" text-anchor="middle" class="feeling">兴奋、满足、回忆</text>
                  <rect x="810" y="640" width="100" height="50" rx="5" ry="5" class="pain-point"></rect>
                  <text x="860" y="665" text-anchor="middle" class="text">核销流程不顺畅</text>
                   <rect x="810" y="700" width="100" height="50" rx="5" ry="5" class="opportunity"></rect>
                  <text x="860" y="725" text-anchor="middle" class="text">单方面快速核销</text>

                  <!-- Arrows -->
                  <path d="M170 400 L 200 400" class="arrow"></path>
                  <path d="M320 400 L 350 400" class="arrow"></path>
                  <path d="M470 400 L 500 400" class="arrow"></path>
                  <path d="M620 400 L 650 400" class="arrow"></path>
                  <path d="M770 400 L 800 400" class="arrow"></path>
                </svg>
            </div>
            
            <h3 class="text-xl font-bold text-pink-800 mt-8 mb-4">用户旅程关键阶段分析</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white p-5 rounded-lg shadow hover:shadow-md transition-shadow">
                    <h4 class="font-bold text-lg text-pink-700 mb-3">初始接触阶段</h4>
                    <p class="text-gray-700 mb-3">从发现产品到完成注册登录，是用户形成第一印象的关键阶段。</p>
                    <div class="text-sm">
                        <p class="text-gray-600 flex items-center mb-1">
                            <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                            <span>痛点：授权流程可能带来摩擦</span>
                        </p>
                        <p class="text-gray-600 flex items-center">
                            <i class="fas fa-lightbulb text-green-500 mr-2"></i>
                            <span>机会：简化登录流程，提供清晰的价值展示</span>
                        </p>
                    </div>
                </div>
                
                <div class="bg-white p-5 rounded-lg shadow hover:shadow-md transition-shadow">
                    <h4 class="font-bold text-lg text-pink-700 mb-3">体验核心价值阶段</h4>
                    <p class="text-gray-700 mb-3">伴侣绑定和核心功能使用是用户实际体验产品价值的阶段。</p>
                    <div class="text-sm">
                        <p class="text-gray-600 flex items-center mb-1">
                            <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                            <span>痛点：邀请流程复杂，发券过程繁琐</span>
                        </p>
                        <p class="text-gray-600 flex items-center">
                            <i class="fas fa-lightbulb text-green-500 mr-2"></i>
                            <span>机会：简化绑定流程，提供模板加速发券</span>
                        </p>
                    </div>
                </div>
                
                <div class="bg-white p-5 rounded-lg shadow hover:shadow-md transition-shadow">
                    <h4 class="font-bold text-lg text-pink-700 mb-3">价值沉淀阶段</h4>
                    <p class="text-gray-700 mb-3">核销流程和历史记录查看是用户感受长期价值的关键。</p>
                    <div class="text-sm">
                        <p class="text-gray-600 flex items-center mb-1">
                            <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                            <span>痛点：核销流程不够顺畅直接</span>
                        </p>
                        <p class="text-gray-600 flex items-center">
                            <i class="fas fa-lightbulb text-green-500 mr-2"></i>
                            <span>机会：简化核销，强化情感记忆沉淀</span>
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-pink-50 border border-pink-100 rounded-lg p-5">
                <h4 class="font-bold text-lg text-pink-700 mb-2">用户旅程总结</h4>
                <p>整体用户旅程呈现为一个从探索到深度体验的过程，情绪曲线整体呈上升趋势，在伴侣绑定阶段可能出现轻微波动，但在核心体验和核销历史阶段达到高点。MVP阶段应重点关注简化流程、提高操作效率和增强情感体验这三个方面，确保用户在每个关键节点都能获得积极情绪反馈。</p>
            </div>
        </div>
    `;
}
