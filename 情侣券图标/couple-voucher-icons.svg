<!-- 情侣信誉券专业图标设计 - 5款顶级方案 144x144px -->

<!-- 方案1：经典券面设计 - 专业金融风格 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="voucherGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFE4E6"/>
      <stop offset="50%" stop-color="#FFB6C1"/>
      <stop offset="100%" stop-color="#FF69B4"/>
    </linearGradient>
    <linearGradient id="paperGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFFFFF"/>
      <stop offset="100%" stop-color="#FAFAFA"/>
    </linearGradient>
    <filter id="premiumShadow1">
      <feDropShadow dx="3" dy="5" stdDeviation="8" flood-opacity="0.25"/>
    </filter>
    <pattern id="securityPattern1" patternUnits="userSpaceOnUse" width="20" height="20">
      <rect width="20" height="20" fill="#FFF5F7"/>
      <circle cx="10" cy="10" r="1" fill="#FFB6C1" opacity="0.3"/>
    </pattern>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="72" cy="72" r="70" fill="url(#voucherGradient1)" filter="url(#premiumShadow1)"/>
  
  <!-- 券面主体 -->
  <rect x="20" y="40" width="104" height="64" rx="8" fill="url(#paperGradient1)" filter="url(#premiumShadow1)"/>
  
  <!-- 安全纹理背景 -->
  <rect x="20" y="40" width="104" height="64" rx="8" fill="url(#securityPattern1)" opacity="0.4"/>
  
  <!-- 券面边框 -->
  <rect x="20" y="40" width="104" height="64" rx="8" fill="none" stroke="#FF69B4" stroke-width="2"/>
  
  <!-- 左侧撕边设计 -->
  <path d="M20 45 L20 40 Q20 36, 24 36 L28 36 Q32 36, 32 40 L32 45 Z" fill="#FF69B4" opacity="0.8"/>
  <path d="M20 99 L20 104 Q20 108, 24 108 L28 108 Q32 108, 32 104 L32 99 Z" fill="#FF69B4" opacity="0.8"/>
  
  <!-- 标题区域 -->
  <rect x="28" y="50" width="88" height="8" rx="2" fill="#FF69B4"/>
  <text x="72" y="65" text-anchor="middle" font-family="serif" font-size="10" font-weight="bold" fill="#FF69B4"></text>
  
  <!-- 双心图案 -->
  <path d="M60 75 C58 73, 55 73, 55 76 C55 79, 60 84, 60 84 C60 84, 65 79, 65 76 C65 73, 62 73, 60 75" fill="#FF69B4"/>
  <path d="M84 75 C82 73, 79 73, 79 76 C79 79, 84 84, 84 84 C84 84, 89 79, 89 76 C89 73, 86 73, 84 75" fill="#FF69B4"/>
  
  <!-- 连接线 -->
  <line x1="65" y1="78" x2="79" y2="78" stroke="#FF69B4" stroke-width="2" stroke-linecap="round"/>
  
  <!-- 印章效果 -->
  <circle cx="100" cy="90" r="8" fill="none" stroke="#FF69B4" stroke-width="1.5"/>
  <text x="100" y="94" text-anchor="middle" font-family="serif" font-size="6" fill="#FF69B4"></text>
</svg>

<!-- 方案2：现代卡片设计 - 简约奢华风 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="modernGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FF8A80"/>
      <stop offset="50%" stop-color="#FFAB91"/>
      <stop offset="100%" stop-color="#FFCC9A"/>
    </linearGradient>
    <linearGradient id="cardGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFFFFF"/>
      <stop offset="100%" stop-color="#F8F9FA"/>
    </linearGradient>
    <filter id="luxuryShadow2">
      <feDropShadow dx="4" dy="6" stdDeviation="12" flood-opacity="0.2"/>
    </filter>
    <linearGradient id="goldAccent2" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#FFD700"/>
      <stop offset="50%" stop-color="#FFA500"/>
      <stop offset="100%" stop-color="#FF8C00"/>
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="144" height="144" rx="28" fill="url(#modernGradient2)" filter="url(#luxuryShadow2)"/>
  
  <!-- 主卡片 -->
  <rect x="24" y="36" width="96" height="72" rx="12" fill="url(#cardGradient2)" filter="url(#luxuryShadow2)"/>
  
  <!-- 金色装饰条 -->
  <rect x="24" y="36" width="96" height="6" rx="3" fill="url(#goldAccent2)"/>
  
  <!-- 芯片设计（模拟银行卡） -->
  <rect x="36" y="52" width="16" height="12" rx="2" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="1"/>
  <rect x="38" y="54" width="12" height="8" rx="1" fill="#F5F5F5"/>
  
  <!-- 情侣头像区域 -->
  <circle cx="48" cy="80" r="10" fill="#FFE4E6" stroke="#FF69B4" stroke-width="2"/>
  <circle cx="76" cy="80" r="10" fill="#FFE4E6" stroke="#FF69B4" stroke-width="2"/>
  
  <!-- 简化人像 -->
  <circle cx="48" cy="77" r="3" fill="#FF69B4"/>
  <path d="M43 85 Q48 88, 53 85" stroke="#FF69B4" stroke-width="2" fill="none"/>
  <circle cx="76" cy="77" r="3" fill="#FF69B4"/>
  <path d="M71 85 Q76 88, 81 85" stroke="#FF69B4" stroke-width="2" fill="none"/>
  
  <!-- 连接心形 -->
  <path d="M62 80 C60 78, 57 78, 57 81 C57 84, 62 89, 62 89 C62 89, 67 84, 67 81 C67 78, 64 78, 62 80" fill="#FF1744"/>
  
  <!-- 现代文字设计 -->
  <rect x="88" y="52" width="24" height="3" rx="1.5" fill="#FF69B4" opacity="0.8"/>
  <rect x="88" y="58" width="20" height="2" rx="1" fill="#FFB6C1" opacity="0.6"/>
  <rect x="88" y="62" width="16" height="2" rx="1" fill="#FFB6C1" opacity="0.6"/>
  
  <!-- NFC标识 -->
  <circle cx="100" cy="90" r="6" fill="none" stroke="#FF69B4" stroke-width="1"/>
  <path d="M97 87 Q100 85, 103 87 Q100 89, 97 91 Q100 93, 103 91" stroke="#FF69B4" stroke-width="1" fill="none"/>
</svg>

<!-- 方案3：印章认证风格 - 传统与现代结合 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="sealGradient3" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#FFCDD2"/>
      <stop offset="70%" stop-color="#F8BBD9"/>
      <stop offset="100%" stop-color="#E1BEE7"/>
    </radialGradient>
    <linearGradient id="documentGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFFEF7"/>
      <stop offset="100%" stop-color="#FFF8E1"/>
    </linearGradient>
    <filter id="authenticShadow3">
      <feDropShadow dx="2" dy="4" stdDeviation="6" flood-opacity="0.3"/>
    </filter>
    <pattern id="watermark3" patternUnits="userSpaceOnUse" width="30" height="30">
      <rect width="30" height="30" fill="#FAFAFA"/>
      <path d="M15 8 C13 6, 10 6, 10 9 C10 12, 15 17, 15 17 C15 17, 20 12, 20 9 C20 6, 17 6, 15 8" fill="#FFE4E6" opacity="0.3"/>
    </pattern>
  </defs>
  
  <!-- 背景 -->
  <circle cx="72" cy="72" r="70" fill="url(#sealGradient3)" filter="url(#authenticShadow3)"/>
  
  <!-- 文档主体 -->
  <rect x="28" y="32" width="88" height="80" rx="6" fill="url(#documentGradient3)" filter="url(#authenticShadow3)"/>
  
  <!-- 水印背景 -->
  <rect x="28" y="32" width="88" height="80" rx="6" fill="url(#watermark3)" opacity="0.5"/>
  
  <!-- 文档头部 -->
  <rect x="36" y="44" width="72" height="4" rx="2" fill="#FF69B4"/>
  <rect x="36" y="52" width="56" height="2" rx="1" fill="#FFB6C1"/>
  <rect x="36" y="56" width="48" height="2" rx="1" fill="#FFB6C1"/>
  
  <!-- 主要印章 -->
  <circle cx="72" cy="78" r="18" fill="#FF1744" opacity="0.9" filter="url(#authenticShadow3)"/>
  <circle cx="72" cy="78" r="15" fill="none" stroke="#FFFFFF" stroke-width="1"/>
  <circle cx="72" cy="78" r="12" fill="none" stroke="#FFFFFF" stroke-width="0.5"/>
  
  <!-- 印章内容 -->
  <text x="72" y="76" text-anchor="middle" font-family="serif" font-size="8" font-weight="bold" fill="#FFFFFF">情侣</text>
  <text x="72" y="84" text-anchor="middle" font-family="serif" font-size="8" font-weight="bold" fill="#FFFFFF">信誉</text>
  
  <!-- 副印章（小） -->
  <circle cx="96" cy="96" r="8" fill="#FF69B4" opacity="0.8"/>
  <text x="96" y="100" text-anchor="middle" font-family="serif" font-size="6" fill="#FFFFFF">券</text>
  
  <!-- 装饰元素 -->
  <path d="M45 70 C43 68, 40 68, 40 71 C40 74, 45 79, 45 79 C45 79, 50 74, 50 71 C50 68, 47 68, 45 70" fill="#FF69B4" opacity="0.6"/>
  <path d="M99 70 C97 68, 94 68, 94 71 C94 74, 99 79, 99 79 C99 79, 104 74, 104 71 C104 68, 101 68, 99 70" fill="#FF69B4" opacity="0.6"/>
  
  <!-- 底部签名线 -->
  <line x1="40" y1="100" x2="70" y2="100" stroke="#FFB6C1" stroke-width="1" stroke-dasharray="2,2"/>
  <text x="55" y="106" text-anchor="middle" font-family="serif" font-size="6" fill="#999">授权签名</text>
</svg>

<!-- 方案4：情侣对称设计 - 高端品牌风 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="brandGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FF8A80"/>
      <stop offset="25%" stop-color="#FFAB91"/>
      <stop offset="75%" stop-color="#FFCC9A"/>
      <stop offset="100%" stop-color="#FFE0B2"/>
    </linearGradient>
    <linearGradient id="premiumCard4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFFFFF"/>
      <stop offset="50%" stop-color="#FAFAFA"/>
      <stop offset="100%" stop-color="#F5F5F5"/>
    </linearGradient>
    <filter id="brandShadow4">
      <feDropShadow dx="5" dy="7" stdDeviation="15" flood-opacity="0.18"/>
    </filter>
    <linearGradient id="roseGold4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFD700"/>
      <stop offset="50%" stop-color="#FF69B4"/>
      <stop offset="100%" stop-color="#FF1744"/>
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="144" height="144" rx="32" fill="url(#brandGradient4)" filter="url(#brandShadow4)"/>
  
  <!-- 主卡片 -->
  <rect x="20" y="28" width="104" height="88" rx="16" fill="url(#premiumCard4)" filter="url(#brandShadow4)"/>
  
  <!-- 玫瑰金装饰框 -->
  <rect x="20" y="28" width="104" height="88" rx="16" fill="none" stroke="url(#roseGold4)" stroke-width="3"/>
  
  <!-- 顶部装饰条 -->
  <rect x="32" y="40" width="80" height="8" rx="4" fill="url(#roseGold4)"/>
  
  <!-- 对称设计 - 左侧（她） -->
  <circle cx="52" cy="72" r="16" fill="#FFE4E6" stroke="#FF69B4" stroke-width="2"/>
  <circle cx="52" cy="68" r="4" fill="#FF69B4"/>
  <path d="M47 78 Q52 82, 57 78" stroke="#FF69B4" stroke-width="2" fill="none"/>
  <path d="M48 65 Q49 63, 50 65" stroke="#FF69B4" stroke-width="1" fill="none"/>
  <path d="M54 65 Q55 63, 56 65" stroke="#FF69B4" stroke-width="1" fill="none"/>
  <ellipse cx="48" cy="74" rx="2" ry="1" fill="#FFB6C1"/>
  <ellipse cx="56" cy="74" rx="2" ry="1" fill="#FFB6C1"/>
  
  <!-- 对称设计 - 右侧（他） -->
  <circle cx="92" cy="72" r="16" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
  <circle cx="92" cy="68" r="4" fill="#2196F3"/>
  <path d="M87 78 Q92 82, 97 78" stroke="#2196F3" stroke-width="2" fill="none"/>
  <rect x="89" y="64" width="6" height="2" rx="1" fill="#2196F3"/>
  
  <!-- 中央连接元素 -->
  <rect x="68" y="70" width="8" height="4" rx="2" fill="url(#roseGold4)"/>
  <path d="M72 60 C70 58, 67 58, 67 61 C67 64, 72 69, 72 69 C72 69, 77 64, 77 61 C77 58, 74 58, 72 60" fill="#FF1744"/>
  
  <!-- 底部文字区域 -->
  <rect x="32" y="96" width="80" height="3" rx="1.5" fill="#FF69B4"/>
  <rect x="32" y="102" width="60" height="2" rx="1" fill="#FFB6C1" opacity="0.7"/>
  
  <!-- 角落装饰 -->
  <circle cx="36" cy="44" r="2" fill="url(#roseGold4)"/>
  <circle cx="108" cy="44" r="2" fill="url(#roseGold4)"/>
  <circle cx="36" cy="100" r="2" fill="url(#roseGold4)"/>
  <circle cx="108" cy="100" r="2" fill="url(#roseGold4)"/>
</svg>

<!-- 方案5：现代极简风格 - 苹果设计语言 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="appleGradient5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#F8F9FA"/>
      <stop offset="50%" stop-color="#E9ECEF"/>
      <stop offset="100%" stop-color="#DEE2E6"/>
    </linearGradient>
    <linearGradient id="glassCard5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFFFFF" stop-opacity="0.95"/>
      <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0.85"/>
    </linearGradient>
    <filter id="modernShadow5">
      <feDropShadow dx="0" dy="8" stdDeviation="20" flood-opacity="0.12"/>
    </filter>
    <linearGradient id="accentColor5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FF6B6B"/>
      <stop offset="50%" stop-color="#FF8E8E"/>
      <stop offset="100%" stop-color="#FFB3B3"/>
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="144" height="144" rx="40" fill="url(#appleGradient5)"/>
  
  <!-- 玻璃态卡片 -->
  <rect x="24" y="32" width="96" height="80" rx="20" fill="url(#glassCard5)" filter="url(#modernShadow5)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  
  <!-- 顶部图标区域 -->
  <circle cx="72" cy="60" r="20" fill="url(#accentColor5)" opacity="0.1"/>
  <circle cx="72" cy="60" r="16" fill="url(#accentColor5)" opacity="0.2"/>
  
  <!-- 极简情侣图标 -->
  <circle cx="66" cy="58" r="6" fill="none" stroke="url(#accentColor5)" stroke-width="2"/>
  <circle cx="78" cy="58" r="6" fill="none" stroke="url(#accentColor5)" stroke-width="2"/>
  <path d="M72 52 C70 50, 67 50, 67 53 C67 56, 72 61, 72 61 C72 61, 77 56, 77 53 C77 50, 74 50, 72 52" fill="url(#accentColor5)"/>
  
  <!-- 文字区域 -->
  <rect x="40" y="84" width="64" height="4" rx="2" fill="url(#accentColor5)" opacity="0.8"/>
  <rect x="40" y="92" width="48" height="2" rx="1" fill="url(#accentColor5)" opacity="0.5"/>
  <rect x="40" y="96" width="40" height="2" rx="1" fill="url(#accentColor5)" opacity="0.5"/>
  
  <!-- 现代装饰元素 -->
  <circle cx="48" cy="48" r="2" fill="url(#accentColor5)" opacity="0.3"/>
  <circle cx="96" cy="48" r="2" fill="url(#accentColor5)" opacity="0.3"/>
  <circle cx="48" cy="96" r="2" fill="url(#accentColor5)" opacity="0.3"/>
  <circle cx="96" cy="96" r="2" fill="url(#accentColor5)" opacity="0.3"/>
  
  <!-- 底部状态指示 -->
  <rect x="60" y="104" width="24" height="3" rx="1.5" fill="url(#accentColor5)" opacity="0.4"/>
  <circle cx="66" cy="105.5" r="1" fill="url(#accentColor5)"/>
  <circle cx="72" cy="105.5" r="1" fill="url(#accentColor5)"/>
  <circle cx="78" cy="105.5" r="1" fill="url(#accentColor5)" opacity="0.5"/>
</svg> 