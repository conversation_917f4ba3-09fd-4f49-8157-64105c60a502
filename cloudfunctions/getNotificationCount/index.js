const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 获取未读通知数量
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 获取未读通知数量 - 修复：使用正确的字段名
    const unreadCount = await db.collection('notifications')
      .where({
        recipientId: openid,
        isRead: false
      })
      .count()
    
    // 获取总通知数量 - 修复：使用正确的字段名
    const totalCount = await db.collection('notifications')
      .where({
        recipientId: openid
      })
      .count()
    
    return {
      success: true,
      data: {
        unreadCount: unreadCount.total,
        totalCount: totalCount.total
      }
    }
    
  } catch (error) {
    console.error('获取通知数量失败:', error)
    return {
      success: false,
      error: 'SYSTEM_ERROR',
      message: '系统错误，请稍后重试',
      data: {
        unreadCount: 0,
        totalCount: 0
      }
    }
  }
} 