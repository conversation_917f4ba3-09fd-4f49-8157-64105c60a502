<svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E6E6FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DDA0DD;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="40" cy="40" r="40" fill="url(#gradient2)"/>
  <circle cx="30" cy="35" r="8" fill="white" opacity="0.8"/>
  <circle cx="50" cy="35" r="8" fill="white" opacity="0.8"/>
  <path d="M25 50 Q40 65 55 50" stroke="white" stroke-width="3" fill="none" opacity="0.8"/>
</svg> 