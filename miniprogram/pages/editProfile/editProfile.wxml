<!--pages/editProfile/editProfile.wxml-->
<view class="container" bindtap="onClickOutside">
  <!-- 主要内容区域 -->
  <view class="main-content">
    
    <!-- 头像卡片 -->
    <view class="profile-card avatar-card" catchtap="preventBubble">
      <view class="card-header">
        <view class="card-title">
          <text class="title-icon">👤</text>
          <text class="title-text">个人头像</text>
        </view>
        <!-- <view class="edit-hint">
          <text class="edit-text">点击更换</text>
          <text class="arrow-icon">→</text>
        </view> -->
      </view>
      
      <view class="card-content">
        <view class="avatar-display">
          <view class="avatar-wrapper">
            <image class="avatar-image" src="{{tempAvatar || '/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="avatar-overlay">
              <text class="overlay-text">选择一张美美的头像吧</text>
            </view>
          </view>
        </view>
        
        <!-- 直接使用button调用微信头像选择 -->
        <button class="avatar-select-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">
          <text class="btn-text">更换头像</text>
        </button>
      </view>
    </view>

    <!-- 昵称卡片 - 极简编辑模式 -->
    <view class="profile-card nickname-card" catchtap="preventBubble">
      <view class="card-header">
        <view class="card-title">
          <text class="title-icon">✨</text>
          <text class="title-text">个人昵称</text>
        </view>
        <view class="edit-hint" wx:if="{{!isEditingNickname}}">
          <!-- <text class="edit-text">点击修改</text> -->
          <!-- <text class="arrow-icon">→</text> -->
        </view>
      </view>
      
      <view class="card-content">
        <!-- 非编辑状态：显示昵称 -->
        <view wx:if="{{!isEditingNickname}}" class="nickname-display" catchtap="startEditNickname">
          <text class="nickname-text">{{tempNickname || '点击设置昵称'}}</text>
          <view class="nickname-tips">
            <text class="tip-text">在情侣券中显示的专属昵称</text>
          </view>
        </view>
        
        <!-- 编辑状态：只显示输入框 -->
        <view wx:if="{{isEditingNickname}}" class="nickname-edit-simple">
          <view class="edit-input-wrapper">
            <input 
              class="nickname-edit-input" 
              type="nickname"
              placeholder="点击输入框获取微信昵称" 
              value="{{editingNickname}}"
              bindinput="onNicknameInput"
              bindnicknamereview="onNicknameReview"
              maxlength="20"
              focus="{{true}}"
            />
            <view class="input-counter">{{editingNickname.length}}/20</view>
          </view>
          
          <view class="edit-tips-simple">
            <text class="tip-text">💡 点击外部可完成编辑</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 个性签名卡片 -->
    <view class="profile-card signature-card" catchtap="preventBubble">
      <view class="card-header">
        <view class="card-title">
          <text class="title-icon">💫</text>
          <text class="title-text">个性签名</text>
        </view>
        <view class="edit-hint" wx:if="{{!isEditingSignature}}">
          <!-- <text class="edit-text">点击修改</text> -->
          <!-- <text class="arrow-icon">→</text> -->
        </view>
      </view>
      
      <view class="card-content">
        <!-- 非编辑状态：显示个性签名 -->
        <view wx:if="{{!isEditingSignature}}" class="signature-display" catchtap="startEditSignature">
          <text class="signature-text">{{tempSignature || '设置你的个性签名吧~'}}</text>
          <view class="signature-tips">
            <text class="tip-text">展示你的个性与态度</text>
          </view>
        </view>
        
        <!-- 编辑状态：显示输入框 -->
        <view wx:if="{{isEditingSignature}}" class="signature-edit-simple">
          <view class="edit-input-wrapper">
            <input 
              class="signature-edit-input" 
              type="text"
              placeholder="输入你的个性签名" 
              value="{{editingSignature}}"
              bindinput="onSignatureInput"
              maxlength="30"
              focus="{{true}}"
            />
            <view class="input-counter">{{editingSignature.length}}/30</view>
          </view>
          
          <view class="edit-tips-simple">
            <text class="tip-text">💡 点击外部可完成编辑</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 温馨提示卡片 -->
    <view class="profile-card tips-card" catchtap="preventBubble">
      <view class="card-header">
        <view class="card-title">
          <text class="title-icon">💝</text>
          <text class="title-text">温馨提示</text>
        </view>
      </view>
      
      <view class="card-content">
        <view class="tips-list">
          <view class="tip-item">
            <text class="bullet">•</text>
            <text class="tip-text">头像和昵称将在你们的情侣券中显示</text>
          </view>
          <view class="tip-item">
            <text class="bullet">•</text>
            <text class="tip-text">只有你的另一半可以看到这些信息</text>
          </view>
          <view class="tip-item">
            <text class="bullet">•</text>
            <text class="tip-text">随时可以在这里修改你的个人资料</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部保存按钮 -->
  <view class="bottom-actions" catchtap="preventBubble">
    <button class="save-button {{isModified ? 'active' : ''}}" bindtap="saveProfile" disabled="{{!isModified}}">
      <text class="save-button-text">{{isModified ? '保存修改' : '暂无修改'}}</text>
    </button>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">保存中...</text>
    </view>
  </view>
</view> 