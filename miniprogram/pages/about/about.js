// about.js
const app = getApp();

Page({
  data: {
    appVersion: '1.0.0',
    buildNumber: '2024001',
    buildDate: '2024-01-01',
    contactEmail: '<EMAIL>',
    wechatService: 'couplelove_cs',
    officialWebsite: 'https://couplelove.app',
    stats: {
      userCount: '1000',
      coupleCount: '500',
      couponCount: '5000',
      redeemedCount: '3000'
    },
    changelogList: [
      {
        version: '1.0.0',
        date: '2024-01-01',
        changes: [
          {
            type: 'new',
            description: '正式发布情侣信任券小程序'
          },
          {
            type: 'new',
            description: '支持创建和发送信任券'
          },
          {
            type: 'new',
            description: '实现情侣绑定功能'
          },
          {
            type: 'new',
            description: '添加消息通知系统'
          }
        ]
      },
      {
        version: '0.9.0',
        date: '2023-12-15',
        changes: [
          {
            type: 'new',
            description: '添加券核销功能'
          },
          {
            type: 'improve',
            description: '优化用户界面设计'
          },
          {
            type: 'fix',
            description: '修复已知问题和BUG'
          }
        ]
      },
      {
        version: '0.8.0',
        date: '2023-12-01',
        changes: [
          {
            type: 'new',
            description: '实现基础的券创建功能'
          },
          {
            type: 'new',
            description: '添加用户登录和个人中心'
          },
          {
            type: 'improve',
            description: '完善数据库结构设计'
          }
        ]
      }
    ]
  },

  onLoad: function() {
    console.log('关于我们页面加载');
    this.loadAppInfo();
    this.loadStats();
  },

  /**
   * 加载应用信息
   */
  loadAppInfo() {
    try {
      // 获取应用版本信息
      const accountInfo = wx.getAccountInfoSync();
      const version = accountInfo.miniProgram.version || this.data.appVersion;
      
      this.setData({
        appVersion: version
      });
    } catch (error) {
      console.error('获取应用信息失败:', error);
    }
  },

  /**
   * 加载统计数据
   */
  loadStats() {
    wx.cloud.callFunction({
      name: 'getAppStats',
      data: {},
      success: (res) => {
        if (res.result.success) {
          this.setData({
            stats: res.result.data || this.data.stats
          });
        }
      },
      fail: (error) => {
        console.error('获取统计数据失败:', error);
      }
    });
  },

  /**
   * 获取变更类型标签
   */
  getChangeTypeLabel(type) {
    const labels = {
      'new': '新增',
      'fix': '修复',
      'improve': '优化'
    };
    return labels[type] || '更新';
  },

  /**
   * 复制邮箱
   */
  copyEmail() {
    this.copyText(this.data.contactEmail, '邮箱地址');
  },

  /**
   * 复制微信号
   */
  copyWechat() {
    this.copyText(this.data.wechatService, '微信客服号');
  },

  /**
   * 复制文本到剪贴板
   */
  copyText(text, label) {
    console.log('复制文本:', text);

    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: `${label}已复制`,
          icon: 'success',
          duration: 2000
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 查看隐私政策
   */
  viewPrivacyPolicy() {
    console.log('查看隐私政策');
    wx.showModal({
      title: '隐私政策',
      content: '我们非常重视您的隐私保护。详细的隐私政策请访问我们的官网查看，或在设置页面中查看完整内容。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 查看服务条款
   */
  viewTermsOfService() {
    console.log('查看服务条款');
    wx.showModal({
      title: '服务条款',
      content: '使用本应用即表示您同意遵守我们的服务条款。详细条款内容请访问官网查看。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 查看用户协议
   */
  viewUserAgreement() {
    console.log('查看用户协议');
    wx.showModal({
      title: '用户协议',
      content: '感谢您使用情侣信任券！请仔细阅读用户协议，详细内容可在官网查看。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '情侣信誉券 - 记录我们的美好约定',
      path: '/pages/index/index',
      imageUrl: '/images/share-logo.png'
    };
  }
});

