/**
 * 通用列表项组件 JS
 * 提供灵活的列表项展示和交互功能
 */

Component({
  /**
   * 组件属性列表
   */
  properties: {
    // 左侧图标 (emoji 或字符)
    icon: {
      type: String,
      value: ''
    },
    
    // 左侧图标图片URL
    iconImage: {
      type: String,
      value: ''
    },
    
    // 主标题
    title: {
      type: String,
      value: '',
      observer: 'onTitleChange'
    },
    
    // 副标题
    subtitle: {
      type: String,
      value: ''
    },
    
    // 详细描述
    description: {
      type: String,
      value: ''
    },
    
    // 右侧文本
    rightText: {
      type: String,
      value: ''
    },
    
    // 是否显示右侧箭头
    showArrow: {
      type: Boolean,
      value: false
    },
    
    // 箭头图标
    arrowIcon: {
      type: String,
      value: '›'
    },
    
    // 徽章文本
    badge: {
      type: String,
      value: ''
    },
    
    // 标签数组
    tags: {
      type: Array,
      value: []
    },
    
    // 状态 (success, warning, error, info, neutral)
    status: {
      type: String,
      value: ''
    },
    
    // 状态文本 (如果为空，会根据 status 自动生成)
    statusText: {
      type: String,
      value: ''
    },
    
    // 是否可点击
    clickable: {
      type: Boolean,
      value: true
    },
    
    // 是否禁用
    disabled: {
      type: Boolean,
      value: false
    },
    
    // 是否激活状态
    active: {
      type: Boolean,
      value: false
    },
    
    // 是否显示分隔线
    showDivider: {
      type: Boolean,
      value: false
    },
    
    // 列表项尺寸 (normal, compact, large)
    size: {
      type: String,
      value: 'normal'
    },
    
    // 自定义样式类名
    customClass: {
      type: String,
      value: ''
    },
    
    // 列表项ID，用于识别
    itemId: {
      type: String,
      value: ''
    },
    
    // 额外数据，方便在事件中传递
    extraData: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件数据
   */
  data: {
    // 状态文本映射
    statusTextMap: {
      success: '成功',
      warning: '警告',
      error: '错误',
      info: '信息',
      neutral: '默认'
    },
    
    // 图标加载状态
    iconLoading: false,
    iconLoadError: false,
    
    // 组件样式类
    itemClass: ''
  },

  /**
   * 组件方法列表
   */
  methods: {
    /**
     * 标题变化监听
     */
    onTitleChange(newVal, oldVal) {
      if (newVal !== oldVal && newVal) {
        console.log(`[ListItem] Title changed: ${newVal}`);
      }
    },

    /**
     * 列表项点击事件
     */
    onItemTap(e) {
      // 如果禁用或不可点击，则返回
      if (this.properties.disabled || !this.properties.clickable) {
        return;
      }
      
      e.stopPropagation();
      
      // 触发点击事件
      this.triggerEvent('tap', {
        itemId: this.properties.itemId,
        title: this.properties.title,
        subtitle: this.properties.subtitle,
        extraData: this.properties.extraData,
        index: this.dataset.index || 0,
        timeStamp: Date.now()
      });
      
      // 添加点击反馈
      this.addTapFeedback();
    },

    /**
     * 图标加载完成
     */
    onIconLoad(e) {
      this.setData({
        iconLoading: false,
        iconLoadError: false
      });
      
      console.log('[ListItem] Icon loaded successfully');
    },

    /**
     * 图标加载失败
     */
    onIconError(e) {
      this.setData({
        iconLoading: false,
        iconLoadError: true
      });
      
      console.error('[ListItem] Icon load failed:', e.detail);
      
      // 触发图标加载失败事件
      this.triggerEvent('iconError', {
        itemId: this.properties.itemId,
        iconImage: this.properties.iconImage,
        error: e.detail
      });
    },

    /**
     * 添加点击反馈效果
     */
    addTapFeedback() {
      // 使用微信小程序动画API
      if (typeof wx !== 'undefined' && wx.createAnimation) {
        const animation = wx.createAnimation({
          duration: 150,
          timingFunction: 'ease-out'
        });
        
        animation.scale(0.98).step();
        animation.scale(1).step({ delay: 50 });
        
        this.animate('.list-item', animation.export());
      }
      
      // 触发反馈事件
      this.triggerEvent('feedback', {
        type: 'tap',
        itemId: this.properties.itemId
      });
    },

    /**
     * 更新组件样式类
     */
    updateItemClass() {
      const classes = [];
      
      if (this.properties.size !== 'normal') {
        classes.push(this.properties.size);
      }
      
      if (this.properties.active) {
        classes.push('active');
      }
      
      if (this.properties.disabled) {
        classes.push('disabled');
      }
      
      this.setData({
        itemClass: classes.join(' ')
      });
    },

    /**
     * 获取状态显示文本
     */
    getStatusText() {
      const { status, statusText } = this.properties;
      const { statusTextMap } = this.data;
      
      if (statusText) {
        return statusText;
      }
      
      return statusTextMap[status] || '';
    },

    /**
     * 设置激活状态
     */
    setActive(active) {
      this.setData({
        active: active
      });
      this.updateItemClass();
    },

    /**
     * 设置禁用状态
     */
    setDisabled(disabled) {
      this.setData({
        disabled: disabled
      });
      this.updateItemClass();
    },

    /**
     * 更新徽章
     */
    updateBadge(badge) {
      this.setData({
        badge: badge || ''
      });
    },

    /**
     * 添加标签
     */
    addTag(tag) {
      if (!tag || this.properties.tags.includes(tag)) {
        return;
      }
      
      const newTags = [...this.properties.tags, tag];
      this.setData({
        tags: newTags
      });
    },

    /**
     * 移除标签
     */
    removeTag(tag) {
      const newTags = this.properties.tags.filter(t => t !== tag);
      this.setData({
        tags: newTags
      });
    },

    /**
     * 清空所有标签
     */
    clearTags() {
      this.setData({
        tags: []
      });
    },

    /**
     * 获取列表项完整信息
     */
    getItemInfo() {
      return {
        itemId: this.properties.itemId,
        title: this.properties.title,
        subtitle: this.properties.subtitle,
        description: this.properties.description,
        rightText: this.properties.rightText,
        badge: this.properties.badge,
        tags: this.properties.tags,
        status: this.properties.status,
        extraData: this.properties.extraData,
        active: this.properties.active,
        disabled: this.properties.disabled,
        clickable: this.properties.clickable
      };
    }
  },

  /**
   * 生命周期函数
   */
  lifetimes: {
    /**
     * 组件实例被创建
     */
    created() {
      console.log('[ListItem] Component created');
    },

    /**
     * 组件实例进入页面节点树
     */
    attached() {
      console.log('[ListItem] Component attached');
      
      // 初始化样式类
      this.updateItemClass();
      
      // 设置图标加载状态
      if (this.properties.iconImage) {
        this.setData({
          iconLoading: true
        });
      }
    },

    /**
     * 组件实例被从页面节点树移除
     */
    detached() {
      console.log('[ListItem] Component detached');
    }
  },

  /**
   * 组件数据字段监听器
   */
  observers: {
    /**
     * 监听状态相关属性变化
     */
    'active, disabled, size': function(active, disabled, size) {
      this.updateItemClass();
    },

    /**
     * 监听状态变化，更新状态文本
     */
    'status, statusText': function(status, statusText) {
      const displayText = this.getStatusText();
      if (displayText !== statusText) {
        this.setData({
          statusText: displayText
        });
      }
    },

    /**
     * 监听图标图片变化
     */
    'iconImage': function(iconImage) {
      if (iconImage) {
        this.setData({
          iconLoading: true,
          iconLoadError: false
        });
      } else {
        this.setData({
          iconLoading: false,
          iconLoadError: false
        });
      }
    }
  },

  /**
   * 组件选项
   */
  options: {
    // 启用多插槽支持
    multipleSlots: true,
    // 使用纯数据字段优化性能
    pureDataPattern: /^_/,
    // 样式隔离
    styleIsolation: 'isolated'
  },

  /**
   * 外部样式类
   */
  externalClasses: [
    'custom-class',
    'item-class',
    'icon-class',
    'content-class',
    'title-class',
    'right-class'
  ]
});
