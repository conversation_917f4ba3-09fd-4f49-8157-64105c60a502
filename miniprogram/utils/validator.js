/**
 * 表单验证工具函数
 * 提供常用的验证规则，如非空、邮箱格式、手机号格式等
 */

/**
 * 验证是否为空
 * @param {*} value - 要验证的值
 * @returns {boolean} 是否非空
 */
function validateRequired(value) {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value.trim().length > 0;
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object') return Object.keys(value).length > 0;
    return true;
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效邮箱
 */
function validateEmail(email) {
    if (!email || typeof email !== 'string') return false;
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
}

/**
 * 验证手机号格式（中国大陆）
 * @param {string} phone - 手机号
 * @returns {boolean} 是否为有效手机号
 */
function validatePhone(phone) {
    if (!phone || typeof phone !== 'string') return false;
    
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone.trim());
}

/**
 * 验证密码强度
 * @param {string} password - 密码
 * @param {Object} options - 验证选项
 * @returns {Object} 验证结果
 */
function validatePassword(password, options = {}) {
    const {
        minLength = 6,
        maxLength = 20,
        requireUppercase = false,
        requireLowercase = false,
        requireNumbers = false,
        requireSpecialChars = false
    } = options;
    
    const result = {
        valid: true,
        errors: []
    };
    
    if (!password || typeof password !== 'string') {
        result.valid = false;
        result.errors.push('密码不能为空');
        return result;
    }
    
    if (password.length < minLength) {
        result.valid = false;
        result.errors.push(`密码长度不能少于${minLength}位`);
    }
    
    if (password.length > maxLength) {
        result.valid = false;
        result.errors.push(`密码长度不能超过${maxLength}位`);
    }
    
    if (requireUppercase && !/[A-Z]/.test(password)) {
        result.valid = false;
        result.errors.push('密码必须包含大写字母');
    }
    
    if (requireLowercase && !/[a-z]/.test(password)) {
        result.valid = false;
        result.errors.push('密码必须包含小写字母');
    }
    
    if (requireNumbers && !/\d/.test(password)) {
        result.valid = false;
        result.errors.push('密码必须包含数字');
    }
    
    if (requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
        result.valid = false;
        result.errors.push('密码必须包含特殊字符');
    }
    
    return result;
}

/**
 * 验证字符串长度
 * @param {string} str - 要验证的字符串
 * @param {number} min - 最小长度
 * @param {number} max - 最大长度
 * @returns {Object} 验证结果
 */
function validateLength(str, min, max) {
    const result = {
        valid: true,
        errors: []
    };
    
    if (typeof str !== 'string') {
        result.valid = false;
        result.errors.push('输入必须是字符串');
        return result;
    }
    
    const length = str.trim().length;
    
    if (length < min) {
        result.valid = false;
        result.errors.push(`长度不能少于${min}个字符`);
    }
    
    if (length > max) {
        result.valid = false;
        result.errors.push(`长度不能超过${max}个字符`);
    }
    
    return result;
}

/**
 * 验证数字范围
 * @param {number} num - 要验证的数字
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {boolean} 是否在范围内
 */
function validateNumberRange(num, min, max) {
    if (typeof num !== 'number' || isNaN(num)) return false;
    return num >= min && num <= max;
}

/**
 * 验证URL格式
 * @param {string} url - URL
 * @returns {boolean} 是否为有效URL
 */
function validateURL(url) {
    if (!url || typeof url !== 'string') return false;
    
    try {
        new URL(url);
        return true;
    } catch (error) {
        return false;
    }
}

/**
 * 验证身份证号码（中国）
 * @param {string} idCard - 身份证号码
 * @returns {boolean} 是否为有效身份证号
 */
function validateIdCard(idCard) {
    if (!idCard || typeof idCard !== 'string') return false;
    
    // 18位身份证号码正则
    const idCardRegex = /^\d{17}[\dXx]$/;
    if (!idCardRegex.test(idCard)) return false;
    
    // 校验位验证
    const coefficients = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    
    let sum = 0;
    for (let i = 0; i < 17; i++) {
        sum += parseInt(idCard[i]) * coefficients[i];
    }
    
    const checkCode = checkCodes[sum % 11];
    return checkCode === idCard[17].toUpperCase();
}

/**
 * 验证券名称格式
 * @param {string} name - 券名称
 * @returns {Object} 验证结果
 */
function validateCouponName(name) {
    const result = {
        valid: true,
        errors: []
    };
    
    if (!validateRequired(name)) {
        result.valid = false;
        result.errors.push('券名称不能为空');
        return result;
    }
    
    const lengthValidation = validateLength(name, 1, 20);
    if (!lengthValidation.valid) {
        result.valid = false;
        result.errors.push(...lengthValidation.errors);
    }
    
    // 检查是否包含敏感词
    const sensitiveWords = ['色情', '暴力', '赌博', '违法'];
    const containsSensitive = sensitiveWords.some(word => name.includes(word));
    if (containsSensitive) {
        result.valid = false;
        result.errors.push('券名称包含敏感词');
    }
    
    return result;
}

/**
 * 验证券描述格式
 * @param {string} description - 券描述
 * @returns {Object} 验证结果
 */
function validateCouponDescription(description) {
    const result = {
        valid: true,
        errors: []
    };
    
    if (!validateRequired(description)) {
        result.valid = false;
        result.errors.push('券描述不能为空');
        return result;
    }
    
    const lengthValidation = validateLength(description, 5, 100);
    if (!lengthValidation.valid) {
        result.valid = false;
        result.errors.push(...lengthValidation.errors);
    }
    
    return result;
}

/**
 * 验证日期格式和有效性
 * @param {string|Date} date - 日期
 * @param {boolean} isFuture - 是否必须是未来日期
 * @returns {Object} 验证结果
 */
function validateDate(date, isFuture = false) {
    const result = {
        valid: true,
        errors: []
    };
    
    if (!date) {
        result.valid = false;
        result.errors.push('日期不能为空');
        return result;
    }
    
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
        result.valid = false;
        result.errors.push('日期格式无效');
        return result;
    }
    
    if (isFuture && dateObj <= new Date()) {
        result.valid = false;
        result.errors.push('日期必须是未来时间');
    }
    
    return result;
}

/**
 * 综合表单验证器
 * @param {Object} data - 要验证的数据
 * @param {Object} rules - 验证规则
 * @returns {Object} 验证结果
 */
function validateForm(data, rules) {
    const result = {
        valid: true,
        errors: {},
        messages: []
    };
    
    for (const [field, fieldRules] of Object.entries(rules)) {
        const value = data[field];
        const fieldErrors = [];
        
        for (const rule of fieldRules) {
            let isValid = false;
            
            switch (rule.type) {
                case 'required':
                    isValid = validateRequired(value);
                    if (!isValid) fieldErrors.push(rule.message || `${field}不能为空`);
                    break;
                    
                case 'email':
                    if (value) {
                        isValid = validateEmail(value);
                        if (!isValid) fieldErrors.push(rule.message || '邮箱格式不正确');
                    }
                    break;
                    
                case 'phone':
                    if (value) {
                        isValid = validatePhone(value);
                        if (!isValid) fieldErrors.push(rule.message || '手机号格式不正确');
                    }
                    break;
                    
                case 'length':
                    if (value) {
                        const lengthResult = validateLength(value, rule.min, rule.max);
                        isValid = lengthResult.valid;
                        if (!isValid) fieldErrors.push(...lengthResult.errors);
                    }
                    break;
                    
                default:
                    if (rule.validator && typeof rule.validator === 'function') {
                        isValid = rule.validator(value);
                        if (!isValid) fieldErrors.push(rule.message || '验证失败');
                    }
            }
        }
        
        if (fieldErrors.length > 0) {
            result.valid = false;
            result.errors[field] = fieldErrors;
            result.messages.push(...fieldErrors);
        }
    }
    
    return result;
}
