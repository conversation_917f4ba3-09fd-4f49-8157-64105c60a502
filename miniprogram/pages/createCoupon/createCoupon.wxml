<!--pages/createCoupon/createCoupon.wxml-->
<view class="container" style="background: {{themeGradient}}">
  <view class="form-preview-container">
    <!-- Coupon Preview -->
    <view class="coupon-preview-wrapper card">
      <view class="coupon-preview" style="background: {{selectedColor}}; height: 397rpx; display: flex; box-sizing: border-box; left: 0rpx; top: 0rpx; width: 649rpx">
        <view class="preview-number" style="position: absolute; left: 507rpx; top: 24rpx">No.{{previewCouponNumber}}</view>
        <view class="preview-type-tag">{{selectedCouponTypeText}}</view>
        <view class="preview-icon">{{selectedIcon}}</view>
        <text class="preview-title" style="position: relative; left: 0rpx; top: -14rpx">{{name || '券标题'}}</text>
        <text class="preview-description" style="position: relative; left: -2rpx; top: 2rpx">{{description || '券的相关描述内容...'}}</text>
        <view class="preview-expiry" style="width: 266rpx; display: block; box-sizing: border-box; position: relative; left: 329rpx; top: 62rpx">
          有效期至: {{expiresAtDate ? expiresAtDate + ' ' + expiresAtTime : '长期有效'}}
        </view>
        <view class="preview-sender" style="width: 141rpx; display: block; box-sizing: border-box; position: relative; left: -8rpx; top: 22rpx">From: {{userInfo.nickName || '你'}}</view>
      </view>
    </view>

    <!-- Form -->
    <view class="form-card card">
      <!-- 券类型选择器 -->
      <view class="form-group">
        <label class="form-label">券类型 <text class="required-star">*</text></label>
        <view class="coupon-type-selector">
          <view class="type-tag {{selectedCouponType === item.value ? 'selected' : ''}} type-{{item.value}}" 
                wx:for="{{couponTypes}}" 
                wx:key="value" 
                data-type="{{item.value}}" 
                bindtap="selectCouponType">
            <view class="type-icon">{{item.icon}}</view>
            <view class="type-name">{{item.name}}</view>
          </view>
        </view>
        <view wx:if="{{formErrors.couponType}}" class="error-message">{{formErrors.couponType}}</view>
      </view>

      <view class="form-group">
        <label class="form-label">券标题 <text class="required-star">*</text></label>
        <input class="form-input" type="text" value="{{name}}" data-field="name" bindinput="onInput" placeholder="例如：爱心早餐券" maxlength="20"/>
        <view wx:if="{{formErrors.name}}" class="error-message">{{formErrors.name}}</view>
      </view>

      <view class="form-group">
        <label class="form-label">券描述 <text class="required-star">*</text></label>
        <textarea class="form-textarea" value="{{description}}" data-field="description" bindinput="onInput" placeholder="详细说明这张券的用途和规则" maxlength="100"/>
        <view wx:if="{{formErrors.description}}" class="error-message">{{formErrors.description}}</view>
      </view>

      <view class="form-group inline-group">
        <view class="half-group">
            <label class="form-label">有效期至 (可选)</label>
            <picker mode="date" value="{{expiresAtDate}}" start="{{minExpiryDate}}" bindchange="onDateChange">
            <view class="form-input picker-input">{{expiresAtDate || '选择日期'}}</view>
            </picker>
            <view wx:if="{{formErrors.expiresAtDate}}" class="error-message">{{formErrors.expiresAtDate}}</view>
        </view>
        <view class="half-group">
            <label class="form-label">时间</label>
             <picker mode="time" value="{{expiresAtTime}}" start="00:00" end="23:59" bindchange="onTimeChange">
            <view class="form-input picker-input">{{expiresAtTime}}</view>
            </picker>
        </view>
      </view>

      <view class="customization-section">
        <view class="custom-item" bindtap="toggleIconPicker">
          <text class="custom-label">选择图标:</text>
          <view class="selected-display icon-display">{{selectedIcon}}</view>
        </view>
        <view class="custom-item" bindtap="toggleColorPicker">
          <text class="custom-label">选择颜色:</text>
          <view class="selected-display color-display" style="background:{{selectedColor}};"></view>
        </view>
      </view>

      <!-- Icon Picker -->
      <view class="picker-container card" wx:if="{{showIconPicker}}">
        <view class="picker-grid">
          <view class="picker-option icon-option" wx:for="{{predefinedIcons}}" wx:key="*this" data-icon="{{item}}" bindtap="selectIcon">
            {{item}}
          </view>
        </view>
      </view>
      
      <!-- Color Picker -->
      <view class="picker-container card" wx:if="{{showColorPicker}}">
        <view class="picker-grid color-picker-grid">
          <view class="picker-option color-option-wrapper" wx:for="{{predefinedColors}}" wx:key="value" data-color="{{item.value}}" bindtap="selectColor">
            <view class="color-option" style="background: {{item.value}};"></view>
            <text class="color-name">{{item.name}}</text>
          </view>
        </view>
      </view>

      <button class="primary-button submit-btn" bindtap="handleSubmit" loading="{{isSubmitting}}" disabled="{{isSubmitting}}">
        {{isSubmitting ? '发送中...' : '发送给TA'}}
      </button>
    </view>
  </view>
</view>
