// Content for the Interaction Logic section

function renderInteractionLogic() {
    return `
        <div class="mb-6">
            <p class="mb-4">本章节详细描述了情侣信誉券小程序的核心交互逻辑，包括用户行为路径、状态转换以及关键交互时序。理解这些逻辑有助于开发过程中实现一致、流畅的用户体验。</p>
            
            <h3 class="text-xl font-bold text-pink-800 mb-4">伴侣绑定交互时序图</h3>
            
            <p class="mb-4">下图展示了用户A发起绑定到用户B接受绑定的完整交互时序：</p>
            
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6 overflow-x-auto">
                <table class="w-full text-sm">
                    <thead>
                        <tr class="border-b border-gray-300">
                            <th class="p-2 text-left">参与者</th>
                            <th class="p-2 text-left text-pink-700">用户A</th>
                            <th class="p-2 text-left text-blue-700">用户B</th>
                            <th class="p-2 text-left text-green-700">小程序系统</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-b border-gray-200">
                            <td class="p-2 align-top">1</td>
                            <td class="p-2 align-top bg-pink-50">发起绑定 ➝</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top"></td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="p-2 align-top">2</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top bg-green-50">生成唯一邀请链接/码</td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="p-2 align-top">3</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top bg-green-50">➝ 返回邀请链接/码</td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="p-2 align-top">4</td>
                            <td class="p-2 align-top bg-pink-50">分享邀请链接/码 (通过微信等) ➝</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top"></td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="p-2 align-top">5</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top bg-blue-50">➝ 访问邀请链接/码</td>
                            <td class="p-2 align-top"></td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="p-2 align-top">6</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top bg-green-50">验证邀请有效性, 识别用户B</td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="p-2 align-top">7</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top bg-green-50">➝ 显示绑定确认页</td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="p-2 align-top">8</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top bg-blue-50">确认绑定 ➝</td>
                            <td class="p-2 align-top"></td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="p-2 align-top">9</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top bg-green-50">更新用户A和用户B记录<br>(设置partner_id)</td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="p-2 align-top">10</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top bg-green-50">绑定成功</td>
                        </tr>
                        <tr>
                            <td class="p-2 align-top">11</td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top"></td>
                            <td class="p-2 align-top bg-green-50">➝ 通知用户A绑定成功</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <h3 class="text-xl font-bold text-pink-800 mb-4">核心状态转换逻辑</h3>
            
            <div class="mb-6">
                <p class="mb-3">信誉券在其生命周期中有多种状态，以下是信誉券的状态转换规则：</p>
                
                <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200 mb-4">
                    <h4 class="font-semibold text-lg mb-3">信誉券状态定义</h4>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="py-2 px-3 text-left border-b border-gray-200">状态</th>
                                    <th class="py-2 px-3 text-left border-b border-gray-200">说明</th>
                                    <th class="py-2 px-3 text-left border-b border-gray-200">可能的下一状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-100">
                                    <td class="py-3 px-3 text-yellow-700 font-medium">pending (待发送)</td>
                                    <td class="py-3 px-3">券已创建但尚未发送给伴侣</td>
                                    <td class="py-3 px-3">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                                            <span>pending (待使用) - 已发送</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-100">
                                    <td class="py-3 px-3 text-blue-700 font-medium">pending (待使用)</td>
                                    <td class="py-3 px-3">券已发送给伴侣，等待使用</td>
                                    <td class="py-3 px-3">
                                        <div class="flex items-center mb-2">
                                            <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                            <span>used (已使用) - 被核销</span>
                                        </div>
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                                            <span>expired (已过期) - 超过有效期</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-100">
                                    <td class="py-3 px-3 text-green-700 font-medium">used (已使用)</td>
                                    <td class="py-3 px-3">券已被接收方核销使用</td>
                                    <td class="py-3 px-3 text-gray-500">终态，不再变化</td>
                                </tr>
                                <tr>
                                    <td class="py-3 px-3 text-red-700 font-medium">expired (已过期)</td>
                                    <td class="py-3 px-3">券超过了设定的有效期</td>
                                    <td class="py-3 px-3 text-gray-500">终态，不再变化</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                    <h4 class="font-semibold text-lg mb-3">状态转换触发条件</h4>
                    
                    <div class="mb-3">
                        <div class="font-medium text-blue-800 mb-1">1. pending(待发送) → pending(待使用)</div>
                        <ul class="list-disc pl-5 text-sm">
                            <li>触发条件：用户在创建券后点击"发送"按钮，选择接收人并确认</li>
                            <li>系统操作：更新券记录的recipient_id字段，状态保持pending但意义变化</li>
                        </ul>
                    </div>
                    
                    <div class="mb-3">
                        <div class="font-medium text-green-800 mb-1">2. pending(待使用) → used(已使用)</div>
                        <ul class="list-disc pl-5 text-sm">
                            <li>触发条件：接收方用户在券详情页点击"使用此券/核销"按钮</li>
                            <li>系统操作：更新券状态为used，记录used_time为当前时间</li>
                        </ul>
                    </div>
                    
                    <div class="mb-3">
                        <div class="font-medium text-red-800 mb-1">3. pending(待使用) → expired(已过期)</div>
                        <ul class="list-disc pl-5 text-sm">
                            <li>触发条件：系统定时任务检查发现当前时间超过券的expiration_date</li>
                            <li>系统操作：更新券状态为expired</li>
                        </ul>
                    </div>
                    
                    <div class="bg-blue-50 p-3 rounded-lg mt-4">
                        <p class="text-sm text-blue-800"><i class="fas fa-info-circle mr-1"></i> <strong>实现说明：</strong> 对于过期状态，可以通过以下两种方式实现：</p>
                        <ul class="list-disc pl-5 text-sm text-blue-800 mt-1">
                            <li>前端实时判断：在展示券列表/详情时，前端根据当前时间和券的expiration_date比较，若已过期则显示为"已过期"状态</li>
                            <li>后端定时更新：设置云函数定时器（如每日0点），批量检查并更新过期券的状态</li>
                        </ul>
                        <p class="text-sm text-blue-800 mt-2">MVP阶段推荐采用前端实时判断方式，实现简单且用户体验一致。</p>
                    </div>
                </div>
            </div>
            
            <h3 class="text-xl font-bold text-pink-800 mb-4">核心页面交互流程</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                            <i class="fas fa-plus-circle text-purple-600"></i>
                        </div>
                        <h4 class="font-semibold text-lg">创建与发送券</h4>
                    </div>
                    
                    <ol class="list-decimal pl-5 text-sm space-y-2">
                        <li>用户点击底部"发券"tab或首页的"创建新券"按钮</li>
                        <li>系统检查用户是否已绑定伴侣
                            <ul class="list-disc pl-5 mt-1 text-xs text-gray-600">
                                <li>若未绑定，提示需先绑定伴侣，并引导至伴侣绑定页</li>
                                <li>若已绑定，进入创建券页面</li>
                            </ul>
                        </li>
                        <li>用户填写券信息（名称、类型、描述、有效期等）</li>
                        <li>用户点击"创建并发送"按钮
                            <ul class="list-disc pl-5 mt-1 text-xs text-gray-600">
                                <li>系统进行表单验证（必填项、格式等）</li>
                                <li>若验证失败，显示相应错误提示</li>
                                <li>若验证通过，弹出确认弹窗</li>
                            </ul>
                        </li>
                        <li>用户确认后，系统创建券记录并更新状态</li>
                        <li>显示创建成功提示，并询问是否返回首页或继续创建</li>
                    </ol>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                        <h4 class="font-semibold text-lg">核销券</h4>
                    </div>
                    
                    <ol class="list-decimal pl-5 text-sm space-y-2">
                        <li>用户在"我收到的券"列表中点击状态为"待使用"的券</li>
                        <li>系统加载并显示券详情页，包含完整券信息</li>
                        <li>系统检查券是否过期
                            <ul class="list-disc pl-5 mt-1 text-xs text-gray-600">
                                <li>若已过期，显示"已过期"状态，不提供核销按钮</li>
                                <li>若未过期，显示"使用此券/核销"按钮</li>
                            </ul>
                        </li>
                        <li>用户点击"使用此券/核销"按钮
                            <ul class="list-disc pl-5 mt-1 text-xs text-gray-600">
                                <li>系统弹出确认对话框，提示核销操作不可撤销</li>
                            </ul>
                        </li>
                        <li>用户确认后，系统更新券状态为"已使用"，记录核销时间</li>
                        <li>系统显示核销成功动画/效果，更新页面显示状态</li>
                        <li>可选：提示用户查看历史记录或返回列表页</li>
                    </ol>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <i class="fas fa-list-alt text-blue-600"></i>
                        </div>
                        <h4 class="font-semibold text-lg">列表浏览与状态筛选</h4>
                    </div>
                    
                    <ol class="list-decimal pl-5 text-sm space-y-2">
                        <li>用户在首页切换"我发出的"/"我收到的"卡片或Tab</li>
                        <li>系统根据选择加载并显示相应列表数据</li>
                        <li>用户可点击顶部状态筛选器（全部/待使用/已使用/已过期）
                            <ul class="list-disc pl-5 mt-1 text-xs text-gray-600">
                                <li>系统根据筛选条件更新列表显示</li>
                                <li>若筛选结果为空，显示相应的空状态提示</li>
                            </ul>
                        </li>
                        <li>用户下拉刷新列表
                            <ul class="list-disc pl-5 mt-1 text-xs text-gray-600">
                                <li>系统重新从服务器获取最新数据</li>
                                <li>更新列表显示，包括可能的状态变化</li>
                            </ul>
                        </li>
                    </ol>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-5 border border-gray-200">
                    <div class="flex items-center mb-3">
                        <div class="w-10 h-10 rounded-full bg-pink-100 flex items-center justify-center mr-3">
                            <i class="fas fa-user-friends text-pink-600"></i>
                        </div>
                        <h4 class="font-semibold text-lg">伴侣解绑与重新绑定</h4>
                    </div>
                    
                    <ol class="list-decimal pl-5 text-sm space-y-2">
                        <li>用户在"我的"页面点击"伴侣信息"，进入伴侣管理页</li>
                        <li>用户点击"解除绑定"按钮
                            <ul class="list-disc pl-5 mt-1 text-xs text-gray-600">
                                <li>系统显示警告弹窗，提醒解绑后相关券数据影响</li>
                            </ul>
                        </li>
                        <li>用户确认解绑
                            <ul class="list-disc pl-5 mt-1 text-xs text-gray-600">
                                <li>系统更新双方用户记录，清除partner_id字段</li>
                                <li>可选：系统更新现有券状态（如设为无效）</li>
                            </ul>
                        </li>
                        <li>系统显示解绑成功提示，页面更新为未绑定状态</li>
                        <li>用户可点击"邀请/绑定伴侣"按钮，重新开始绑定流程</li>
                    </ol>
                    
                    <div class="bg-yellow-50 p-3 rounded-lg mt-3">
                        <p class="text-xs text-yellow-800"><i class="fas fa-exclamation-triangle mr-1"></i> <strong>注意事项：</strong> 解绑功能对已存在的券有影响，MVP可能需要简化处理或添加明确的用户提示。建议在首个版本可以不优先实现解绑功能，或实现后添加使用限制。</p>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 bg-gray-50 p-5 rounded-lg border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">交互设计原则</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="p-3 bg-white rounded-lg shadow-sm">
                        <div class="font-medium mb-2 flex items-center">
                            <i class="fas fa-bolt text-amber-500 mr-2"></i>
                            <span>即时反馈</span>
                        </div>
                        <p class="text-sm text-gray-600">用户的每次操作都应得到明确、及时的反馈，特别是在核心流程（发券、核销）中。</p>
                    </div>
                    
                    <div class="p-3 bg-white rounded-lg shadow-sm">
                        <div class="font-medium mb-2 flex items-center">
                            <i class="fas fa-hand-point-up text-blue-500 mr-2"></i>
                            <span>简化步骤</span>
                        </div>
                        <p class="text-sm text-gray-600">核心操作流程尽量控制在3-4步内完成，减少用户等待和操作成本。</p>
                    </div>
                    
                    <div class="p-3 bg-white rounded-lg shadow-sm">
                        <div class="font-medium mb-2 flex items-center">
                            <i class="fas fa-magic text-purple-500 mr-2"></i>
                            <span>情感化交互</span>
                        </div>
                        <p class="text-sm text-gray-600">在关键节点（如券核销成功）添加愉悦的动画或视觉效果，增强情感体验。</p>
                    </div>
                    
                    <div class="p-3 bg-white rounded-lg shadow-sm">
                        <div class="font-medium mb-2 flex items-center">
                            <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                            <span>错误预防</span>
                        </div>
                        <p class="text-sm text-gray-600">对不可逆操作（如核销）提供明确的确认机制，避免误操作导致的负面体验。</p>
                    </div>
                    
                    <div class="p-3 bg-white rounded-lg shadow-sm">
                        <div class="font-medium mb-2 flex items-center">
                            <i class="fas fa-history text-green-500 mr-2"></i>
                            <span>状态透明</span>
                        </div>
                        <p class="text-sm text-gray-600">券的状态变化应清晰可见，双方用户应能实时看到同步的状态更新。</p>
                    </div>
                    
                    <div class="p-3 bg-white rounded-lg shadow-sm">
                        <div class="font-medium mb-2 flex items-center">
                            <i class="fas fa-heart text-pink-500 mr-2"></i>
                            <span>情感连接</span>
                        </div>
                        <p class="text-sm text-gray-600">整体交互应强调伴侣之间的连接感，如通过共享状态和活动提醒增强情感纽带。</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}
