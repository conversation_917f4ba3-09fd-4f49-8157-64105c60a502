/**
 * 数据库操作封装工具
 * 提供通用的数据库操作方法
 */

const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 数据库操作类
 */
class Database {
  
  /**
   * 获取用户信息
   * @param {string} openid 用户OpenID
   * @returns {Object} 操作结果
   */
  static async getUser(openid) {
    try {
      const result = await db.collection('users').where({
        openid: openid
      }).get();
      
      return {
        success: true,
        data: result.data.length > 0 ? result.data[0] : null
      };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建用户
   * @param {Object} userData 用户数据
   * @returns {Object} 操作结果
   */
  static async createUser(userData) {
    try {
      const result = await db.collection('users').add({
        data: userData
      });
      
      return {
        success: true,
        data: {
          _id: result._id,
          ...userData
        }
      };
    } catch (error) {
      console.error('创建用户失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 更新用户信息
   * @param {string} userId 用户ID
   * @param {Object} updateData 更新数据
   * @returns {Object} 操作结果
   */
  static async updateUser(userId, updateData) {
    try {
      await db.collection('users').doc(userId).update({
        data: {
          ...updateData,
          updateTime: new Date()
        }
      });
      
      return {
        success: true,
        message: '用户信息更新成功'
      };
    } catch (error) {
      console.error('更新用户信息失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取情侣关系
   * @param {string} coupleId 情侣关系ID
   * @returns {Object} 操作结果
   */
  static async getCouple(coupleId) {
    try {
      const result = await db.collection('couples').doc(coupleId).get();
      
      return {
        success: true,
        data: result.data || null
      };
    } catch (error) {
      console.error('获取情侣关系失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建情侣关系
   * @param {Object} coupleData 情侣关系数据
   * @returns {Object} 操作结果
   */
  static async createCouple(coupleData) {
    try {
      const result = await db.collection('couples').add({
        data: coupleData
      });
      
      return {
        success: true,
        data: {
          _id: result._id,
          ...coupleData
        }
      };
    } catch (error) {
      console.error('创建情侣关系失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取券信息
   * @param {string} couponId 券ID
   * @returns {Object} 操作结果
   */
  static async getCoupon(couponId) {
    try {
      const result = await db.collection('coupons').doc(couponId).get();
      
      return {
        success: true,
        data: result.data || null
      };
    } catch (error) {
      console.error('获取券信息失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建券
   * @param {Object} couponData 券数据
   * @returns {Object} 操作结果
   */
  static async createCoupon(couponData) {
    try {
      const result = await db.collection('coupons').add({
        data: couponData
      });
      
      return {
        success: true,
        data: {
          _id: result._id,
          ...couponData
        }
      };
    } catch (error) {
      console.error('创建券失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 更新券状态
   * @param {string} couponId 券ID
   * @param {Object} updateData 更新数据
   * @returns {Object} 操作结果
   */
  static async updateCoupon(couponId, updateData) {
    try {
      await db.collection('coupons').doc(couponId).update({
        data: {
          ...updateData,
          updateTime: new Date()
        }
      });
      
      return {
        success: true,
        message: '券信息更新成功'
      };
    } catch (error) {
      console.error('更新券信息失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取用户的券列表
   * @param {string} openid 用户OpenID
   * @param {Object} filters 过滤条件
   * @returns {Object} 操作结果
   */
  static async getUserCoupons(openid, filters = {}) {
    try {
      let query = db.collection('coupons');
      
      // 构建查询条件
      const whereConditions = [];
      
      if (filters.type === 'sent') {
        whereConditions.push({ creatorId: openid });
      } else if (filters.type === 'received') {
        whereConditions.push({ recipientId: openid });
      } else {
        // 获取所有相关券（发送的和接收的）
        whereConditions.push(_.or([
          { creatorId: openid },
          { recipientId: openid }
        ]));
      }

      // 状态过滤
      if (filters.status && filters.status.length > 0) {
        whereConditions.push({ status: _.in(filters.status) });
      }

      // 应用查询条件
      if (whereConditions.length > 0) {
        query = query.where(_.and(whereConditions));
      }

      // 排序
      query = query.orderBy('createTime', 'desc');

      // 分页
      if (filters.limit) {
        query = query.limit(filters.limit);
      }
      if (filters.skip) {
        query = query.skip(filters.skip);
      }

      const result = await query.get();
      
      return {
        success: true,
        data: result.data
      };
    } catch (error) {
      console.error('获取用户券列表失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 创建通知
   * @param {Object} notificationData 通知数据
   * @returns {Object} 操作结果
   */
  static async createNotification(notificationData) {
    try {
      const result = await db.collection('notifications').add({
        data: notificationData
      });
      
      return {
        success: true,
        data: {
          _id: result._id,
          ...notificationData
        }
      };
    } catch (error) {
      console.error('创建通知失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取用户通知列表
   * @param {string} openid 用户OpenID
   * @param {Object} options 查询选项
   * @returns {Object} 操作结果
   */
  static async getUserNotifications(openid, options = {}) {
    try {
      let query = db.collection('notifications').where({
        recipientId: openid
      });

      // 只获取未读通知
      if (options.unreadOnly) {
        query = query.where({
          isRead: false
        });
      }

      // 排序和分页
      query = query.orderBy('createTime', 'desc');
      
      if (options.limit) {
        query = query.limit(options.limit);
      }

      const result = await query.get();
      
      return {
        success: true,
        data: result.data
      };
    } catch (error) {
      console.error('获取用户通知列表失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 标记通知为已读
   * @param {Array|string} notificationIds 通知ID或ID数组
   * @returns {Object} 操作结果
   */
  static async markNotificationsAsRead(notificationIds) {
    try {
      const ids = Array.isArray(notificationIds) ? notificationIds : [notificationIds];
      
      const promises = ids.map(id => 
        db.collection('notifications').doc(id).update({
          data: {
            isRead: true,
            readTime: new Date()
          }
        })
      );

      await Promise.all(promises);
      
      return {
        success: true,
        message: '通知已标记为已读'
      };
    } catch (error) {
      console.error('标记通知为已读失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 记录消息发送日志
   * @param {Object} logData 日志数据
   * @returns {Object} 操作结果
   */
  static async createMessageLog(logData) {
    try {
      const result = await db.collection('message_logs').add({
        data: logData
      });
      
      return {
        success: true,
        data: {
          _id: result._id,
          ...logData
        }
      };
    } catch (error) {
      console.error('记录消息发送日志失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 批量更新过期券状态
   * @returns {Object} 操作结果
   */
  static async updateExpiredCoupons() {
    try {
      const now = new Date();
      
      // 查询已过期但状态不是 expired 的券
      const expiredCoupons = await db.collection('coupons').where({
        expiresAt: _.lt(now),
        status: _.neq('expired')
      }).get();

      if (expiredCoupons.data.length === 0) {
        return {
          success: true,
          data: { updatedCount: 0 }
        };
      }

      // 批量更新为过期状态
      const promises = expiredCoupons.data.map(coupon => 
        db.collection('coupons').doc(coupon._id).update({
          data: {
            status: 'expired',
            expiredTime: now,
            updateTime: now
          }
        })
      );

      await Promise.all(promises);
      
      return {
        success: true,
        data: { updatedCount: expiredCoupons.data.length }
      };
    } catch (error) {
      console.error('批量更新过期券状态失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = Database; 