/**
 * 创建/更新信誉券云函数
 * 处理信誉券的创建和更新操作
 */

const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event 云函数调用参数
 * @param {Object} context 云函数运行上下文
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const OPENID = wxContext.OPENID;
  
  console.log('创建/更新券云函数开始执行, openid:', OPENID, 'params:', event);
  
  try {
    // 参数验证
    const { 
      couponId, 
      name, 
      description,
      couponNumber, // 新增：券编号
      couponType = 'promise', // 新增：券类型，默认为承诺券
      icon, 
      bgColor, 
      expiryDate, 
      status = 'received' // 修改：默认为已接收状态，发券即送达
    } = event;

    // 必填参数验证
    if (!name || !description) {
      throw new Error('券标题和描述不能为空');
    }

    if (name.trim().length > 20) {
      throw new Error('券标题不能超过20个字');
    }

    if (description.trim().length > 100) {
      throw new Error('券描述不能超过100个字');
    }

    // 验证券类型
    const validCouponTypes = ['promise', 'task', 'reward', 'apology', 'special'];
    if (!validCouponTypes.includes(couponType)) {
      throw new Error('无效的券类型');
    }

    // 生成券编号（如果未提供）
    const finalCouponNumber = couponNumber || generateCouponNumber();

    // 查询用户信息 - 修复：使用正确的字段名 _openid
    const userQuery = await db.collection('users').where({
      _openid: OPENID
    }).get();

    if (userQuery.data.length === 0) {
      throw new Error('用户不存在，请先登录');
    }

    const userInfo = userQuery.data[0];
    console.log('查询到用户信息:', {
      _id: userInfo._id,
      _openid: userInfo._openid,
      nickName: userInfo.nickName,
      partnerId: userInfo.partnerId
    });

    // 检查用户是否已绑定情侣 - 修复：使用 partnerId 而不是 coupleId
    if (!userInfo.partnerId) {
      throw new Error('请先绑定情侣才能创建信誉券');
    }

    // 获取情侣信息 - 修复：使用 partnerId 查询 couples 表
    const coupleQuery = await db.collection('couples').doc(userInfo.partnerId).get();
    if (!coupleQuery.data) {
      throw new Error('情侣关系不存在');
    }

    const coupleInfo = coupleQuery.data;
    console.log('查询到情侣信息:', {
      _id: coupleInfo._id,
      user1Id: coupleInfo.user1Id,
      user2Id: coupleInfo.user2Id,
      status: coupleInfo.status
    });

    // 确定接收方（伴侣）的openid
    const partnerOpenid = coupleInfo.user1Id === OPENID ? coupleInfo.user2Id : coupleInfo.user1Id;
    
    // 获取伴侣用户信息
    const partnerQuery = await db.collection('users').where({
      _openid: partnerOpenid
    }).get();
    
    if (partnerQuery.data.length === 0) {
      throw new Error('伴侣用户信息不存在');
    }
    
    const partnerInfo = partnerQuery.data[0];
    console.log('查询到伴侣信息:', {
      _id: partnerInfo._id,
      _openid: partnerInfo._openid,
      nickName: partnerInfo.nickName
    });

    const currentTime = new Date();

    // 验证过期时间
    if (expiryDate && new Date(expiryDate) <= currentTime) {
      throw new Error('有效期不能早于当前时间');
    }

    // 构建券数据
    const couponData = {
      name: name.trim(), // 保持兼容性
      title: name.trim(), // 新增：兼容前端期望的字段
      description: description.trim(),
      couponNumber: finalCouponNumber, // 新增：券编号
      couponType: couponType, // 新增：券类型
      icon: icon || getDefaultIcon(couponType),
      bgColor: bgColor || getDefaultBgColor(couponType),
      expiryDate: expiryDate ? new Date(expiryDate) : null,
      expiresAt: expiryDate ? new Date(expiryDate) : null, // 兼容前端期望的字段
      status: status,
      
      // 创建者信息
      creatorId: OPENID,
      creatorNickname: userInfo.nickName,
      creatorAvatar: userInfo.avatarUrl,
      
      // 接收者信息
      recipientId: partnerOpenid,
      recipientNickname: partnerInfo.nickName,
      recipientAvatar: partnerInfo.avatarUrl,
      
      // 关联信息
      coupleId: userInfo.partnerId, // 使用 partnerId 作为 coupleId
      
      // 时间信息
      createTime: currentTime,
      updateTime: currentTime,
      sendTime: currentTime, // 发送时间
      receiveTime: status === 'received' ? currentTime : null, // 立即接收
      redeemTime: null,
      
      // 额外信息
      category: event.category || 'custom',
      tags: event.tags || [],
      isTemplate: false,
      
      // 统计信息
      viewCount: 0,
      shareCount: 0
    };

    let result;
    let isNewCoupon = false;

    if (couponId) {
      // 更新现有券
      console.log('更新现有券:', couponId);
      
      // 验证券的所有权
      const existingCouponQuery = await db.collection('coupons').doc(couponId).get();
      if (!existingCouponQuery.data) {
        throw new Error('券不存在');
      }

      const existingCoupon = existingCouponQuery.data;
      if (existingCoupon.creatorId !== OPENID) {
        throw new Error('只能修改自己创建的券');
      }

      if (existingCoupon.status !== 'draft' && existingCoupon.status !== 'received') {
        throw new Error('只能修改草稿或已接收状态的券');
      }

      // 更新券数据
      const updateData = {
        ...couponData,
        createTime: existingCoupon.createTime, // 保持原创建时间
        viewCount: existingCoupon.viewCount, // 保持统计数据
        shareCount: existingCoupon.shareCount
      };

      await db.collection('coupons').doc(couponId).update({
        data: updateData
      });

      result = {
        _id: couponId,
        ...updateData
      };

    } else {
      // 创建新券
      console.log('创建新券');
      isNewCoupon = true;
      
      const createResult = await db.collection('coupons').add({
        data: couponData
      });

      if (!createResult._id) {
        throw new Error('券创建失败');
      }

      result = {
        _id: createResult._id,
        ...couponData
      };

      // 更新用户统计信息
      await db.collection('users').doc(userInfo._id).update({
        data: {
          'stats.couponsCreated': db.command.inc(1),
          updateTime: currentTime
        }
      });

      // 更新情侣统计信息
      await db.collection('couples').doc(userInfo.partnerId).update({
        data: {
          'stats.totalCoupons': db.command.inc(1),
          'stats.activeCoupons': db.command.inc(1),
          updateTime: currentTime
        }
      });
    }

    console.log('券操作成功:', result._id, '操作类型:', couponId ? '更新' : '创建');

    // 如果是新创建的券且状态为已接收，发送通知
    if (isNewCoupon && status === 'received') {
      console.log('准备发送新券通知...');
      
      try {
        // 只使用一种通知方式，直接保存通知记录到数据库
        await db.collection('notifications').add({
          data: {
            type: 'new_coupon',
            title: '收到新的信任券',
            content: `${userInfo.nickName} 送给您一张信任券：${result.name}，已自动接收`,
            recipientId: partnerOpenid,
            senderId: OPENID,
            couponId: result._id,
            couponName: result.name,
            isRead: false,
            createTime: currentTime
          }
        });
        
        console.log('通知记录已保存到数据库');
        
      } catch (notificationError) {
        console.error('发送通知失败:', notificationError);
        // 通知失败不影响券的创建成功
      }
    }

    return {
      success: true,
      message: couponId ? '券更新成功' : '券创建成功',
      data: {
        coupon: result,
        timestamp: currentTime.toISOString()
      }
    };

  } catch (error) {
    console.error('创建/更新券云函数执行失败:', error);
    
    return {
      success: false,
      message: '操作失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 生成券编号
 * 规则：No.YY+4位数字 (如: No.25001)
 */
function generateCouponNumber() {
  const currentYear = new Date().getFullYear();
  const yearSuffix = String(currentYear).slice(-2); // 取年份后两位
  
  // 生成4位随机数字
  const randomNum = Math.floor(Math.random() * 9000) + 1000; // 1000-9999
  
  return `${yearSuffix}${randomNum}`;
}

/**
 * 根据券类型获取默认图标
 */
function getDefaultIcon(couponType) {
  const iconMap = {
    promise: '💕',
    task: '📝',
    reward: '🎁',
    apology: '🙏',
    special: '✨'
  };
  return iconMap[couponType] || '💕';
}

/**
 * 根据券类型获取默认背景色
 */
function getDefaultBgColor(couponType) {
  const bgColorMap = {
    promise: 'linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%)',
    task: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    reward: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
    apology: 'linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%)',
    special: 'linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 25%, #45B7D1 50%, #96CEB4 75%, #FFEAA7 100%)'
  };
  return bgColorMap[couponType] || 'linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%)';
} 