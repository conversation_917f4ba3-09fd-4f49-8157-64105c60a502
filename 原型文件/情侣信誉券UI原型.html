<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情侣信誉券 - 让爱的承诺有形有爱</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/mermaid/8.14.0/mermaid.min.js"></script>
    
    <style>
        * {
            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
        }

        :root {
            --primary: #FF6D96;
            --secondary: #FFE4E1;
            --accent: #FFD700;
            --neutral-dark: #333333;
            --neutral-medium: #666666;
            --neutral-light: #CCCCCC;
            --white: #FFFFFF;
        }

        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #FFE4E1 0%, #FFF5F5 100%);
            min-height: 100vh;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            margin: 40px auto;
            background: var(--white);
            border-radius: 30px;
            border: 1px solid #ddd;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .page {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            background: var(--white);
            overflow-y: auto;
        }

        .page.active {
            opacity: 1;
            transform: translateX(0);
        }

        .page.slide-out {
            transform: translateX(-100%);
        }

        /* Header */
        .header {
            padding: 60px 20px 20px;
            text-align: center;
            background: linear-gradient(135deg, var(--primary) 0%, #FF8FB3 100%);
            color: var(--white);
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        /* Login Page */
        .login-content {
            padding: 60px 40px;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary) 0%, #FF8FB3 100%);
            color: var(--white);
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: var(--white);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: var(--primary);
        }

        .login-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .login-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 60px;
            line-height: 1.5;
        }

        .btn-primary {
            background: var(--white);
            color: var(--primary);
            border: none;
            padding: 16px 32px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        /* Home Page */
        .home-header {
            background: linear-gradient(135deg, var(--primary) 0%, #FF8FB3 100%);
            color: var(--white);
            padding: 60px 20px 30px;
            border-radius: 0 0 30px 30px;
        }

        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--white);
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
        }

        .user-name {
            font-size: 18px;
            font-weight: 500;
        }

        .couple-status {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }

        .quick-actions {
            padding: 30px 20px;
        }

        .action-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .action-btn {
            background: var(--white);
            border: 2px solid var(--secondary);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--neutral-dark);
            text-decoration: none;
        }

        .action-btn:hover {
            border-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255,109,150,0.2);
        }

        .action-icon {
            font-size: 24px;
            color: var(--primary);
            margin-bottom: 8px;
        }

        .action-text {
            font-size: 14px;
            font-weight: 500;
        }

        /* Statistics */
        .stats-section {
            background: var(--white);
            margin: 0 20px;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stats-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--neutral-dark);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 600;
            color: var(--primary);
        }

        .stat-label {
            font-size: 12px;
            color: var(--neutral-medium);
            margin-top: 4px;
        }

        /* Voucher Card */
        .voucher-card {
            background: linear-gradient(135deg, var(--primary) 0%, #FF8FB3 100%);
            border-radius: 20px;
            padding: 20px;
            margin: 10px 0;
            color: var(--white);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .voucher-card:hover {
            transform: translateY(-3px);
        }

        .voucher-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .voucher-type {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 10px;
            font-size: 12px;
        }

        .voucher-number {
            font-size: 12px;
            opacity: 0.8;
        }

        .voucher-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .voucher-desc {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
            margin-bottom: 15px;
        }

        .voucher-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            opacity: 0.8;
        }

        /* Tab Navigation */
        .tab-nav {
            display: flex;
            background: var(--secondary);
            margin: 20px;
            border-radius: 12px;
            padding: 4px;
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--neutral-medium);
        }

        .tab-item.active {
            background: var(--white);
            color: var(--primary);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* Create Voucher Form */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--neutral-dark);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--secondary);
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: var(--white);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }

        .form-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            background: var(--white);
            border-top: 1px solid var(--neutral-light);
            padding: 10px 0;
            display: flex;
            justify-content: space-around;
        }

        .nav-item {
            text-align: center;
            cursor: pointer;
            padding: 8px;
            color: var(--neutral-medium);
            transition: color 0.3s ease;
        }

        .nav-item.active {
            color: var(--primary);
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .nav-text {
            font-size: 12px;
        }

        /* Floating elements */
        .floating-hearts {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .heart {
            position: absolute;
            color: var(--primary);
            opacity: 0.3;
            animation: float 6s infinite ease-in-out;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-20px) rotate(10deg); opacity: 0.6; }
        }

        /* Responsive adjustments */
        @media (max-width: 400px) {
            .phone-frame {
                width: 100vw;
                height: 100vh;
                margin: 0;
                border-radius: 0;
                border: none;
            }
        }

        /* Loading animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: var(--white);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Phone Frame Container -->
    <div class="phone-frame">
        
        <!-- Login Page -->
        <div class="page active" id="loginPage">
            <div class="login-content">
                <div class="floating-hearts">
                    <i class="fas fa-heart heart" style="top: 10%; left: 20%; animation-delay: 0s;"></i>
                    <i class="fas fa-heart heart" style="top: 30%; right: 25%; animation-delay: 2s;"></i>
                    <i class="fas fa-heart heart" style="bottom: 40%; left: 30%; animation-delay: 4s;"></i>
                </div>
                
                <div class="logo">
                    <i class="fas fa-heart"></i>
                </div>
                
                <h1 class="login-title">情侣信誉券</h1>
                <p class="login-subtitle">让爱的承诺有形有爱<br>将甜蜜时光具象为温暖券面</p>
                
                <button class="btn-primary" onclick="login()">
                    <i class="fab fa-weixin" style="margin-right: 8px;"></i>
                    微信一键登录
                </button>
            </div>
        </div>

        <!-- Home Page -->
        <div class="page" id="homePage">
            <div class="home-header">
                <div class="user-info">
                    <div class="avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="user-name">小甜心</div>
                        <div style="font-size: 14px; opacity: 0.8;">欢迎回来</div>
                    </div>
                </div>
                
                <div class="couple-status">
                    <i class="fas fa-heart" style="margin-right: 8px;"></i>
                    已与 "大宝贝" 绑定
                    <div style="font-size: 12px; margin-top: 5px; opacity: 0.8;">
                        在一起第 256 天
                    </div>
                </div>
            </div>

            <div class="quick-actions">
                <div class="action-grid">
                    <div class="action-btn" onclick="navigateTo('createPage')">
                        <div class="action-icon">
                            <i class="fas fa-plus-circle"></i>
                        </div>
                        <div class="action-text">创建信誉券</div>
                    </div>
                    
                    <div class="action-btn" onclick="navigateTo('receivedPage')">
                        <div class="action-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <div class="action-text">我收到的</div>
                    </div>
                    
                    <div class="action-btn" onclick="navigateTo('sentPage')">
                        <div class="action-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="action-text">我发出的</div>
                    </div>
                    
                    <div class="action-btn" onclick="navigateTo('profilePage')">
                        <div class="action-icon">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="action-text">个人中心</div>
                    </div>
                </div>

                <div class="stats-section">
                    <div class="stats-title">
                        <i class="fas fa-chart-line" style="margin-right: 8px; color: var(--primary);"></i>
                        本月统计
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">12</div>
                            <div class="stat-label">已创建</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">8</div>
                            <div class="stat-label">已使用</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">67%</div>
                            <div class="stat-label">履约率</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Create Voucher Page -->
        <div class="page" id="createPage">
            <div class="header">
                <button onclick="navigateTo('homePage')" style="position: absolute; left: 20px; top: 60px; background: none; border: none; color: white; font-size: 18px;">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1>创建信誉券</h1>
                <p>为爱人制作一张专属承诺</p>
            </div>

            <div style="padding: 20px;">
                <form id="voucherForm">
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-tag" style="margin-right: 5px; color: var(--primary);"></i>
                            券名称 *
                        </label>
                        <input type="text" class="form-input" placeholder="例如：爱心早餐券" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-list" style="margin-right: 5px; color: var(--primary);"></i>
                            券类型
                        </label>
                        <select class="form-input form-select">
                            <option>服务券</option>
                            <option>体验券</option>
                            <option>按摩券</option>
                            <option>买单券</option>
                            <option>道歉券</option>
                            <option>奖励券</option>
                            <option>游戏券</option>
                            <option>自定义</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-align-left" style="margin-right: 5px; color: var(--primary);"></i>
                            详细描述
                        </label>
                        <textarea class="form-input form-textarea" placeholder="承诺为你制作一顿温馨早餐，包含你最爱的荷包蛋和小粥"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-calendar" style="margin-right: 5px; color: var(--primary);"></i>
                            有效期
                        </label>
                        <select class="form-input form-select">
                            <option>7天</option>
                            <option>30天</option>
                            <option>永久有效</option>
                            <option>指定日期</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-palette" style="margin-right: 5px; color: var(--primary);"></i>
                            券面风格
                        </label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px;">
                            <div style="width: 100%; height: 60px; background: linear-gradient(135deg, #FF6D96, #FF8FB3); border-radius: 8px; cursor: pointer; border: 2px solid transparent;" onclick="selectStyle(this)"></div>
                            <div style="width: 100%; height: 60px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 8px; cursor: pointer; border: 2px solid transparent;" onclick="selectStyle(this)"></div>
                            <div style="width: 100%; height: 60px; background: linear-gradient(135deg, #FFD700, #FFA500); border-radius: 8px; cursor: pointer; border: 2px solid transparent;" onclick="selectStyle(this)"></div>
                        </div>
                    </div>

                    <button type="button" class="btn-primary w-full" style="width: 100%; margin-top: 30px;" onclick="createVoucher()">
                        <i class="fas fa-heart" style="margin-right: 8px;"></i>
                        创建并发送给 TA
                    </button>
                </form>
            </div>
        </div>

        <!-- Received Vouchers Page -->
        <div class="page" id="receivedPage">
            <div class="header">
                <button onclick="navigateTo('homePage')" style="position: absolute; left: 20px; top: 60px; background: none; border: none; color: white; font-size: 18px;">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1>我收到的券</h1>
                <p>来自爱人的温暖承诺</p>
            </div>

            <div style="padding: 20px;">
                <div class="tab-nav">
                    <div class="tab-item active">待使用</div>
                    <div class="tab-item">已使用</div>
                    <div class="tab-item">已过期</div>
                </div>

                <div class="voucher-card" onclick="navigateTo('voucherDetailPage')">
                    <div class="voucher-header">
                        <div class="voucher-type">服务券</div>
                        <div class="voucher-number">No.001</div>
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <i class="fas fa-utensils" style="font-size: 32px; margin-bottom: 10px;"></i>
                    </div>
                    <div class="voucher-title">爱心早餐券</div>
                    <div class="voucher-desc">承诺为你制作一顿温馨早餐，包含你最爱的荷包蛋和小粥</div>
                    <div class="voucher-footer">
                        <span>发券人：大宝贝</span>
                        <span>有效期至：2024-02-14</span>
                    </div>
                </div>

                <div class="voucher-card" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                    <div class="voucher-header">
                        <div class="voucher-type">体验券</div>
                        <div class="voucher-number">No.002</div>
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <i class="fas fa-moon" style="font-size: 32px; margin-bottom: 10px;"></i>
                    </div>
                    <div class="voucher-title">星空约会券</div>
                    <div class="voucher-desc">一起在阳台看星星数月亮，聊聊我们的小秘密</div>
                    <div class="voucher-footer">
                        <span>发券人：大宝贝</span>
                        <span>有效期至：2024-03-01</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Voucher Detail Page -->
        <div class="page" id="voucherDetailPage">
            <div class="header">
                <button onclick="navigateTo('receivedPage')" style="position: absolute; left: 20px; top: 60px; background: none; border: none; color: white; font-size: 18px;">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1>券详情</h1>
                <p>查看承诺详情</p>
            </div>

            <div style="padding: 20px;">
                <div class="voucher-card" style="margin-bottom: 30px; transform: scale(1.05);">
                    <div class="voucher-header">
                        <div class="voucher-type">服务券</div>
                        <div class="voucher-number">No.001</div>
                    </div>
                    <div style="text-align: center; margin: 20px 0;">
                        <i class="fas fa-utensils" style="font-size: 48px; margin-bottom: 15px;"></i>
                    </div>
                    <div class="voucher-title">爱心早餐券</div>
                    <div class="voucher-desc">承诺为你制作一顿温馨早餐，包含你最爱的荷包蛋和小粥</div>
                    <div class="voucher-footer">
                        <span>发券人：大宝贝</span>
                        <span>有效期至：2024-02-14</span>
                    </div>
                </div>

                <div style="background: var(--white); padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                    <h3 style="margin-bottom: 15px; color: var(--neutral-dark);">
                        <i class="fas fa-info-circle" style="margin-right: 8px; color: var(--primary);"></i>
                        详细信息
                    </h3>
                    <div style="margin-bottom: 10px;">
                        <span style="color: var(--neutral-medium);">创建时间：</span>
                        <span>2024-01-15 10:30</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <span style="color: var(--neutral-medium);">状态：</span>
                        <span style="color: var(--primary); font-weight: 500;">待使用</span>
                    </div>
                    <div>
                        <span style="color: var(--neutral-medium);">发送留言：</span>
                        <span>"亲爱的，辛苦了~ 这是我的小心意"</span>
                    </div>
                </div>

                <button class="btn-primary" style="width: 100%; padding: 16px; font-size: 18px;" onclick="useVoucher()">
                    <i class="fas fa-heart" style="margin-right: 8px;"></i>
                    立即使用这张券
                </button>
            </div>
        </div>

        <!-- Profile Page -->
        <div class="page" id="profilePage">
            <div class="header">
                <button onclick="navigateTo('homePage')" style="position: absolute; left: 20px; top: 60px; background: none; border: none; color: white; font-size: 18px;">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1>个人中心</h1>
                <p>管理你的甜蜜空间</p>
            </div>

            <div style="padding: 20px;">
                <div style="background: var(--white); padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                    <div style="display: flex; align-items: center; margin-bottom: 20px;">
                        <div class="avatar" style="width: 60px; height: 60px; margin-right: 15px;">
                            <i class="fas fa-user" style="font-size: 24px;"></i>
                        </div>
                        <div>
                            <div style="font-size: 18px; font-weight: 600; color: var(--neutral-dark);">小甜心</div>
                            <div style="font-size: 14px; color: var(--neutral-medium);">与大宝贝在一起第256天</div>
                        </div>
                    </div>
                    
                    <div style="background: var(--secondary); padding: 15px; border-radius: 10px;">
                        <div style="text-align: center;">
                            <i class="fas fa-heart" style="color: var(--primary); margin-right: 8px;"></i>
                            情侣空间
                        </div>
                    </div>
                </div>

                <div style="background: var(--white); padding: 20px; border-radius: 15px; margin-bottom: 20px;">
                    <h3 style="margin-bottom: 15px; color: var(--neutral-dark);">功能菜单</h3>
                    
                    <div style="margin-bottom: 15px; padding: 15px; background: var(--secondary); border-radius: 10px; cursor: pointer;">
                        <i class="fas fa-link" style="margin-right: 10px; color: var(--primary);"></i>
                        绑定管理
                    </div>
                    
                    <div style="margin-bottom: 15px; padding: 15px; background: var(--secondary); border-radius: 10px; cursor: pointer;">
                        <i class="fas fa-history" style="margin-right: 10px; color: var(--primary);"></i>
                        历史记录
                    </div>
                    
                    <div style="margin-bottom: 15px; padding: 15px; background: var(--secondary); border-radius: 10px; cursor: pointer;">
                        <i class="fas fa-cog" style="margin-right: 10px; color: var(--primary);"></i>
                        设置
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="navigateTo('homePage')">
                <div class="nav-icon"><i class="fas fa-home"></i></div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item" onclick="navigateTo('receivedPage')">
                <div class="nav-icon"><i class="fas fa-gift"></i></div>
                <div class="nav-text">我的券</div>
            </div>
            <div class="nav-item" onclick="navigateTo('createPage')">
                <div class="nav-icon"><i class="fas fa-plus-circle"></i></div>
                <div class="nav-text">创建</div>
            </div>
            <div class="nav-item" onclick="navigateTo('profilePage')">
                <div class="nav-icon"><i class="fas fa-user"></i></div>
                <div class="nav-text">我的</div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 'loginPage';

        function navigateTo(pageId) {
            const currentPageEl = document.getElementById(currentPage);
            const targetPageEl = document.getElementById(pageId);
            
            currentPageEl.classList.add('slide-out');
            setTimeout(() => {
                currentPageEl.classList.remove('active', 'slide-out');
                targetPageEl.classList.add('active');
                currentPage = pageId;
                
                // Update bottom nav
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                
                if (pageId === 'homePage') document.querySelector('.nav-item:nth-child(1)').classList.add('active');
                if (pageId === 'receivedPage') document.querySelector('.nav-item:nth-child(2)').classList.add('active');
                if (pageId === 'createPage') document.querySelector('.nav-item:nth-child(3)').classList.add('active');
                if (pageId === 'profilePage') document.querySelector('.nav-item:nth-child(4)').classList.add('active');
            }, 150);
        }

        function login() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<div class="loading"></div> 登录中...';
            btn.disabled = true;
            
            setTimeout(() => {
                navigateTo('homePage');
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        }

        function selectStyle(element) {
            document.querySelectorAll('[onclick="selectStyle(this)"]').forEach(el => {
                el.style.border = '2px solid transparent';
            });
            element.style.border = '2px solid var(--primary)';
        }

        function createVoucher() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<div class="loading"></div> 创建中...';
            btn.disabled = true;
            
            setTimeout(() => {
                alert('信誉券创建成功！已发送给大宝贝 💕');
                navigateTo('homePage');
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        }

        function useVoucher() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<div class="loading"></div> 使用中...';
            btn.disabled = true;
            
            setTimeout(() => {
                alert('券使用成功！快去享受你的爱心早餐吧 😊');
                navigateTo('receivedPage');
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 1500);
        }

        // Auto-select first style option
        document.addEventListener('DOMContentLoaded', function() {
            const firstStyle = document.querySelector('[onclick="selectStyle(this)"]');
            if (firstStyle) {
                selectStyle(firstStyle);
            }
        });
    </script>
</body>
</html> 