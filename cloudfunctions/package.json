{"name": "couple-trust-coupon-notifications", "version": "1.0.0", "description": "情侣信任券小程序消息通知云函数", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint .", "format": "prettier --write ."}, "keywords": ["wechat", "miniprogram", "cloud-functions", "notifications", "subscribe-message", "couple", "trust-coupon"], "author": "Couple Trust Coupon Team", "license": "MIT", "dependencies": {"wx-server-sdk": "^2.6.3"}, "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.5.0"}, "engines": {"node": ">=12.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/couple-trust-coupon"}, "bugs": {"url": "https://github.com/your-org/couple-trust-coupon/issues"}, "homepage": "https://github.com/your-org/couple-trust-coupon#readme", "cloudbaseConfig": {"framework": "wechat-miniprogram", "runtime": "nodejs12.16", "timeout": 60000, "memorySize": 256, "environment": {"NODE_ENV": "production"}}, "miniprogram": {"cloud": true, "compileType": "miniprogram", "libVersion": "2.32.3", "appid": "YOUR_MINIPROGRAM_APPID", "projectname": "couple-trust-coupon"}}