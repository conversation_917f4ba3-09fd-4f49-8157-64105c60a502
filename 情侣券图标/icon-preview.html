<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序图标设计方案预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 50%, #F0F8FF 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #FF69B4;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1rem;
        }
        
        .names-section {
            margin-bottom: 50px;
            background: #FAFAFA;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #FF69B4;
        }
        
        .names-section h2 {
            color: #FF69B4;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }
        
        .names-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .name-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .name-card:hover {
            transform: translateY(-3px);
        }
        
        .name-title {
            font-weight: bold;
            color: #FF69B4;
            font-size: 1.1rem;
            margin-bottom: 5px;
        }
        
        .name-desc {
            color: #666;
            font-size: 0.9rem;
        }
        
        .recommended {
            border: 2px solid #FF69B4;
            background: linear-gradient(135deg, #FFE4E1, #FFF0F5);
        }
        
        .icons-section h2 {
            color: #FF69B4;
            margin-bottom: 30px;
            text-align: center;
            font-size: 1.8rem;
        }
        
        .icons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .icon-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .icon-card:hover {
            transform: translateY(-5px);
        }
        
        .icon-display {
            background: #FAFAFA;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .icon-title {
            font-weight: bold;
            color: #FF69B4;
            margin-bottom: 8px;
            font-size: 1.2rem;
        }
        
        .icon-desc {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .download-section {
            background: #F0F8FF;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-top: 30px;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #FF69B4, #FFB6C1);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            margin: 5px;
            transition: transform 0.3s ease;
        }
        
        .download-btn:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 小程序品牌设计方案</h1>
        <p class="subtitle">让承诺更有仪式感的情侣应用</p>
        
        <!-- 名称方案 -->
        <div class="names-section">
            <h2>💝 产品名称建议</h2>
            
            <h3 style="color: #FF69B4; margin-bottom: 15px;">🎯 推荐首选</h3>
            <div class="names-grid">
                <div class="name-card recommended">
                    <div class="name-title">甜蜜约定</div>
                    <div class="name-desc">直接体现情侣承诺，充满浪漫色彩</div>
                </div>
                <div class="name-card">
                    <div class="name-title">心愿清单</div>
                    <div class="name-desc">简洁温馨，暗示美好愿望的实现</div>
                </div>
                <div class="name-card">
                    <div class="name-title">爱的见证</div>
                    <div class="name-desc">强调仪式感，让每个承诺都有见证意义</div>
                </div>
            </div>
            
            <h3 style="color: #FF69B4; margin: 20px 0 15px 0;">🌟 创意选择</h3>
            <div class="names-grid">
                <div class="name-card">
                    <div class="name-title">约定小屋</div>
                    <div class="name-desc">营造温馨的专属空间感</div>
                </div>
                <div class="name-card">
                    <div class="name-title">心意卡片</div>
                    <div class="name-desc">突出卡片形式，简单易懂</div>
                </div>
                <div class="name-card">
                    <div class="name-title">情话盒子</div>
                    <div class="name-desc">神秘感与收藏感并存</div>
                </div>
                <div class="name-card">
                    <div class="name-title">心动约定</div>
                    <div class="name-desc">年轻化表达，充满活力</div>
                </div>
                <div class="name-card">
                    <div class="name-title">约定星球</div>
                    <div class="name-desc">营造专属的浪漫世界</div>
                </div>
                <div class="name-card">
                    <div class="name-title">甜蜜时光机</div>
                    <div class="name-desc">暗示记录美好时光</div>
                </div>
            </div>
        </div>
        
        <!-- 图标设计 -->
        <div class="icons-section">
            <h2>🎨 图标设计方案 (144×144px)</h2>
            
            <div class="icons-grid">
                <!-- 方案1 -->
                <div class="icon-card">
                    <div class="icon-display">
                        <svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FF69B4"/>
                              <stop offset="50%" stop-color="#FFB6C1"/>
                              <stop offset="100%" stop-color="#FFC0CB"/>
                            </linearGradient>
                            <filter id="shadow1">
                              <feDropShadow dx="2" dy="2" stdDeviation="4" flood-opacity="0.3"/>
                            </filter>
                          </defs>
                          <circle cx="72" cy="72" r="68" fill="url(#gradient1)" filter="url(#shadow1)"/>
                          <path d="M50 45 C40 35, 25 35, 25 55 C25 70, 50 85, 50 85 C50 85, 60 75, 65 65" 
                                fill="#FFFFFF" opacity="0.9"/>
                          <path d="M94 45 C104 35, 119 35, 119 55 C119 70, 94 85, 94 85 C94 85, 84 75, 79 65" 
                                fill="#FFFFFF" opacity="0.9"/>
                          <circle cx="72" cy="65" r="8" fill="none" stroke="#FFFFFF" stroke-width="3" opacity="0.8"/>
                        </svg>
                    </div>
                    <div class="icon-title">双心相扣</div>
                    <div class="icon-desc">经典浪漫设计，两颗心通过连接环相扣，象征情侣间的紧密连接和永恒承诺</div>
                </div>
                
                <!-- 方案2 -->
                <div class="icon-card">
                    <div class="icon-display">
                        <svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FF8A80"/>
                              <stop offset="50%" stop-color="#FFAB91"/>
                              <stop offset="100%" stop-color="#FFCC9A"/>
                            </linearGradient>
                            <filter id="shadow2">
                              <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.2"/>
                            </filter>
                          </defs>
                          <rect width="144" height="144" rx="32" fill="url(#gradient2)" filter="url(#shadow2)"/>
                          <ellipse cx="50" cy="45" rx="12" ry="15" fill="#FFFFFF" opacity="0.9"/>
                          <rect x="38" y="58" width="24" height="35" rx="12" fill="#FFFFFF" opacity="0.9"/>
                          <ellipse cx="94" cy="45" rx="12" ry="15" fill="#FFFFFF" opacity="0.9"/>
                          <rect x="82" y="58" width="24" height="35" rx="12" fill="#FFFFFF" opacity="0.9"/>
                          <line x1="62" y1="75" x2="82" y2="75" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round" opacity="0.9"/>
                          <path d="M72 100 C68 96, 60 96, 60 104 C60 110, 72 120, 72 120 C72 120, 84 110, 84 104 C84 96, 76 96, 72 100" 
                                fill="#FFFFFF" opacity="0.7"/>
                        </svg>
                    </div>
                    <div class="icon-title">情侣牵手</div>
                    <div class="icon-desc">温暖人心的设计，展现情侣牵手的美好瞬间，下方的心形代表爱意满满</div>
                </div>
                
                <!-- 方案3 -->
                <div class="icon-card">
                    <div class="icon-display">
                        <svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#F8BBD9"/>
                              <stop offset="100%" stop-color="#E1BEE7"/>
                            </linearGradient>
                            <filter id="shadow3">
                              <feDropShadow dx="3" dy="3" stdDeviation="5" flood-opacity="0.15"/>
                            </filter>
                          </defs>
                          <circle cx="72" cy="72" r="70" fill="url(#gradient3)" filter="url(#shadow3)"/>
                          <rect x="32" y="45" width="80" height="54" rx="8" fill="#FFFFFF" filter="url(#shadow3)"/>
                          <line x1="45" y1="60" x2="99" y2="60" stroke="#FFB6C1" stroke-width="2" stroke-linecap="round"/>
                          <line x1="45" y1="72" x2="85" y2="72" stroke="#FFB6C1" stroke-width="2" stroke-linecap="round"/>
                          <line x1="45" y1="84" x2="75" y2="84" stroke="#FFB6C1" stroke-width="2" stroke-linecap="round"/>
                          <path d="M95 52 C93 50, 90 50, 90 53 C90 56, 95 60, 95 60 C95 60, 100 56, 100 53 C100 50, 97 50, 95 52" 
                                fill="#FF69B4"/>
                        </svg>
                    </div>
                    <div class="icon-title">约定卡片</div>
                    <div class="icon-desc">简约现代风格，直观展现产品特色，卡片设计清晰传达核心功能概念</div>
                </div>
                
                <!-- 方案4 -->
                <div class="icon-card">
                    <div class="icon-display">
                        <svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FFCDD2"/>
                              <stop offset="50%" stop-color="#F8BBD9"/>
                              <stop offset="100%" stop-color="#E1BEE7"/>
                            </linearGradient>
                            <filter id="shadow4">
                              <feDropShadow dx="2" dy="3" stdDeviation="4" flood-opacity="0.2"/>
                            </filter>
                          </defs>
                          <rect width="144" height="144" rx="28" fill="url(#gradient4)" filter="url(#shadow4)"/>
                          <rect x="25" y="30" width="94" height="84" rx="4" fill="#FFFFFF" filter="url(#shadow4)"/>
                          <rect x="35" y="45" width="74" height="3" rx="1.5" fill="#FF69B4"/>
                          <line x1="35" y1="60" x2="109" y2="60" stroke="#FFB6C1" stroke-width="1" opacity="0.6"/>
                          <line x1="35" y1="70" x2="95" y2="70" stroke="#FFB6C1" stroke-width="1" opacity="0.6"/>
                          <line x1="35" y1="80" x2="105" y2="80" stroke="#FFB6C1" stroke-width="1" opacity="0.6"/>
                          <line x1="35" y1="90" x2="85" y2="90" stroke="#FFB6C1" stroke-width="1" opacity="0.6"/>
                          <path d="M72 100 C69 97, 64 97, 64 102 C64 106, 72 113, 72 113 C72 113, 80 106, 80 102 C80 97, 75 97, 72 100" 
                                fill="#FF69B4"/>
                          <circle cx="100" cy="50" r="3" fill="#FFB6C1" opacity="0.7"/>
                          <circle cx="105" cy="45" r="2" fill="#FF69B4" opacity="0.7"/>
                          <circle cx="95" cy="48" r="2" fill="#FF69B4" opacity="0.7"/>
                        </svg>
                    </div>
                    <div class="icon-title">约定书</div>
                    <div class="icon-desc">文艺范设计，像一份正式的约定书，增强仪式感，底部心形签名彰显爱意</div>
                </div>
                
                <!-- 方案5 -->
                <div class="icon-card">
                    <div class="icon-display">
                        <svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FF8A80"/>
                              <stop offset="100%" stop-color="#FFAB91"/>
                            </linearGradient>
                            <linearGradient id="boxGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FFFFFF"/>
                              <stop offset="100%" stop-color="#F5F5F5"/>
                            </linearGradient>
                            <filter id="shadow5">
                              <feDropShadow dx="3" dy="4" stdDeviation="6" flood-opacity="0.25"/>
                            </filter>
                          </defs>
                          <circle cx="72" cy="72" r="68" fill="url(#gradient5)" filter="url(#shadow5)"/>
                          <rect x="40" y="75" width="64" height="35" rx="4" fill="url(#boxGradient)" filter="url(#shadow5)"/>
                          <rect x="40" y="65" width="64" height="15" rx="4" fill="#FFFFFF" filter="url(#shadow5)"/>
                          <path d="M68 65 L76 65 M72 50 L72 70" stroke="#FF69B4" stroke-width="3" stroke-linecap="round"/>
                          <ellipse cx="68" cy="58" rx="6" ry="4" fill="#FF69B4" opacity="0.8"/>
                          <ellipse cx="76" cy="58" rx="6" ry="4" fill="#FF69B4" opacity="0.8"/>
                          <circle cx="72" cy="58" r="3" fill="#FF1744"/>
                          <path d="M55 90 L57 94 L61 94 L58 97 L59 101 L55 99 L51 101 L52 97 L49 94 L53 94 Z" 
                                fill="#FFB6C1" opacity="0.7"/>
                          <path d="M85 85 L86 87 L88 87 L87 89 L87 91 L85 90 L83 91 L83 89 L82 87 L84 87 Z" 
                                fill="#FF69B4" opacity="0.7"/>
                        </svg>
                    </div>
                    <div class="icon-title">心愿盒子</div>
                    <div class="icon-desc">立体精致设计，礼品盒造型配蝴蝶结，象征珍贵的心愿收藏，充满惊喜感</div>
                </div>
            </div>
        </div>
        
        <div class="download-section">
            <h3 style="color: #FF69B4; margin-bottom: 15px;">📥 使用说明</h3>
            <p style="color: #666; margin-bottom: 15px;">
                所有图标均为SVG格式，144×144像素，可直接用于微信小程序。<br>
                您可以复制SVG代码，或导出为PNG/JPG格式使用。
            </p>
            <button class="download-btn" onclick="downloadSVG()">下载全部SVG</button>
            <button class="download-btn" onclick="copyToClipboard()">复制代码</button>
        </div>
    </div>

    <script>
        function downloadSVG() {
            alert('请右键保存您喜欢的图标，或复制SVG代码到文件中保存。');
        }
        
        function copyToClipboard() {
            alert('您可以从源文件 app-icons.svg 中复制需要的图标代码。');
        }
    </script>
</body>
</html> 