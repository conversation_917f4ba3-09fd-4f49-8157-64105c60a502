<!-- pages/index/index.wxml -->
<view class="page-container">
  <!-- 背景图片层 - 回到image标签方式 -->
  <view class="background-layer">
    <!-- 调试信息 -->
    
    <image 
      class="background-image" 
      src="../../images/couple-background.png" 
      mode="aspectFill"
      binderror="onBackgroundImageError"
      bindload="onBackgroundImageLoad"
      style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1;">
    </image>
    <view class="background-overlay"></view>
  </view>
  
  <!-- 内容层 -->
  <view class="container" style="background: transparent;">
    <!-- Header Section -->
    <view class="header-section card">
      <view class="app-info-top">
        <image class="user-avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="app-greeting">
          <text class="greeting-main" wx:if="{{userInfo}}">Hi, {{userInfo.nickName || '亲爱的'}}!</text>
          <text class="greeting-main" wx:else>欢迎来到情侣信誉券</text>
          <text class="greeting-sub">记录和分享你们的甜蜜承诺</text>
        </view>
      </view>
      <view class="create-coupon-btn-header primary-button" bindtap="goToCreateCoupon">发券给TA</view>
    </view>

    <!-- 未登录状态 -->
    <block wx:if="{{!userInfo}}">
      <view class="empty-state card">
        <image src="/images/couple-hearts.svg" class="empty-icon" />
        <text class="empty-text">请先登录体验情侣信誉券</text>
      </view>
    </block>

    <!-- 已登录但未绑定状态 -->
    <block wx:elif="{{userInfo && !coupleInfo}}">
      <view class="bind-prompt">
        <view class="prompt-card card text-center">
          <image src="/images/couple-silhouette.svg" class="prompt-icon" />
          <text class="prompt-title">绑定你的另一半</text>
          <text class="prompt-message">创建邀请码或输入对方的邀请码，开始甜蜜互动</text>
          <view class="bind-buttons">
            <button class="primary-button bind-btn" bindtap="goToBinding">立即绑定</button>
          </view>
        </view>
      </view>
    </block>

    <!-- 已登录且已绑定状态 - 显示完整功能 -->
    <block wx:elif="{{userInfo && coupleInfo}}">
      <!-- Filter Tabs -->
      <view class="filter-tabs">
        <view class="tab {{activeFilter === 'partner' ? 'active' : ''}}" bindtap="switchFilter" data-filter="partner">
          TA的爱意券
        </view>
        <view class="tab {{activeFilter === 'featured' ? 'active' : ''}}" bindtap="switchFilter" data-filter="featured">
          <!-- 创作灵感 -->
          浪漫模板库
        </view>
      </view>

      <!-- Coupons from Partner -->
      <block wx:if="{{activeFilter === 'partner'}}">
        <scroll-view scroll-y class="coupons-scroll-view">
          <view wx:if="{{!isLoading && couponsFromPartner.length === 0}}" class="empty-state card">
            <image src="/images/couple-coupon-empty.svg" class="empty-icon couple-empty-icon" />
            <text class="empty-title">等待TA的爱意券</text>
            <text class="empty-subtitle">还没有收到TA的券哦，快去暗示一下吧～</text>
          </view>
          <view class="coupons-grid" wx:if="{{couponsFromPartner.length > 0}}">
            <view wx:for="{{couponsFromPartner}}" wx:key="_id" class="coupon-item" data-id="{{item._id}}" bindtap="onCouponTap">
              <coupon-card 
                title="{{item.title || item.name}}"
                description="{{item.description}}"
                coupon-number="{{item.couponNumber}}"
                coupon-type="{{item.couponType || 'promise'}}"
                status="{{item.status}}"
                sender="{{item.creatorNickname}}"
                receiver="{{item.recipientNickname}}"
                expiry-date="{{item.expiryDate || item.expiresAt}}"
                create-time="{{item.createTime}}"
                coupon-id="{{item._id}}"
                show-status="{{true}}"
                show-actions="{{false}}"
                clickable="{{false}}"
                custom-class="index-coupon-card">
              </coupon-card>
            </view>
          </view>
        </scroll-view>
      </block>

      <!-- Featured/Template Coupons -->
      <block wx:if="{{activeFilter === 'featured'}}">
        <scroll-view scroll-y class="coupons-scroll-view">
          <view wx:if="{{featuredCoupons.length === 0}}" class="empty-state card">
            <image src="/images/lightbulb.svg" class="empty-icon" />
            <text class="empty-text">暂无推荐，发挥你的创意吧！</text>
          </view>
          <view class="coupons-grid" wx:if="{{featuredCoupons.length > 0}}">
            <view class="template-coupon-wrapper"
                  wx:for="{{featuredCoupons}}"
                  wx:key="id"
                  data-template="{{item}}"
                  bindtap="useTemplate">
              <!-- 使用券卡片组件展示模板 -->
              <coupon-card
                title="{{item.title}}"
                description="{{item.description}}"
                coupon-type="{{item.couponType || 'promise'}}"
                status="received"
                show-status="{{false}}"
                show-actions="{{false}}"
                clickable="{{false}}"
                show-coupon-number="{{false}}"
                custom-class="template-coupon-card">
              </coupon-card>
            </view>
          </view>
        </scroll-view>
      </block>
    </block>
  </view>
</view>