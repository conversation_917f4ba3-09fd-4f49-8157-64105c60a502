<!--feedback.wxml-->
<view class="container">
  <!-- 常见问题 -->
  <view class="faq-section">
    <view class="section-header">
      <text class="section-title">常见问题</text>
      <text class="section-subtitle">快速找到您遇到的问题</text>
    </view>
    
    <view class="faq-list">
      <view 
        wx:for="{{faqList}}" 
        wx:key="id"
        class="faq-item {{item.expanded ? 'expanded' : ''}}"
        bindtap="toggleFaq"
        data-index="{{index}}"
      >
        <view class="faq-question">
          <text class="question-text">{{item.question}}</text>
          <text class="expand-icon">{{item.expanded ? '−' : '+'}}</text>
        </view>
        <view wx:if="{{item.expanded}}" class="faq-answer">
          <text class="answer-text">{{item.answer}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 反馈表单 -->
  <view class="feedback-section">
    <view class="section-header">
      <text class="section-title">问题反馈</text>
      <text class="section-subtitle">遇到问题？告诉我们如何改进</text>
    </view>

    <form bindsubmit="submitFeedback" report-submit="true">
      <!-- 反馈类型 -->
      <view class="form-group">
        <text class="form-label required">反馈类型</text>
        <view class="feedback-types">
          <view 
            wx:for="{{feedbackTypes}}" 
            wx:key="value"
            class="type-item {{selectedType === item.value ? 'selected' : ''}}"
            bindtap="selectFeedbackType"
            data-type="{{item.value}}"
          >
            <view class="type-icon">{{item.icon}}</view>
            <text class="type-label">{{item.label}}</text>
          </view>
        </view>
      </view>

      <!-- 问题描述 -->
      <view class="form-group">
        <text class="form-label required">问题描述</text>
        <textarea 
          class="form-textarea {{errors.description ? 'error' : ''}}"
          placeholder="请详细描述您遇到的问题或建议..."
          value="{{formData.description}}"
          bindinput="onDescriptionInput"
          maxlength="500"
          auto-height
        ></textarea>
        <view class="char-count">{{formData.description.length}}/500</view>
        <view wx:if="{{errors.description}}" class="error-text">{{errors.description}}</view>
      </view>

      <!-- 联系方式 -->
      <view class="form-group">
        <text class="form-label">联系方式 (可选)</text>
        <input 
          class="form-input"
          placeholder="手机号或邮箱，便于我们回复您"
          value="{{formData.contact}}"
          bindinput="onContactInput"
          maxlength="50"
        />
      </view>

      <!-- 截图上传 -->
      <view class="form-group">
        <text class="form-label">相关截图 (可选)</text>
        <view class="image-upload">
          <view class="image-list">
            <view 
              wx:for="{{screenshots}}" 
              wx:key="*this"
              class="image-item"
              bindtap="previewImage"
              data-index="{{index}}"
            >
              <image class="uploaded-image" src="{{item}}" mode="aspectFill"></image>
              <view class="remove-image" bindtap="removeImage" data-index="{{index}}" catchtap="true">
                <text class="remove-icon">×</text>
              </view>
            </view>
            <view 
              wx:if="{{screenshots.length < maxImages}}"
              class="add-image-btn"
              bindtap="chooseImages"
            >
              <text class="add-icon">+</text>
              <text class="add-text">添加图片</text>
            </view>
          </view>
          <text class="upload-tip">最多可上传{{maxImages}}张图片，帮助我们更好地了解问题</text>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <button 
          class="submit-btn"
          form-type="submit"
          loading="{{isSubmitting}}"
          disabled="{{!isFormValid || isSubmitting}}"
        >
          {{isSubmitting ? '提交中...' : '提交反馈'}}
        </button>
      </view>
    </form>
  </view>

  <!-- 联系我们 -->
  <view class="contact-section">
    <view class="section-header">
      <text class="section-title">联系我们</text>
      <text class="section-subtitle">其他联系方式</text>
    </view>
    
    <view class="contact-methods">
      <view class="contact-item" bindtap="copyText" data-text="{{serviceEmail}}">
        <view class="contact-icon">📧</view>
        <view class="contact-info">
          <text class="contact-title">邮箱客服</text>
          <text class="contact-detail">{{serviceEmail}}</text>
        </view>
        <view class="contact-action">复制</view>
      </view>
      
      <view class="contact-item" bindtap="joinQRGroup">
        <view class="contact-icon">👥</view>
        <view class="contact-info">
          <text class="contact-title">用户交流群</text>
          <text class="contact-detail">扫码加入官方交流群</text>
        </view>
        <view class="contact-action">扫码</view>
      </view>

      <view class="contact-item" bindtap="callService">
        <view class="contact-icon">📞</view>
        <view class="contact-info">
          <text class="contact-title">客服热线</text>
          <text class="contact-detail">{{servicePhone}}</text>
        </view>
        <view class="contact-action">拨打</view>
      </view>
    </view>
  </view>

  <!-- 成功提示弹窗 -->
  <view wx:if="{{showSuccessModal}}" class="modal-overlay" bindtap="hideSuccessModal">
    <view class="modal-content" catchtap="preventClose">
      <view class="success-icon">✅</view>
      <text class="success-title">反馈提交成功</text>
      <text class="success-message">感谢您的反馈！我们会在3个工作日内处理并回复您。</text>
      <button class="modal-btn" bindtap="hideSuccessModal">我知道了</button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>
</view>

