const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 解除情侣绑定
 * 使用软删除方式，将couple记录状态设为inactive，支持重新绑定
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  console.log('开始解除情侣绑定, openid:', openid)
  
  try {
    // 获取当前用户信息
    const userResult = await db.collection('users')
      .where({
        _openid: openid // 使用正确的字段名 _openid
      })
      .get()
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        error: 'USER_NOT_FOUND',
        message: '用户不存在'
      }
    }
    
    const user = userResult.data[0]
    const coupleId = user.partnerId // 使用正确的字段名 partnerId
    
    console.log('用户信息:', {
      _id: user._id,
      _openid: user._openid,
      partnerId: user.partnerId
    })
    
    if (!coupleId) {
      return {
        success: false,
        error: 'NOT_IN_COUPLE',
        message: '当前没有绑定情侣关系'
      }
    }
    
    // 获取情侣关系信息
    const coupleResult = await db.collection('couples')
      .doc(coupleId)
      .get()
    
    if (!coupleResult.data) {
      return {
        success: false,
        error: 'COUPLE_NOT_FOUND',
        message: '情侣关系不存在'
      }
    }
    
    const couple = coupleResult.data
    const partnerOpenid = couple.user1Id === openid ? couple.user2Id : couple.user1Id
    
    console.log('情侣关系信息:', {
      _id: couple._id,
      user1Id: couple.user1Id,
      user2Id: couple.user2Id,
      status: couple.status,
      partnerOpenid: partnerOpenid
    })
    
    // 获取伴侣信息
    const partnerResult = await db.collection('users')
      .where({
        _openid: partnerOpenid
      })
      .get()
    
    let partner = null
    if (partnerResult.data.length > 0) {
      partner = partnerResult.data[0]
    }
    
    // 开始事务处理
    const transaction = await db.startTransaction()
    
    try {
      const unbindTime = new Date()
      
      // 1. 更新情侣关系状态为inactive（软删除）
      await transaction.collection('couples')
        .doc(coupleId)
        .update({
          data: {
            status: 'inactive',
            unbindTime: unbindTime,
            unbindBy: openid,
            updateTime: unbindTime
          }
        })
      
      console.log('已将couples记录状态更新为inactive')
      
      // 2. 更新当前用户信息，清除情侣关系
      await transaction.collection('users')
        .doc(user._id)
        .update({
          data: {
            partnerId: null,
            relationshipStatus: 'single',
            unbindTime: unbindTime,
            updateTime: unbindTime
          }
        })
      
      console.log('已更新当前用户信息')
      
      // 3. 更新对方用户信息，清除情侣关系
      if (partner) {
        await transaction.collection('users')
          .doc(partner._id)
          .update({
            data: {
              partnerId: null,
              relationshipStatus: 'single',
              unbindTime: unbindTime,
              updateTime: unbindTime
            }
          })
        
        console.log('已更新伴侣用户信息')
      }
      
      // 4. 彻底删除所有相关的券数据 - 修复问题2：彻底清除所有券数据
      const couponDeleteResult = await transaction.collection('coupons')
        .where({
          $or: [
            {
              $and: [
                { creatorId: openid },
                { recipientId: partnerOpenid }
              ]
            },
            {
              $and: [
                { creatorId: partnerOpenid },
                { recipientId: openid }
              ]
            }
          ]
        })
        .remove()
      
      console.log('已删除相关券数据，影响数量:', couponDeleteResult.stats.removed)

      // 5. 彻底删除所有相关的通知消息 - 修复问题2：彻底清除所有通知数据
      const notificationDeleteResult = await transaction.collection('notifications')
        .where({
          $or: [
            {
              $and: [
                { senderId: openid },
                { recipientId: partnerOpenid }
              ]
            },
            {
              $and: [
                { senderId: partnerOpenid },
                { recipientId: openid }
              ]
            }
          ]
        })
        .remove()

      console.log('已删除所有相关通知，影响数量:', notificationDeleteResult.stats.removed)

      // 修复问题2：移除解绑通知发送逻辑，不需要发送通知
      console.log('解绑操作完成，不发送通知给对方')
      
      // 提交事务
      await transaction.commit()
      console.log('解绑事务提交成功')
      
      return {
        success: true,
        message: '情侣关系已成功解除，所有相关数据已清除',
        data: {
          unbindTime: unbindTime,
          deletedCoupons: couponDeleteResult.stats.removed,
          deletedNotifications: notificationDeleteResult.stats.removed
        }
      }
      
    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback()
      console.error('事务执行失败，已回滚:', transactionError)
      throw transactionError
    }
    
  } catch (error) {
    console.error('解除情侣绑定失败:', error)
    return {
      success: false,
      error: 'SYSTEM_ERROR',
      message: '系统错误，请稍后重试',
      details: error.message
    }
  }
} 