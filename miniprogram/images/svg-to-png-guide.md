# SVG转PNG图标转换指南

## 📁 文件清单

已创建的SVG图标文件：

### 普通状态（灰色 #7A7E83）
- `tab-home-normal.svg` - 首页图标（普通状态）
- `tab-coupon-normal.svg` - 我的券图标（普通状态）
- `tab-message-normal.svg` - 消息图标（普通状态）
- `tab-profile-normal.svg` - 我的图标（普通状态）

### 选中状态（粉色 #FF69B4）
- `tab-home-active.svg` - 首页图标（选中状态）
- `tab-coupon-active.svg` - 我的券图标（选中状态）
- `tab-message-active.svg` - 消息图标（选中状态）
- `tab-profile-active.svg` - 我的图标（选中状态）

## 🔄 转换要求

### 技术规格
- **输出尺寸**：81x81 像素
- **输出格式**：PNG
- **背景**：透明
- **颜色深度**：32位 RGBA

### 文件命名
转换后的PNG文件应命名为：
- `tab-home-normal.png`
- `tab-home-active.png`
- `tab-coupon-normal.png`
- `tab-coupon-active.png`
- `tab-message-normal.png`
- `tab-message-active.png`
- `tab-profile-normal.png`
- `tab-profile-active.png`

## 🛠️ 转换方法

### 方法一：在线转换工具（推荐）

1. **访问在线转换网站**：
   - https://svgtopng.com/
   - https://convertio.co/svg-png/
   - https://cloudconvert.com/svg-to-png

2. **转换步骤**：
   - 上传SVG文件
   - 设置输出尺寸为 81x81 像素
   - 确保背景透明
   - 下载PNG文件

### 方法二：使用Figma（设计师推荐）

1. **导入SVG**：
   - 在Figma中创建新文件
   - 拖拽SVG文件到画布

2. **调整尺寸**：
   - 选中图标
   - 在右侧面板设置尺寸为 81x81

3. **导出PNG**：
   - 选中图标
   - 点击右下角"Export"
   - 选择PNG格式
   - 设置2x导出（实际输出162x162，系统会自动缩放）

### 方法三：使用Photoshop

1. **打开SVG**：
   - File → Open
   - 选择SVG文件
   - 设置尺寸为 81x81 像素，分辨率72 DPI

2. **导出PNG**：
   - File → Export → Export As
   - 选择PNG格式
   - 确保透明度选项开启

### 方法四：命令行工具（开发者）

#### macOS/Linux
```bash
# 安装 librsvg
brew install librsvg  # macOS
sudo apt-get install librsvg2-bin  # Ubuntu

# 批量转换
for file in tab-*-normal.svg; do
    rsvg-convert -w 81 -h 81 "$file" > "${file%.svg}.png"
done

for file in tab-*-active.svg; do
    rsvg-convert -w 81 -h 81 "$file" > "${file%.svg}.png"
done
```

#### 使用ImageMagick
```bash
# 安装 ImageMagick
brew install imagemagick  # macOS

# 转换单个文件
convert -background none -size 81x81 tab-home-normal.svg tab-home-normal.png

# 批量转换
for file in *.svg; do
    convert -background none -size 81x81 "$file" "${file%.svg}.png"
done
```

## ✅ 质量检查

转换完成后，请检查：

1. **文件大小**：每个PNG文件应小于40KB
2. **尺寸**：确认为81x81像素
3. **透明背景**：背景应为透明，无白色或其他颜色
4. **颜色准确性**：
   - 普通状态应为灰色 (#7A7E83)
   - 选中状态应为粉色 (#FF69B4)
5. **清晰度**：图标边缘清晰，无锯齿

## 📱 测试建议

1. **在微信开发者工具中测试**：
   - 将PNG文件放入 `miniprogram/images/` 目录
   - 编译小程序
   - 检查底部导航图标显示效果

2. **真机测试**：
   - 在不同设备上测试显示效果
   - 确认选中和未选中状态切换正常

## 🎨 设计说明

### 图标含义
- **首页**：心形房子，象征温馨的情侣小屋
- **我的券**：爱心装饰的券卡，代表情侣之间的承诺
- **消息**：爱心聊天气泡，表达情侣间的甜蜜交流
- **我的**：带心形装饰的用户头像，体现个人情侣身份

### 设计特色
- 统一的情侣粉色主题
- 简约线条风格
- 每个图标都融入爱心元素
- 适合情侣信任券的产品定位

## 🚀 部署步骤

1. **转换所有SVG文件为PNG**
2. **将PNG文件放入 `miniprogram/images/` 目录**
3. **确认 `app.json` 中的路径正确**
4. **编译并测试小程序**
5. **提交代码并部署**

转换完成后，你的小程序将拥有美观统一的底部导航图标！🎉 