/* settings.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 50%, #F0F8FF 100%);
  padding: 30rpx;
  box-sizing: border-box;
  padding-bottom: 150rpx;
}

/* 设置分组 */
.settings-group {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10rpx);
}

.group-title {
  padding: 24rpx 30rpx;
  font-size: 26rpx;
  color: #666;
  font-weight: 600;
  background: rgba(248, 249, 250, 0.8);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  display: block;
}

/* 设置项 */
.setting-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.action-item:active {
  background: rgba(255, 105, 180, 0.05);
}

.setting-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-right: 20rpx;
}

.setting-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.setting-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.setting-arrow {
  font-size: 20rpx;
  color: #ccc;
  font-weight: bold;
}

/* 开关样式 */
switch {
  transform: scale(0.8);
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80vw;
  max-width: 500rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.3);
}

.modal-content.large {
  width: 90vw;
  max-width: 600rpx;
  max-height: 70vh;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.close-btn {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border: none;
  border-radius: 50%;
  font-size: 20rpx;
  color: #666;
}

.modal-body {
  padding: 30rpx;
}

.modal-message {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.modal-scroll {
  max-height: 400rpx;
}

.privacy-content {
  padding: 20rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.privacy-section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-top: 20rpx;
}

.privacy-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.modal-actions {
  display: flex;
  border-top: 1rpx solid #e9ecef;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border-right: 1rpx solid #e9ecef;
}

.confirm-btn {
  background: #FF69B4;
  color: white;
}

.primary-btn {
  width: 100%;
  height: 80rpx;
  background: #FF69B4;
  color: white;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF69B4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 20rpx;
  }
  
  .setting-item {
    padding: 24rpx 20rpx;
  }
  
  .modal-content {
    width: 85vw;
  }
  
  .modal-content.large {
    width: 95vw;
  }
}

