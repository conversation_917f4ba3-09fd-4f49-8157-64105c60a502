情侣信誉券问题点
1.首页点击使用模版跳转创建信任券页面后，信息带进来了，但是预览界面的颜色没变，图标也没变，没有带过来，是因为首页模版中的图标和颜色跟创建页面的图标和颜色没有保持一致，因此需要确保两边的图标和颜色都存在。

2.首页-他的爱意券缺少缺省图标或插画

3.信誉券卡片需要有阴影，这样会显得有立体感，更真实。

4.他的爱意券和浪漫模版库tab页签如果能支持手滑动切换更好，现在是必须得手动点击tab页签才可以切换。

5.首页浪漫模版库下的卡片样式有错位，展示不正常，另外去掉：使用此模版按钮，直接点击卡片就可以跳转。

6.浪漫模版库页签下，点击使用某个模板，进入后不要弹窗提示：模版已应用。

7.信任券详情页中，券状态下，发送时间和接收时间是同时的，因此去掉接收时间。另外时间需要包含发送时间、申兑时间、兑现时间，过期时间。
发送时间：点击发券记录的时间
申兑时间：申请兑换记录的时间
兑现时间：同意兑换记录的时间
过期时间：券信息中定义的过期时间。
逻辑是：如果直到过期，还没有兑现，状态未改变，说明已过期，则展示过期时间，不展示兑现时间。

8.我的券模块中，已兑现和已过期这两种状态是互斥的，已兑现是在为过期前使用兑现了承诺，已过期是没有兑现承诺，超过了有效期。已过期的状态只会出现在已失效页签下。因此如果是已经兑现的券，在已兑现页签下展示券图标、券名称、券类型，和券状态，以及兑现时间

9.我的模块下，个人头像更换后，另一个人看到的头像是空白展示，有问题。

10.我的模块下，姓名、签名修改保存不成功，展示的还是修改前的。

11.浪漫模版库下应该也需要按照类型去区分，否则模版会过多展示。要分类展示。

12.消息模块下，新券提醒页签下展示的是未兑现之前的，如果申请兑现后，应该在兑现申请下，兑现完成就在兑现完成页签，如果券过期了，就展示到过期提醒页签下。

13.创建信任券的详情页，券类型的展现形式不好看，应该是按照标签找事，不要图标，就展示标签，区分颜色。

14.创建信任券的详情页，下方应该有一个保存为模版的按钮，与发送给他按钮在同一行，一个在左，一个在右。

15.信誉券发送后，应该默认跳转到我的券模块-我发送的页签下。

16.消息模块的徽标展示逻辑还是有点问题，对方发了券之后，我切换页签后，有新消息发过来了，但是徽标没有刷新展示，我必须得重新刷新进入小程序才可以看到。应该是有新消息发送过来，就需要跟新消息同步刷新展示。

17.查看发送的券详情，券状态需要调整，收到券后，状态应该是待兑现，不用展示别的，当申请兑现后，状态改为申请兑现中，·同意兑现后，状态改为已兑现。 