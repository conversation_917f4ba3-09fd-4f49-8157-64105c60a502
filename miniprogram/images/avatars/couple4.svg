<svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#A8E6CF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#81C784;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="40" cy="40" r="40" fill="url(#gradient4)"/>
  <ellipse cx="40" cy="35" rx="15" ry="10" fill="white" opacity="0.7"/>
  <path d="M30 50 Q40 60 50 50" stroke="white" stroke-width="2" fill="none" opacity="0.8"/>
</svg> 