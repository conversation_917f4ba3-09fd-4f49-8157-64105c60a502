const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 获取券详情
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  console.log('获取券详情, openid:', openid, 'couponId:', event.couponId);
  
  try {
    const { couponId } = event
    
    // 参数验证
    if (!couponId) {
      return {
        success: false,
        error: 'MISSING_PARAMS',
        message: '缺少必要参数：couponId'
      }
    }
    
    // 获取券详情
    const couponResult = await db.collection('coupons')
      .doc(couponId)
      .get()
    
    if (!couponResult.data) {
      return {
        success: false,
        error: 'COUPON_NOT_FOUND',
        message: '券不存在'
      }
    }
    
    const coupon = couponResult.data
    console.log('查询到券信息:', {
      _id: coupon._id,
      name: coupon.name,
      creatorId: coupon.creatorId,
      recipientId: coupon.recipientId,
      status: coupon.status
    });
    
    // 修复：使用正确的字段名检查权限
    const userResult = await db.collection('users')
      .where({
        _openid: openid
      })
      .get()
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        error: 'USER_NOT_FOUND',
        message: '用户不存在'
      }
    }
    
    const user = userResult.data[0]
    console.log('查询到用户信息:', {
      _id: user._id,
      _openid: user._openid,
      partnerId: user.partnerId
    });
    
    // 修复：使用正确的字段名检查权限
    const hasPermission = 
      coupon.creatorId === openid || 
      coupon.recipientId === openid
    
    if (!hasPermission) {
      console.log('权限检查失败:', {
        creatorId: coupon.creatorId,
        recipientId: coupon.recipientId,
        userOpenid: openid
      });
      return {
        success: false,
        error: 'NO_PERMISSION',
        message: '无权限查看此券详情'
      }
    }
    
    // 修复：获取创建者和接收者信息
    const [creatorResult, recipientResult] = await Promise.all([
      db.collection('users').where({ _openid: coupon.creatorId }).get(),
      coupon.recipientId ? db.collection('users').where({ _openid: coupon.recipientId }).get() : Promise.resolve({ data: [] })
    ])
    
    const creator = creatorResult.data[0];
    const recipient = recipientResult.data[0];
    
    console.log('查询到创建者信息:', creator ? {
      _id: creator._id,
      _openid: creator._openid,
      nickName: creator.nickName
    } : null);
    
    console.log('查询到接收者信息:', recipient ? {
      _id: recipient._id,
      _openid: recipient._openid,
      nickName: recipient.nickName
    } : null);
    
    // 组装详细信息
    const couponDetail = {
      ...coupon,
      title: coupon.name,           // 添加：向后兼容
      expiresAt: coupon.expiryDate, // 添加：向后兼容
      creator: creator ? {
        _id: creator._id,
        _openid: creator._openid,
        nickName: creator.nickName,
        avatarUrl: creator.avatarUrl
      } : null,
      recipient: recipient ? {
        _id: recipient._id,
        _openid: recipient._openid,
        nickName: recipient.nickName,
        avatarUrl: recipient.avatarUrl
      } : null,
      // 添加便于前端使用的字段
      creatorNickname: creator?.nickName || '未知用户',
      creatorAvatar: creator?.avatarUrl || '/images/default-avatar.png',
      recipientNickname: recipient?.nickName || '未知用户',
      recipientAvatar: recipient?.avatarUrl || '/images/default-avatar.png'
    }
    
    // 获取相关的兑换记录（如果有）
    if (coupon.status === 'pending_redeem' || coupon.status === 'redeemed') {
      try {
        const redemptionResult = await db.collection('couponRedemptions')
          .where({
            couponId: couponId
          })
          .orderBy('createTime', 'desc')
          .limit(1)
          .get()
        
        if (redemptionResult.data.length > 0) {
          couponDetail.redemption = redemptionResult.data[0]
        }
      } catch (redemptionError) {
        console.log('获取兑换记录失败（可能是集合不存在）:', redemptionError);
        // 兑换记录获取失败不影响券详情返回
      }
    }
    
    console.log('返回券详情成功');
    
    return {
      success: true,
      data: couponDetail
    }
    
  } catch (error) {
    console.error('获取券详情失败:', error)
    return {
      success: false,
      error: 'SYSTEM_ERROR',
      message: '系统错误，请稍后重试: ' + error.message
    }
  }
} 
 