/* pages/myCoupons/myCoupons.wxss */
@import "../../app.wxss";

/* 页面容器 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeef7 0%, #f8e8ff 100%);
  padding-top: 20rpx;
}

/* 页面标题区域 - 移除 */
.page-header {
  display: none;
}

/* 主Tab导航 */
.main-tabs-container {
  position: relative;
  margin: 0 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 20rpx rgba(255, 105, 180, 0.15);
  overflow: hidden;
}

.main-tabs {
  display: flex;
  position: relative;
  z-index: 2;
}

.main-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 20rpx;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.main-tab.active {
  color: #ff69b4;
}

.tab-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.tab-label {
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.tab-count {
  background: #ff69b4;
  color: white;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  min-width: 24rpx;
  text-align: center;
  transform: scale(0.9);
  transition: all 0.3s ease;
}

.main-tab.active .tab-count {
  transform: scale(1);
  background: #ff1493;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50%;
  height: 6rpx;
  background: linear-gradient(90deg, #ff69b4, #ff1493);
  border-radius: 3rpx 3rpx 0 0;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

/* 子Tab导航 */
.sub-tabs-container {
  margin: 0 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  backdrop-filter: blur(8rpx);
  box-shadow: 0 2rpx 12rpx rgba(255, 105, 180, 0.1);
}

.sub-tabs-scroll {
  width: 100%;
  white-space: nowrap;
}

.sub-tabs {
  display: flex;
  padding: 8rpx;
}

.sub-tab {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  margin: 0 6rpx;
  border-radius: 12rpx;
  background: transparent;
  transition: all 0.3s ease;
  position: relative;
}

.sub-tab.active {
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  color: white;
  transform: scale(1.05);
}

.sub-tab-label {
  font-size: 24rpx;
  font-weight: 500;
  margin-right: 8rpx;
}

.sub-tab-count {
  background: rgba(255, 255, 255, 0.9);
  color: #ff69b4;
  font-size: 18rpx;
  font-weight: 600;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  min-width: 20rpx;
  text-align: center;
}

.sub-tab.active .sub-tab-count {
  background: rgba(255, 255, 255, 0.95);
  color: #ff1493;
}

/* 内容区域 */
.content-container {
  flex: 1;
  margin: 0 30rpx;
  transition: all 0.3s ease;
}

/* 动画类 */
.tab-switching {
  opacity: 0.7;
  transform: scale(0.98);
}

.content-fade-out {
  opacity: 0;
  transform: translateY(20rpx);
}

.content-fade-in {
  opacity: 1;
  transform: translateY(0);
}

.content-slide-out {
  opacity: 0;
  transform: translateX(-30rpx);
}

.content-slide-in {
  opacity: 1;
  transform: translateX(0);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.loading-heart {
  font-size: 80rpx;
  animation: heartbeat 1.5s ease-in-out infinite;
  margin-bottom: 20rpx;
}

.loading-dots {
  display: flex;
  gap: 12rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #ff69b4;
  animation: bounce 1.4s ease-in-out infinite both;
}

.dot2 { animation-delay: 0.16s; }
.dot3 { animation-delay: 0.32s; }

.loading-text {
  font-size: 28rpx;
  color: #999;
  opacity: 0.8;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-animation {
  position: relative;
  margin-bottom: 40rpx;
}

.empty-icon {
  font-size: 100rpx;
  position: relative;
  z-index: 2;
  animation: float 3s ease-in-out infinite;
}

.empty-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring {
  position: absolute;
  border: 2rpx solid rgba(255, 105, 180, 0.2);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.ring1 {
  width: 120rpx;
  height: 120rpx;
  margin: -60rpx 0 0 -60rpx;
}

.ring2 {
  width: 160rpx;
  height: 160rpx;
  margin: -80rpx 0 0 -80rpx;
  animation-delay: 0.5s;
}

.ring3 {
  width: 200rpx;
  height: 200rpx;
  margin: -100rpx 0 0 -100rpx;
  animation-delay: 1s;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
  line-height: 1.6;
}

/* 券列表 */
.coupons-scroll {
  height: calc(100vh - 300rpx);
}

.coupons-list {
  padding-bottom: 40rpx;
}

.coupon-item-wrapper {
  margin-bottom: 24rpx;
}

.coupon-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 30rpx;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 180, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.coupon-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.2);
}

.coupon-main {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.coupon-icon-container {
  position: relative;
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffe0f0, #ffb3d9);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.2);
}

.coupon-icon {
  font-size: 36rpx;
  position: relative;
  z-index: 2;
}

.icon-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 105, 180, 0.3) 0%, transparent 70%);
  border-radius: 20rpx;
  animation: glow 2s ease-in-out infinite alternate;
}

.coupon-content {
  flex: 1;
  min-width: 0; /* 防止内容溢出 */
}

.coupon-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.4;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.coupon-description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.5;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 我收到的券信息样式 */
.coupon-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
}

.coupon-type {
  display: flex;
  align-items: center;
}

.type-text {
  font-size: 22rpx;
  color: #ff69b4;
  background: rgba(255, 105, 180, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.coupon-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 22rpx;
  color: #666;
  background: rgba(102, 102, 102, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

/* 我发送的券信息样式 */
.coupon-meta-sent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.sent-time {
  display: flex;
  align-items: center;
}

.sent-time-text {
  font-size: 22rpx;
  color: #999;
  background: rgba(153, 153, 153, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.coupon-expiry {
  display: flex;
  align-items: center;
}

.expiry-text {
  font-size: 22rpx;
  color: #999;
  background: rgba(153, 153, 153, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

/* 券装饰元素 */
.coupon-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.decoration-heart {
  position: absolute;
  font-size: 20rpx;
  opacity: 0.3;
  animation: float 4s ease-in-out infinite;
}

.heart1 {
  top: 20rpx;
  right: 30rpx;
  animation-delay: 0s;
}

.heart2 {
  bottom: 20rpx;
  left: 30rpx;
  animation-delay: 2s;
}

/* 底部安全区域 */
.bottom-safe-area {
  height: 40rpx;
}

/* 动画定义 */
@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes bounce {
  0%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-12rpx); }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.1; }
}

@keyframes glow {
  0% { opacity: 0.5; }
  100% { opacity: 0.8; }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .page-header {
    padding: 30rpx 20rpx 15rpx;
  }
  
  .main-tabs-container,
  .sub-tabs-container {
    margin-left: 20rpx;
    margin-right: 20rpx;
  }
  
  .coupons-scroll {
    padding: 0 20rpx;
  }
  
  .coupon-card {
    padding: 20rpx;
  }
  
  .coupon-main {
    gap: 15rpx;
  }
  
  .coupon-icon-container {
    width: 70rpx;
    height: 70rpx;
  }
  
  .coupon-icon {
    font-size: 35rpx;
  }
}
