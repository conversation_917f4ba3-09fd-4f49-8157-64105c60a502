{"component": true, "componentGenerics": {}, "usingComponents": {}, "componentPlaceholder": {}, "styleIsolation": "isolated", "description": "券卡片组件，用于展示信任券信息，支持多种状态和类型，插槽内容定制", "version": "1.0.0", "author": "情侣信任券小程序团队", "category": "Display Components", "keywords": ["卡片", "信任券", "展示", "状态"], "repository": {"type": "git", "url": "https://github.com/coupleapp/miniprogram-components"}, "license": "MIT", "properties": {"title": {"type": "String", "description": "券标题", "default": "", "required": true}, "description": {"type": "String", "description": "券描述", "default": "", "required": false}, "status": {"type": "String", "description": "券状态", "default": "received", "enum": ["received", "pending_redeem", "redeemed", "expired", "sent"], "required": false}, "type": {"type": "String", "description": "券类型", "default": "normal", "enum": ["special", "normal", "limited", "holiday", "anniversary"], "required": false}, "sender": {"type": "String", "description": "发送方", "default": "", "required": false}, "receiver": {"type": "String", "description": "接收方", "default": "", "required": false}, "expiryDate": {"type": "String", "description": "有效期日期", "default": "", "required": false}, "createTime": {"type": "String", "description": "创建时间", "default": "", "required": false}, "couponId": {"type": "String", "description": "券ID", "default": "", "required": false}, "showStatus": {"type": "Boolean", "description": "是否显示状态标识", "default": true, "required": false}, "showActions": {"type": "Boolean", "description": "是否显示操作按钮", "default": false, "required": false}, "showDecoration": {"type": "Boolean", "description": "是否显示装饰", "default": true, "required": false}, "clickable": {"type": "Boolean", "description": "是否可点击", "default": true, "required": false}, "effect": {"type": "String", "description": "特殊效果", "default": "", "enum": ["", "glow", "pulse"], "required": false}}, "events": {"tap": {"description": "卡片点击事件", "parameters": {"couponId": "String - 券ID", "title": "String - 券标题", "status": "String - 券状态", "type": "String - 券类型", "position": "Object - 点击位置", "timeStamp": "Number - 时间戳"}}, "expired": {"description": "券过期事件", "parameters": {"couponId": "String - 券ID", "title": "String - 券标题", "expiryDate": "String - 过期日期"}}}, "methods": {"getCouponInfo": {"description": "获取券的完整信息", "parameters": {}, "returns": "Object - 券信息对象"}, "setEffect": {"description": "设置特殊效果", "parameters": {"effect": "String - 效果名称"}, "returns": "void"}, "removeEffect": {"description": "移除特殊效果", "parameters": {}, "returns": "void"}, "checkExpiry": {"description": "检查是否过期", "parameters": {}, "returns": "void"}}, "slots": {"content": {"description": "自定义内容插槽，显示在描述区域"}, "actions": {"description": "自定义操作按钮插槽，显示在卡片底部"}}, "examples": [{"title": "基础使用", "description": "显示一张基本的信任券卡片", "code": {"wxml": "<coupon-card title=\"一次不生气券\" description=\"生气时可以使用此券\" status=\"received\" sender=\"亲爱的\" type=\"special\" bind:tap=\"onCouponTap\" />", "js": "Page({ onCouponTap(e) { console.log('券被点击:', e.detail); } })"}}, {"title": "自定义内容", "description": "使用插槽自定义卡片内容", "code": {"wxml": "<coupon-card title=\"特殊券\" show-actions=\"{{true}}\"><view slot=\"content\">自定义内容区域</view><button slot=\"actions\" size=\"mini\">立即使用</button></coupon-card>"}}, {"title": "不同状态", "description": "展示不同状态的券", "code": {"wxml": "<coupon-card title=\"已核销券\" status=\"redeemed\" type=\"normal\" />\n<coupon-card title=\"待核销券\" status=\"pending_redeem\" type=\"limited\" />\n<coupon-card title=\"已过期券\" status=\"expired\" type=\"holiday\" />"}}, {"title": "特殊效果", "description": "添加特殊视觉效果", "code": {"wxml": "<coupon-card title=\"发光券\" effect=\"glow\" type=\"special\" />\n<coupon-card title=\"脉冲券\" effect=\"pulse\" type=\"anniversary\" />"}}], "changelog": [{"version": "1.0.0", "date": "2024-01-01", "changes": ["初始版本发布", "支持多种券状态展示", "支持多种券类型样式", "支持自定义内容插槽", "支持点击交互", "支持过期检查", "支持特殊视觉效果", "支持响应式设计", "支持深色模式", "支持无障碍功能"]}], "dependencies": {}, "devDependencies": {}, "peerDependencies": {}, "browserslist": ["iOS >= 9", "Android >= 5"], "miniProgramMinVersion": "2.10.0", "tags": ["UI", "Display", "Card", "Coupon", "Interactive"], "screenshots": ["https://example.com/screenshots/coupon-card-basic.png", "https://example.com/screenshots/coupon-card-states.png", "https://example.com/screenshots/coupon-card-types.png"]}