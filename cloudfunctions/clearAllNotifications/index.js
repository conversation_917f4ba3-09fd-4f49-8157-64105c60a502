const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 清空所有通知
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 删除用户所有通知 - 修复：使用recipientId字段
    const deleteResult = await db.collection('notifications')
      .where({
        recipientId: openid
      })
      .remove()
    
    return {
      success: true,
      deletedCount: deleteResult.stats.removed,
      message: `已清空 ${deleteResult.stats.removed} 条通知`
    }
    
  } catch (error) {
    console.error('清空通知失败:', error)
    return {
      success: false,
      error: 'SYSTEM_ERROR',
      message: '系统错误，请稍后重试'
    }
  }
} 