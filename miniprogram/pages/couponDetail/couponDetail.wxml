<!--pages/couponDetail/couponDetail.wxml-->
<view class="container" style="background: {{themeGradient}};">
  <view wx:if="{{isLoading}}" class="loading-full-page">
    <view class="loading-spinner-global"></view>
    <text>加载券详情...</text>
  </view>

  <block wx:else>
    <view wx:if="{{!coupon}}" class="empty-state-full card">
      <image src="/images/coupon-not-found.svg" class="empty-icon"/>
      <text class="message-title">哎呀，没找到这张券</text>
      <text class="message-body">可能是链接失效了，或者券已被删除。</text>
      <button class="secondary-button" bindtap="wx.navigateBack">返回上一页</button>
    </view>

    <view wx:if="{{coupon}}" class="coupon-detail-container">
      <!-- Coupon Visual Display -->
      <view class="coupon-visual-card" style="background: {{coupon.bgColor || 'linear-gradient(135deg, #FF69B4, #FFB6C1)'}};">
        <view class="coupon-icon-large">{{coupon.icon || '💖'}}</view>
        <text class="coupon-title-large">{{coupon.name}}</text>
        <text class="coupon-description-detail">{{coupon.description}}</text>
        <view class="coupon-meta-row">
          <view class="meta-item">
            <text class="meta-label">来自:</text>
            <text class="meta-value">{{coupon.creatorNickname}}</text>
          </view>
          <view class="meta-item">
            <text class="meta-label">赠与:</text>
            <text class="meta-value">{{coupon.recipientNickname}}</text>
          </view>
        </view>
         <view class="coupon-expiry-detail">
          有效期至: {{formattedTimes.expiryDate}}
        </view>
      </view>

      <!-- Coupon Status & Info Section -->
      <view class="info-section card">
        <view class="info-header">
          <text class="section-title">券状态</text>
          <text class="status-tag status-{{coupon.status}}">{{dynamicStatusText}}</text>
        </view>

        <view class="info-item" wx:if="{{coupon.redeemedAt && coupon.redemptionNote}}">
            <text class="info-label">兑现留言:</text>
            <text class="info-value notes">{{coupon.redemptionNote}}</text>
        </view>
        <!-- 时间轴显示 -->
        <view class="timeline-section" wx:if="{{formattedTimes.sendTime}}">
          <view class="timeline-title">📅 时间轴</view>

          <!-- 发送时间 -->
          <view class="timeline-item completed">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <text class="timeline-label">发送时间</text>
              <text class="timeline-time">{{formattedTimes.sendTime}}</text>
            </view>
          </view>

          <!-- 申请兑现时间 -->
          <view class="timeline-item {{formattedTimes.requestTime ? 'completed' : 'pending'}}" wx:if="{{coupon.status !== 'received'}}">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <text class="timeline-label">申请兑现</text>
              <text class="timeline-time" wx:if="{{formattedTimes.requestTime}}">{{formattedTimes.requestTime}}</text>
              <text class="timeline-time pending" wx:else>待申请</text>
            </view>
          </view>

          <!-- 最终时间（兑现时间或过期时间） -->
          <view class="timeline-item {{formattedTimes.finalTime ? 'completed' : 'pending'}}" wx:if="{{formattedTimes.finalTimeLabel}}">
            <view class="timeline-dot"></view>
            <view class="timeline-content">
              <text class="timeline-label">{{formattedTimes.finalTimeLabel}}</text>
              <text class="timeline-time">{{formattedTimes.finalTime}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- Actions Section -->
      <view class="actions-section card" wx:if="{{userInfo}}">
        <!-- 接收方操作区域 -->
        <view wx:if="{{isRecipient}}" class="action-area">
          <view class="action-title">📝 接收方操作说明</view>
          <view class="action-desc">
            <text wx:if="{{coupon.status === 'received'}}">💖 券状态：待兑现。点击"申请兑现"按钮向对方申请使用</text>
            <text wx:elif="{{coupon.status === 'pending_redeem'}}">⏳ 券状态：申请兑现中。请等待对方确认</text>
            <text wx:elif="{{coupon.status === 'redeemed'}}">🎉 券状态：已兑现。恭喜！这张券已成功兑现</text>
            <text wx:else="{{coupon.status === 'expired' || coupon.status === 'cancelled'}}">😔 这张券已失效，无法使用</text>
          </view>

          <button
            wx:if="{{coupon.status === 'received' && canBeRedeemed}}"
            class="primary-button action-button"
            bindtap="requestRedeem">
            申请兑现
          </button>
        </view>

        <!-- 创建方操作区域 -->
        <view wx:if="{{isCreator}}" class="action-area">
          <view class="action-title">🎯 创建方操作说明</view>
          <view class="action-desc">
            <text wx:if="{{coupon.status === 'received'}}">✅ 券状态：待兑现。等待对方申请兑现</text>
            <text wx:elif="{{coupon.status === 'pending_redeem'}}">💝 券状态：申请兑现中。对方已申请兑现，你可以选择同意或拒绝</text>
            <text wx:elif="{{coupon.status === 'redeemed'}}">🎊 券状态：已兑现。承诺已履行</text>
            <text wx:else="{{coupon.status === 'expired' || coupon.status === 'cancelled'}}">⚰️ 券已失效</text>
          </view>

          <button
            wx:if="{{canCreatorConfirmRedemption}}"
            class="primary-button action-button"
            bindtap="confirmRedemption">
            同意TA兑现
          </button>
        </view>
      </view>

      <view wx:if="{{!userInfo}}" class="login-prompt-card card">
        <text>登录后可进行更多操作</text>
        <button class="primary-button mt-2 full-width" bindtap="app.performLogin">去登录</button>
      </view>
    </view>
  </block>

  <!-- Redemption Modal -->
  <view wx:if="{{showRedeemModal}}" class="modal-overlay" bindtap="closeRedeemModal">
    <view class="modal-content redeem-modal" catchtap>
      <text class="modal-title">确认兑现</text>
      <text class="modal-subtitle">为这次兑现添加一句甜甜的话吧~ (可选)</text>
      <textarea class="modal-textarea" 
                placeholder="例如：收到你的爱啦，真开心！" 
                value="{{redemptionNote}}" 
                bindinput="onRedemptionNoteInput"
                maxlength="50"/>
      <view class="modal-actions">
        <button class="modal-button secondary" bindtap="closeRedeemModal">取消</button>
        <button class="modal-button primary" bindtap="handleConfirmRedemption">确认兑现</button>
      </view>
    </view>
  </view>

</view>
