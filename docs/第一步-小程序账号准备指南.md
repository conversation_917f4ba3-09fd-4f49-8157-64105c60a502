# 第一步：小程序账号准备指南

## 📋 任务概述
在开始开发之前，您需要完成微信小程序的注册和基础配置。这是项目能够正常运行的前提条件。

## 🎯 本步骤目标
- ✅ 注册微信小程序账号
- ✅ 获取AppID
- ✅ 开通云开发服务
- ✅ 配置基础信息

## 📱 1. 注册微信小程序账号

### 1.1 访问微信公众平台
- 打开浏览器，访问：https://mp.weixin.qq.com/
- 点击右上角"立即注册"

### 1.2 选择账号类型
- 选择"小程序"
- 填写邮箱（建议使用企业邮箱）
- 设置密码并确认
- 点击"注册"

### 1.3 邮箱验证
- 登录您的邮箱
- 查收微信团队发送的确认邮件
- 点击邮件中的确认链接

### 1.4 信息登记
填写以下信息：
- **账号类型**：选择"个人"或"企业"（推荐企业）
- **主体信息**：
  - 个人：身份证信息、手机号验证
  - 企业：营业执照、组织机构代码等
- **管理员信息**：绑定管理员微信号

### 1.5 微信认证（可选但推荐）
- 费用：300元/年
- 好处：提升可信度、获得更多接口权限
- 流程：在设置→微信认证中申请

## ⚙️ 2. 基础配置

### 2.1 完善小程序信息
登录小程序后台，在"设置"→"基本信息"中：
- **小程序名称**：情侣信誉券
- **小程序介绍**：为情侣量身定制的信任券小程序，让承诺更有仪式感
- **服务类目**：
  - 一级类目：工具
  - 二级类目：效率
- **标签**：情侣、信任、券、承诺、互动

### 2.2 上传小程序头像
- 尺寸：144x144像素
- 格式：JPG/PNG
- 内容：设计一个温馨的情侣主题图标

### 2.3 设置客服功能
在"功能"→"客服"中：
- 添加客服人员微信号
- 设置自动回复消息

## ☁️ 3. 开通云开发

### 3.1 进入云开发控制台
- 在小程序后台，点击左侧"云开发"
- 如果是第一次使用，点击"开通"

### 3.2 创建云环境
- **环境名称**：couple-voucher-prod（生产环境）
- **环境ID**：系统自动生成，形如：couple-voucher-xxx
- **计费方式**：按量付费（初期推荐）

### 3.3 环境配置
创建完成后，记录以下信息：
- **环境ID**：例如 `couple-voucher-4g2h8j6k`
- **地域**：默认即可
- **基础环境版本**：选择最新版本

### 3.4 资源配额查看
在云开发控制台查看当前配额：
- **数据库**：读写次数、容量
- **云函数**：调用次数、执行时间
- **云存储**：下载流量、容量

## 🔑 4. 获取关键信息

完成上述步骤后，您应该得到以下关键信息：

### 4.1 AppID
- 位置：设置→基本信息→账号信息
- 格式：wx1234567890abcdef
- **请妥善保存，后续配置需要使用**

### 4.2 云环境ID
- 位置：云开发→设置→环境设置
- 格式：couple-voucher-4g2h8j6k
- **请妥善保存，后续配置需要使用**

### 4.3 AppSecret（如需要）
- 位置：设置→开发设置→开发者密钥
- 注意：小程序云开发模式通常不需要AppSecret

## 📋 5. 配置检查清单

完成注册后，请确认以下项目：

### 基础信息
- [ ] 小程序名称已设置
- [ ] 小程序介绍已填写
- [ ] 头像已上传
- [ ] 服务类目已选择

### 开发设置
- [ ] AppID已获取并记录
- [ ] 服务器域名已配置（如有需要）
- [ ] 业务域名已配置（如有需要）

### 云开发
- [ ] 云环境已创建
- [ ] 环境ID已记录
- [ ] 资源配额已了解

### 权限管理
- [ ] 开发者已添加（如有团队成员）
- [ ] 体验者已添加（用于测试）

## 🚨 6. 注意事项

### 6.1 费用说明
- **小程序注册**：免费
- **微信认证**：300元/年（可选）
- **云开发**：按量付费，初期费用很低

### 6.2 审核时间
- **小程序注册**：通常1-2工作日
- **微信认证**：通常3-7工作日
- **类目审核**：即时生效

### 6.3 常见问题
- **邮箱收不到验证码**：检查垃圾邮件
- **身份验证失败**：确保信息填写准确
- **云开发开通失败**：可能需要先完成实名认证

## 📞 7. 需要帮助时

如果在注册过程中遇到问题：
- **微信官方客服**：在微信公众平台右下角点击客服
- **开发文档**：https://developers.weixin.qq.com/miniprogram/dev/
- **社区支持**：微信开发者社区

## ✅ 8. 完成确认

当您完成所有步骤后，请将以下信息提供给开发团队：

```
小程序信息：
- AppID: wx________________
- 小程序名称: 情侣信誉券
- 云环境ID: _______________

账号状态：
- 注册状态: □已完成  □进行中
- 认证状态: □已认证  □未认证
- 云开发状态: □已开通  □未开通
```

## 🎯 下一步预告

完成账号准备后，下一步我们将：
1. 配置开发环境
2. 部署云函数
3. 初始化数据库
4. 进行功能测试

**预计完成时间**：1-3个工作日（取决于审核速度）

---

**💡 小贴士**：建议在工作日白天进行注册，这样如果有问题可以及时联系微信客服。注册完成后，一定要妥善保存AppID和云环境ID，这是后续开发的关键信息。 