/* pages/editProfile/editProfile.wxss */

/* 整体容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 50%, #F0F8FF 100%);
  position: relative;
  overflow: hidden;
}

/* 浮动背景装饰 */
.container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
  animation: float 20s ease-in-out infinite;
  z-index: 0;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30rpx, -30rpx) rotate(120deg); }
  66% { transform: translate(-20rpx, 20rpx) rotate(240deg); }
}

.main-content {
  padding: 40rpx 30rpx 180rpx;
  position: relative;
  z-index: 1;
}

/* 卡片通用样式 */
.profile-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 182, 193, 0.2);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 182, 193, 0.1), transparent);
  transition: left 0.5s ease;
  z-index: 0;
}

.profile-card:active::before {
  left: 100%;
}

.profile-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 182, 193, 0.3);
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 30rpx 0;
  position: relative;
  z-index: 1;
}

.card-title {
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.edit-hint {
  display: flex;
  align-items: center;
  color: #FF69B4;
}

.edit-text {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.arrow-icon {
  font-size: 28rpx;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(6rpx); }
}

/* 卡片内容 */
.card-content {
  padding: 20rpx 30rpx 30rpx;
  position: relative;
  z-index: 1;
}

/* 头像卡片特定样式 */
.avatar-card .card-content {
  text-align: center;
}

.avatar-display {
  position: relative;
  display: inline-block;
  margin-bottom: 20rpx;
}

.avatar-wrapper {
  position: relative;
  width: 140rpx;
  height: 140rpx;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto;
}

.avatar-image {
  width: 100% !important;
  height: 100% !important;
  border-radius: 50% !important;
  object-fit: cover !important;
  object-position: center !important;
  display: block !important;
  aspect-ratio: 1 / 1 !important;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-card:active .avatar-overlay {
  opacity: 1;
}

.camera-icon {
  font-size: 32rpx;
  color: white;
}

.avatar-ring {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  border: 3rpx solid #FF69B4;
  border-radius: 50%;
  opacity: 0.6;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.05); opacity: 0.8; }
}

.avatar-tips {
  margin-top: 10rpx;
}

/* 昵称卡片特定样式 */
.nickname-display {
  background: linear-gradient(135deg, #F8F9FA, #FFF5F8);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid rgba(255, 105, 180, 0.1);
  position: relative;
}

.nickname-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 105, 180, 0.05), rgba(138, 43, 226, 0.05));
  border-radius: 14rpx;
  z-index: 0;
}

.nickname-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  position: relative;
  z-index: 1;
}

/* 个性签名显示区域 */
.signature-display {
  background: linear-gradient(135deg, #F0F8FF, #FFF8F0);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid rgba(255, 165, 0, 0.1);
  position: relative;
}

.signature-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 165, 0, 0.05), rgba(255, 215, 0, 0.05));
  border-radius: 14rpx;
  z-index: 0;
}

.signature-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  position: relative;
  z-index: 1;
  font-style: italic;
}

/* 个性签名编辑区域 */
.signature-edit-simple {
  padding: 10rpx 0;
}

.signature-edit-input {
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  width: 100%;
  font-style: italic;
}

/* 提示样式 */
.avatar-tips,
.nickname-tips,
.signature-tips {
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 温馨提示卡片 */
.tips-card {
  background: linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(240, 248, 255, 0.9));
}

.tips-list {
  padding: 10rpx 0;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.bullet {
  color: #FF69B4;
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 2rpx;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(255, 182, 193, 0.2);
  z-index: 100;
}

.save-button {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #DDD, #CCC);
  color: #999;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.save-button.active {
  background: linear-gradient(135deg, #FF69B4, #8A2BE2);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.4);
}

.save-button.active::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
}

.save-button.active:active::before {
  width: 600rpx;
  height: 600rpx;
}

.save-button-text {
  position: relative;
  z-index: 1;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-container,
.input-modal-container {
  background: white;
  border-radius: 24rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(100rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.modal-header,
.input-modal-header {
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #F0F0F0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #F5F5F5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
}

.modal-content,
.input-modal-content {
  padding: 30rpx;
}

/* 选项卡片 */
.option-card,
.avatar-option {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-radius: 16rpx;
  background: #F8F9FA;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
  border: none;
  width: 100%;
  text-align: left;
}

.option-card:last-child,
.avatar-option:last-child {
  margin-bottom: 0;
}

.option-card:active,
.avatar-option:active {
  background: #FF69B4;
  transform: scale(0.98);
}

.option-card:active .option-title,
.option-card:active .option-desc,
.option-card:active .option-arrow,
.avatar-option:active .option-title,
.avatar-option:active .option-desc,
.avatar-option:active .option-arrow {
  color: white;
}

.option-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
}

.option-info {
  flex: 1;
}

.option-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #666;
}

.option-arrow {
  font-size: 32rpx;
  color: #999;
}

/* 输入框样式 */
.wechat-nickname-tip {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #E3F2FD, #F3E5F5);
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.wechat-nickname-tip .tip-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.wechat-nickname-tip .tip-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.input-wrapper {
  position: relative;
}

.nickname-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #E0E0E0;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  background: white;
  transition: border-color 0.3s ease;
}

.nickname-input:focus {
  border-color: #FF69B4;
  outline: none;
}

.input-counter {
  position: absolute;
  right: 24rpx;
  bottom: -40rpx;
  font-size: 20rpx;
  color: #999;
}

/* 弹窗按钮 */
.input-modal-actions {
  padding: 0 30rpx 30rpx;
  display: flex;
  gap: 20rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #F5F5F5;
  color: #666;
}

.cancel-btn:active {
  background: #E0E0E0;
}

.confirm-btn {
  background: linear-gradient(135deg, #FF69B4, #8A2BE2);
  color: white;
}

.confirm-btn:active {
  transform: scale(0.98);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-content {
  background: white;
  padding: 60rpx;
  border-radius: 24rpx;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #F0F0F0;
  border-top: 6rpx solid #FF69B4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .main-content {
    padding-left: 20rpx;
    padding-right: 20rpx;
  }
  
  .avatar-wrapper {
    width: 120rpx;
    height: 120rpx;
  }
  
  .avatar-ring {
    width: 140rpx;
    height: 140rpx;
  }
  
  .nickname-display {
    max-width: 450rpx;
  }
  
  .modal-container, .input-modal-container {
    max-width: 500rpx;
  }
} 

/* 头像选择按钮 */
.avatar-select-btn {
  background: linear-gradient(135deg, #FF69B4, #FF1493);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 6rpx 20rpx rgba(255, 105, 180, 0.3);
  transition: all 0.3s ease;
  margin-top: 20rpx;
  position: relative;
  overflow: hidden;
}

.avatar-select-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.avatar-select-btn:active::before {
  left: 100%;
}

.avatar-select-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.4);
}

.avatar-select-btn .btn-text {
  position: relative;
  z-index: 1;
}

.overlay-text {
  font-size: 20rpx;
  color: white;
  text-align: center;
  text-shadow: 1rpx 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

/* 昵称编辑 - 极简样式 */
.nickname-edit-simple {
  margin-top: 10rpx;
}

.edit-input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.edit-input-wrapper:focus-within {
  border-color: #ff69b4;
  background: #fff;
  box-shadow: 0 0 0 4rpx rgba(255, 105, 180, 0.1);
}

.nickname-edit-input {
  width: 100%;
  padding: 24rpx 80rpx 24rpx 24rpx;
  font-size: 32rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  line-height: 1.4;
}

.input-counter {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
  pointer-events: none;
}

.edit-tips-simple {
  margin-top: 16rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, #fff5f8 0%, #ffe8f0 100%);
  border-radius: 12rpx;
  border-left: 4rpx solid #ff69b4;
}

.edit-tips-simple .tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
} 