/**
 * 通过邀请码绑定情侣关系云函数
 * 用户输入邀请码建立情侣绑定关系
 * 支持重新绑定：如果之前有过绑定关系，则重新激活；否则创建新的绑定关系
 */

const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event 云函数调用参数
 * @param {string} event.inviteCode 邀请码
 * @param {Object} context 云函数运行上下文
 */
exports.main = async (event, context) => {
  console.log('开始执行邀请码绑定云函数:', event);
  
  try {
    const { inviteCode } = event;
    
    // 参数验证
    if (!inviteCode) {
      throw new Error('邀请码不能为空');
    }

    // 获取微信调用上下文
    const wxContext = cloud.getWXContext();
    const { OPENID } = wxContext;
    
    if (!OPENID) {
      throw new Error('无法获取用户 OpenID');
    }

    console.log('用户绑定请求:', { OPENID, inviteCode: inviteCode.toUpperCase() });

    // 查询当前用户信息
    const currentUserQuery = await db.collection('users').where({
      _openid: OPENID
    }).get();

    if (currentUserQuery.data.length === 0) {
      throw new Error('用户不存在，请先登录');
    }

    const currentUser = currentUserQuery.data[0];
    console.log('当前用户信息:', { 
      _id: currentUser._id, 
      _openid: currentUser._openid,
      nickName: currentUser.nickName,
      partnerId: currentUser.partnerId 
    });

    // 检查当前用户是否已经绑定情侣
    if (currentUser.partnerId) {
      // 检查当前绑定是否有效
      const existingCoupleResult = await db.collection('couples').doc(currentUser.partnerId).get();
      if (existingCoupleResult.data && existingCoupleResult.data.status === 'active') {
        throw new Error('您已经绑定了情侣，无法重复绑定');
      }
    }

    // 查询邀请码对应的用户
    const inviterQuery = await db.collection('users').where({
      inviteCode: inviteCode.toUpperCase(),
      inviteCodeExpiry: db.command.gt(new Date())
    }).get();

    if (inviterQuery.data.length === 0) {
      console.log('未找到有效邀请码:', inviteCode.toUpperCase());
      throw new Error('邀请码无效或已过期，请重新获取');
    }

    const inviter = inviterQuery.data[0];
    console.log('邀请方用户信息:', { 
      _id: inviter._id, 
      _openid: inviter._openid,
      nickName: inviter.nickName,
      partnerId: inviter.partnerId,
      inviteCode: inviter.inviteCode
    });

    // 检查是否自己绑定自己
    if (inviter._openid === OPENID) {
      throw new Error('不能使用自己的邀请码');
    }

    // 检查邀请方是否已经绑定
    if (inviter.partnerId) {
      // 检查邀请方的绑定是否有效
      const inviterCoupleResult = await db.collection('couples').doc(inviter.partnerId).get();
      if (inviterCoupleResult.data && inviterCoupleResult.data.status === 'active') {
        throw new Error('该邀请码的主人已经绑定了其他人');
      }
    }

    // 检查是否存在历史绑定关系（两人之间）
    console.log('检查历史绑定关系...');
    const historyCoupleQuery = await db.collection('couples').where({
      $or: [
        {
          $and: [
            { user1Id: inviter._openid },
            { user2Id: currentUser._openid }
          ]
        },
        {
          $and: [
            { user1Id: currentUser._openid },
            { user2Id: inviter._openid }
          ]
        }
      ]
    }).orderBy('createTime', 'desc').get();

    const currentTime = new Date();
    let coupleId = null;
    let isReactivation = false;

    // 使用数据库事务确保数据一致性
    console.log('开始执行数据库事务...');
    
    const transaction = await db.startTransaction();
    
    try {
      if (historyCoupleQuery.data.length > 0) {
        // 存在历史绑定关系，重新激活最新的一个
        const historyCouple = historyCoupleQuery.data[0];
        coupleId = historyCouple._id;
        isReactivation = true;
        
        console.log('找到历史绑定关系，重新激活:', coupleId);
        
        // 重新激活情侣关系
        await transaction.collection('couples').doc(coupleId).update({
          data: {
            status: 'active',
            bindTime: currentTime, // 更新绑定时间
            updateTime: currentTime,
            reactivationTime: currentTime,
            reactivationCount: db.command.inc(1), // 重新激活次数+1
            // 更新用户信息
            user1Info: historyCouple.user1Id === inviter._openid ? {
              _id: inviter._id,
              nickName: inviter.nickName,
              avatarUrl: inviter.avatarUrl
            } : {
              _id: currentUser._id,
              nickName: currentUser.nickName,
              avatarUrl: currentUser.avatarUrl
            },
            user2Info: historyCouple.user2Id === currentUser._openid ? {
              _id: currentUser._id,
              nickName: currentUser.nickName,
              avatarUrl: currentUser.avatarUrl
            } : {
              _id: inviter._id,
              nickName: inviter.nickName,
              avatarUrl: inviter.avatarUrl
            }
          }
        });
        
        console.log('历史关系重新激活成功');
        
      } else {
        // 不存在历史绑定关系，创建新的绑定关系
        console.log('创建新的绑定关系...');
        
        const coupleData = {
          user1Id: inviter._openid,
          user1Info: {
            _id: inviter._id,
            nickName: inviter.nickName,
            avatarUrl: inviter.avatarUrl
          },
          user2Id: currentUser._openid,
          user2Info: {
            _id: currentUser._id,
            nickName: currentUser.nickName,
            avatarUrl: currentUser.avatarUrl
          },
          status: 'active',
          bindTime: currentTime,
          createTime: currentTime,
          updateTime: currentTime,
          reactivationCount: 0,
          stats: {
            totalCoupons: 0,
            redeemedCoupons: 0,
            activeCoupons: 0
          },
          settings: {
            allowNotifications: true,
            shareAnniversary: true
          }
        };

        const coupleResult = await transaction.collection('couples').add({
          data: coupleData
        });

        coupleId = coupleResult._id;
        console.log('新couples记录创建成功，ID:', coupleId);
      }

      // 更新邀请方用户信息
      console.log('更新邀请方用户信息...', inviter._id);
      await transaction.collection('users').doc(inviter._id).update({
        data: {
          partnerId: coupleId,
          inviteCode: null, // 清除邀请码
          inviteCodeExpiry: null,
          relationshipStatus: 'coupled',
          updateTime: currentTime
        }
      });
      console.log('邀请方用户信息更新成功');

      // 更新当前用户信息
      console.log('更新当前用户信息...', currentUser._id);
      await transaction.collection('users').doc(currentUser._id).update({
        data: {
          partnerId: coupleId,
          relationshipStatus: 'coupled',
          updateTime: currentTime
        }
      });
      console.log('当前用户信息更新成功');

      // 如果是重新激活，需要将之前失效的券重新激活
      if (isReactivation) {
        console.log('重新激活相关券...');
        const reactivatedCoupons = await transaction.collection('coupons').where({
          $or: [
            { 
              $and: [
                { creatorId: inviter._openid },
                { recipientId: currentUser._openid }
              ]
            },
            { 
              $and: [
                { creatorId: currentUser._openid },
                { recipientId: inviter._openid }
              ]
            }
          ],
          status: 'invalid',
          invalidReason: 'couple_unbound'
        }).update({
          data: {
            status: 'active',
            invalidReason: null,
            invalidTime: null,
            reactivationTime: currentTime,
            updateTime: currentTime
          }
        });
        
        console.log('重新激活券数量:', reactivatedCoupons.stats.updated);
      }

      // 提交事务
      await transaction.commit();
      console.log('数据库事务提交成功');

      // 验证更新结果
      const updatedInviter = await db.collection('users').doc(inviter._id).get();
      const updatedCurrentUser = await db.collection('users').doc(currentUser._id).get();
      
      console.log('更新后的邀请方用户:', {
        _id: updatedInviter.data._id,
        partnerId: updatedInviter.data.partnerId
      });
      console.log('更新后的当前用户:', {
        _id: updatedCurrentUser.data._id,
        partnerId: updatedCurrentUser.data.partnerId
      });

      // 获取完整的情侣信息用于返回
      const finalCoupleResult = await db.collection('couples').doc(coupleId).get();
      const finalCoupleInfo = finalCoupleResult.data;

      // 准备返回的情侣信息
      const coupleInfo = {
        _id: coupleId,
        ...finalCoupleInfo,
        partnerId: inviter._openid,
        partnerNickname: inviter.nickName,
        partnerAvatar: inviter.avatarUrl,
        partnerInfo: {
          _id: inviter._id,
          _openid: inviter._openid,
          nickName: inviter.nickName,
          avatarUrl: inviter.avatarUrl
        }
      };

      console.log('情侣绑定完全成功:', {
        coupleId,
        isReactivation,
        user1: { _id: inviter._id, _openid: inviter._openid },
        user2: { _id: currentUser._id, _openid: currentUser._openid }
      });

      return {
        success: true,
        message: '绑定成功',
        data: {
          coupleInfo: coupleInfo,
          inviterInfo: {
            _id: inviter._id,
            nickName: inviter.nickName,
            avatarUrl: inviter.avatarUrl
          },
          bindTime: currentTime.toISOString(),
          timestamp: currentTime.toISOString()
        }
      };

    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback();
      console.error('数据库事务失败，已回滚:', transactionError);
      throw new Error(`绑定过程中发生错误: ${transactionError.message}`);
    }

  } catch (error) {
    console.error('邀请码绑定云函数执行失败:', error);
    
    return {
      success: false,
      message: '绑定失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}; 