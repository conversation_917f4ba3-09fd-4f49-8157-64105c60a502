/**
 * 情侣信誉券小程序部署配置
 * 用于配置不同环境的部署参数
 */

module.exports = {
  // 开发环境配置
  development: {
    env: 'dev-xxx', // 云开发环境ID（开发环境）
    appid: 'your-dev-appid', // 小程序AppID（开发）
    version: '1.0.0-dev',
    description: '开发版本',
    cloudfunctions: [
      'loginOrCreateUser',
      'generateInviteCode',
      'bindWithInviteCode',
      'createOrUpdateCoupon',
      'getCoupons',
      'updateCouponStatus',
      'sendNotification',
      'autoUpdateExpired'
    ],
    // 数据库配置
    database: {
      collections: [
        'users',
        'couples', 
        'coupons',
        'notifications',
        'message_logs'
      ]
    },
    // 存储配置
    storage: {
      buckets: ['user-avatars', 'coupon-images']
    }
  },

  // 测试环境配置
  test: {
    env: 'test-xxx', // 云开发环境ID（测试环境）
    appid: 'your-test-appid', // 小程序AppID（测试）
    version: '1.0.0-test',
    description: '测试版本',
    cloudfunctions: [
      'loginOrCreateUser',
      'generateInviteCode',
      'bindWithInviteCode',
      'createOrUpdateCoupon',
      'getCoupons',
      'updateCouponStatus',
      'sendNotification',
      'autoUpdateExpired'
    ],
    database: {
      collections: [
        'users',
        'couples',
        'coupons', 
        'notifications',
        'message_logs'
      ]
    },
    storage: {
      buckets: ['user-avatars', 'coupon-images']
    }
  },

  // 生产环境配置
  production: {
    env: 'prod-xxx', // 云开发环境ID（生产环境）
    appid: 'your-prod-appid', // 小程序AppID（生产）
    version: '1.0.0',
    description: '正式版本',
    cloudfunctions: [
      'loginOrCreateUser',
      'generateInviteCode',
      'bindWithInviteCode',
      'createOrUpdateCoupon',
      'getCoupons',
      'updateCouponStatus',
      'sendNotification',
      'autoUpdateExpired'
    ],
    database: {
      collections: [
        'users',
        'couples',
        'coupons',
        'notifications',
        'message_logs'
      ]
    },
    storage: {
      buckets: ['user-avatars', 'coupon-images']
    },
    // 生产环境特殊配置
    performance: {
      enableCache: true,
      cacheTimeout: 300000, // 5分钟
      enableCompression: true
    },
    security: {
      enableRateLimit: true,
      maxRequestsPerMinute: 100
    }
  },

  // 部署脚本配置
  deploy: {
    // 云函数部署配置
    cloudfunctions: {
      timeout: 60000, // 部署超时时间
      memory: 256, // 内存配置
      runtime: 'Nodejs12.16' // 运行时环境
    },
    
    // 数据库初始化配置
    database: {
      initData: {
        // 券模板数据
        couponTemplates: [
          {
            title: '陪我看电影',
            description: '陪我看一场我想看的电影',
            icon: '🎬',
            bgColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            category: 'entertainment',
            tags: ['电影', '陪伴']
          },
          {
            title: '做我最爱的菜',
            description: '亲手为我做一道我最爱吃的菜',
            icon: '🍳',
            bgColor: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
            category: 'food',
            tags: ['美食', '用心']
          },
          {
            title: '陪我散步',
            description: '陪我在公园里悠闲地散步一小时',
            icon: '🚶‍♀️',
            bgColor: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
            category: 'activity',
            tags: ['运动', '陪伴']
          }
        ]
      }
    }
  },

  // 监控配置
  monitoring: {
    // 错误监控
    errorTracking: {
      enable: true,
      errorThreshold: 10 // 错误率阈值
    },
    
    // 性能监控
    performanceTracking: {
      enable: true,
      responseTimeThreshold: 3000 // 响应时间阈值（毫秒）
    },
    
    // 用户行为监控
    behaviorTracking: {
      enable: true,
      events: ['login', 'create_coupon', 'redeem_coupon']
    }
  },

  // 备份配置
  backup: {
    // 数据库备份
    database: {
      enable: true,
      schedule: '0 2 * * *', // 每天凌晨2点备份
      retention: 30 // 保留30天
    },
    
    // 存储文件备份
    storage: {
      enable: true,
      schedule: '0 3 * * 0', // 每周日凌晨3点备份
      retention: 12 // 保留12周
    }
  }
}; 