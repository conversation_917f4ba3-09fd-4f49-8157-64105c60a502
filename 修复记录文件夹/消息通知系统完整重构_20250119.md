# 消息通知系统完整重构总结

## 修复时间
2025年1月19日

## 修复背景
用户要求对消息通知系统进行完整的逻辑重构，实现：
1. 在兑现申请和兑现完成下增加二级tab页签
2. 完整的消息状态流转：发券 → 申请兑现 → 兑现完成 → 过期
3. 根据用户角色（申请方/接收方）显示不同的消息和文案
4. 消息在不同状态下自动在不同tab间迁移

## 重构内容

### 1. 设计二级tab页签结构
**新增功能：**
- 兑现申请：我申请的 / 我接收的
- 兑现完成：我申请的 / 我兑现的
- 支持一级tab和二级tab的嵌套导航

**技术实现：**
```javascript
filterTabs: [
  { type: "all", label: "全部", count: 0 },
  { type: "new_coupon", label: "新券提醒", count: 0 },
  { 
    type: "redeem_request", 
    label: "兑现申请", 
    hasSubTabs: true,
    subTabs: [
      { type: "redeem_request_my", label: "我申请的", count: 0 },
      { type: "redeem_request_received", label: "我接收的", count: 0 }
    ]
  },
  { 
    type: "redeem_result", 
    label: "兑现完成", 
    hasSubTabs: true,
    subTabs: [
      { type: "redeem_result_my", label: "我申请的", count: 0 },
      { type: "redeem_result_processed", label: "我兑现的", count: 0 }
    ]
  },
  { type: "expiry", label: "过期提醒", count: 0 }
]
```

### 2. 更新通知类型定义
**新增字段：**
- `notificationStatus`: 'active' | 'hidden' | 'moved' - 控制通知显示状态
- `couponStatus`: 券的当前状态
- `userRole`: 'recipient' | 'creator' | 'applicant' | 'approver' | 'beneficiary' | 'processor'
- `subType`: 子类型，用于更细粒度的分类

**角色定义：**
- `recipient`: 券的接收方
- `creator`: 券的创建方
- `applicant`: 申请兑现的一方
- `approver`: 审批兑现的一方
- `beneficiary`: 兑现受益人（申请成功）
- `processor`: 兑现处理人（审批通过）

### 3. 实现消息状态流转逻辑
**完整流程：**

#### 阶段1：发券
- 创建方发券给接收方
- 接收方在"新券提醒"看到消息

#### 阶段2：申请兑现
- 接收方点击申请兑现
- 接收方的"新券提醒"消息隐藏
- 接收方在"兑现申请-我申请的"看到"等待对方兑现"
- 创建方在"兑现申请-我接收的"看到"对方申请兑现"

#### 阶段3：兑现完成
- 创建方同意兑现
- 双方的"兑现申请"消息隐藏
- 创建方在"兑现完成-我兑现的"看到"您已兑现爱人申请的券"
- 接收方在"兑现完成-我申请的"看到"已成功兑现这张券"

#### 阶段4：券过期
- 所有相关消息隐藏
- 在"过期提醒"显示过期消息

### 4. 创建通知状态管理云函数
**新增云函数：`updateNotificationStatus`**

**核心功能：**
- `handleApplyRedeem`: 处理申请兑现的状态变化
- `handleApproveRedeem`: 处理同意兑现的状态变化
- `handleRejectRedeem`: 处理拒绝兑现的状态变化
- `handleExpireCoupon`: 处理券过期的状态变化

**关键逻辑：**
```javascript
// 申请兑现时
await handleApplyRedeem(couponId, applicantId) {
  // 1. 隐藏申请人的新券提醒
  // 2. 为申请人创建"等待兑现"通知
  // 3. 为券创建者创建"收到申请"通知
}

// 同意兑现时
await handleApproveRedeem(couponId, approverId) {
  // 1. 隐藏双方的兑现申请通知
  // 2. 为审批人创建"已兑现"通知
  // 3. 为申请人创建"兑现成功"通知
}
```

### 5. 实现新的筛选逻辑
**支持功能：**
- 一级tab筛选
- 二级tab筛选
- 根据用户角色筛选
- 只显示活跃状态的通知

**筛选逻辑：**
```javascript
applyFilter() {
  if (currentFilter === "redeem_request" && currentSubFilter) {
    if (currentSubFilter === "redeem_request_my") {
      // 我申请的：显示我作为申请人的通知
      filteredNotifications = notifications.filter(
        (item) => item.type === "redeem_request" && 
                  item.userRole === 'applicant' &&
                  item.notificationStatus === 'active'
      );
    }
    // 其他筛选逻辑...
  }
}
```

### 6. 更新消息文案系统
**智能文案生成：**
- 根据通知类型、用户角色、券状态生成对应文案
- 支持自定义标题和内容
- 优先使用云函数中的自定义文案

**文案示例：**
```javascript
// 兑现申请 - 申请人视角
title: "等待对方兑现"
content: "您已申请兑现券：家务代劳券，等待对方处理"

// 兑现申请 - 审批人视角  
title: "收到兑现申请"
content: "对方申请兑现券：家务代劳券"

// 兑现完成 - 受益人视角
title: "已成功兑现这张券"
content: "您的券：家务代劳券 已成功兑现"

// 兑现完成 - 处理人视角
title: "您已兑现爱人申请的券"
content: "您已成功兑现券：家务代劳券"
```

### 7. 实现消息状态迁移
**自动迁移机制：**
- 券状态变化时自动调用`updateNotificationStatus`
- 隐藏不再需要的通知
- 创建新状态的通知
- 确保消息在正确的tab显示

**集成点：**
- 券详情页面的操作按钮
- 过期处理云函数
- 券状态更新云函数

### 8. 优化UI布局设计
**二级tab样式：**
- 毛玻璃效果背景
- 渐变激活状态
- 响应式布局适配
- 小屏幕优化

**视觉层次：**
```css
/* 一级tab */
.filter-tabs-container {
  /* 主要导航样式 */
}

/* 二级tab */
.sub-tabs-container {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  margin-top: 10rpx;
  border-radius: 20rpx;
}

.sub-tab.active {
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 20, 147, 0.3);
}
```

### 9. 添加过期处理逻辑
**过期时自动：**
- 调用通知状态管理系统
- 隐藏所有相关的活跃通知
- 创建过期提醒通知
- 确保只在过期提醒tab显示

### 10. 完整计数系统
**智能计数：**
- 只统计活跃状态的通知
- 支持二级tab的独立计数
- 主tab显示子tab的总计数

```javascript
updateFilterCounts() {
  const activeNotifications = notifications.filter(
    (item) => item.notificationStatus === 'active'
  );
  
  if (tab.type === "redeem_request") {
    const myRequests = activeNotifications.filter(
      (item) => item.type === "redeem_request" && item.userRole === 'applicant'
    ).length;
    const receivedRequests = activeNotifications.filter(
      (item) => item.type === "redeem_request" && item.userRole === 'approver'
    ).length;
    
    tab.subTabs[0].count = myRequests;
    tab.subTabs[1].count = receivedRequests;
    tab.count = myRequests + receivedRequests;
  }
}
```

## 技术架构亮点

### 1. 状态机设计
- 明确的状态转换规则
- 自动化的状态迁移
- 防止状态不一致

### 2. 角色驱动的UI
- 根据用户角色显示不同内容
- 智能文案生成
- 个性化用户体验

### 3. 云函数协同
- 券状态管理 + 通知状态管理
- 统一的状态更新入口
- 数据一致性保证

### 4. 灵活的筛选系统
- 支持多级筛选
- 状态感知筛选
- 高性能计数统计

## 数据流图

```
发券 → [新券提醒]
  ↓ 申请兑现
隐藏新券提醒 → 创建兑现申请消息
  ↓ 同意兑现  
隐藏兑现申请 → 创建兑现完成消息
  ↓ 券过期
隐藏所有消息 → 创建过期提醒
```

## 用户角色矩阵

| 操作阶段 | 申请方看到 | 审批方看到 | 显示位置 |
|---------|-----------|-----------|----------|
| 收到新券 | 新券提醒 | - | 新券提醒 |
| 申请兑现 | 等待对方兑现 | 收到兑现申请 | 兑现申请(我申请的/我接收的) |
| 兑现完成 | 已成功兑现 | 您已兑现爱人申请的券 | 兑现完成(我申请的/我兑现的) |
| 券过期 | 券已过期 | 券已过期 | 过期提醒 |

## 修复验证
1. ✅ 二级tab页签正确显示和切换
2. ✅ 消息状态流转逻辑完整
3. ✅ 用户角色区分准确
4. ✅ 消息文案根据角色正确显示
5. ✅ 消息在不同阶段正确迁移tab
6. ✅ 计数系统准确统计
7. ✅ UI布局美观响应式
8. ✅ 过期处理逻辑正确

## 后续建议
1. 可以添加消息状态变化的动画效果
2. 可以考虑添加消息已读状态的批量管理
3. 建议定期清理过期的隐藏消息
4. 可以添加消息搜索功能

## 影响范围
- 消息通知系统完全重构，用户体验显著提升
- 消息逻辑更加清晰，符合用户心理模型
- 二级导航提升了信息组织效率
- 智能文案系统提供个性化体验
- 状态管理系统确保数据一致性 