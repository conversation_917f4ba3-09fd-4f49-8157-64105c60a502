{"component": true, "componentGenerics": {}, "usingComponents": {}, "componentPlaceholder": {}, "styleIsolation": "isolated", "description": "空状态提示组件，用于列表无数据、搜索无结果等场景，支持自定义提示图标、文字和操作按钮", "version": "1.0.0", "author": "情侣信任券小程序团队", "category": "Feedback Components", "keywords": ["空状态", "无数据", "提示", "引导", "占位"], "repository": {"type": "git", "url": "https://github.com/coupleapp/miniprogram-components"}, "license": "MIT", "properties": {"visible": {"type": "Boolean", "description": "是否显示空状态", "default": true, "required": false}, "icon": {"type": "String", "description": "图标 (emoji 或字符)", "default": "", "required": false}, "iconImage": {"type": "String", "description": "图标图片URL", "default": "", "required": false}, "title": {"type": "String", "description": "主标题", "default": "暂无内容", "required": false}, "description": {"type": "String", "description": "描述文本", "default": "", "required": false}, "actionText": {"type": "String", "description": "主要操作按钮文本", "default": "", "required": false}, "secondaryActionText": {"type": "String", "description": "次要操作按钮文本", "default": "", "required": false}, "actionType": {"type": "String", "description": "操作按钮类型", "default": "primary", "enum": ["primary", "success", "warning", "error", "info"], "required": false}, "actionLoading": {"type": "Boolean", "description": "主要操作按钮加载状态", "default": false, "required": false}, "actionDisabled": {"type": "Boolean", "description": "主要操作按钮禁用状态", "default": false, "required": false}, "secondaryActionLoading": {"type": "Boolean", "description": "次要操作按钮加载状态", "default": false, "required": false}, "secondaryActionDisabled": {"type": "Boolean", "description": "次要操作按钮禁用状态", "default": false, "required": false}, "size": {"type": "String", "description": "尺寸大小", "default": "normal", "enum": ["small", "normal", "large", "compact", "fullscreen"], "required": false}, "showActions": {"type": "Boolean", "description": "是否显示操作区域", "default": true, "required": false}, "showContentSlot": {"type": "Boolean", "description": "是否显示内容插槽", "default": false, "required": false}, "showActionsSlot": {"type": "Boolean", "description": "是否显示操作插槽", "default": false, "required": false}, "emptyId": {"type": "String", "description": "组件ID，用于识别", "default": "", "required": false}, "extraData": {"type": "Object", "description": "额外数据", "default": {}, "required": false}}, "events": {"action": {"description": "主要操作按钮点击事件", "parameters": {"type": "String - 操作类型 (primary)", "actionType": "String - 按钮主题类型", "emptyId": "String - 组件ID", "extraData": "Object - 额外数据", "timeStamp": "Number - 时间戳"}}, "secondaryAction": {"description": "次要操作按钮点击事件", "parameters": {"type": "String - 操作类型 (secondary)", "emptyId": "String - 组件ID", "extraData": "Object - 额外数据", "timeStamp": "Number - 时间戳"}}, "iconError": {"description": "图标加载失败事件", "parameters": {"emptyId": "String - 组件ID", "iconImage": "String - 图标URL", "error": "Object - 错误信息"}}, "feedback": {"description": "用户交互反馈事件", "parameters": {"type": "String - 反馈类型", "emptyId": "String - 组件ID", "action": "String - 操作动作"}}, "show": {"description": "显示空状态时触发", "parameters": {"emptyId": "String - 组件ID", "timeStamp": "Number - 时间戳"}}, "hide": {"description": "隐藏空状态时触发", "parameters": {"emptyId": "String - 组件ID", "timeStamp": "Number - 时间戳"}}}, "methods": {"show": {"description": "显示空状态", "parameters": {}, "returns": "void"}, "hide": {"description": "隐藏空状态", "parameters": {}, "returns": "void"}, "toggle": {"description": "切换显示状态", "parameters": {}, "returns": "void"}, "updateContent": {"description": "更新内容", "parameters": {"options": "Object - 要更新的内容字段"}, "returns": "void"}, "setLoading": {"description": "设置加载状态", "parameters": {"primary": "Boolean - 主按钮加载状态", "secondary": "Boolean - 次按钮加载状态"}, "returns": "void"}, "setDisabled": {"description": "设置禁用状态", "parameters": {"primary": "Boolean - 主按钮禁用状态", "secondary": "Boolean - 次按钮禁用状态"}, "returns": "void"}, "applyConfig": {"description": "应用预设配置", "parameters": {"configKey": "String - 配置键名 (noData, noNetwork, noResult, error, coming)"}, "returns": "void"}, "getState": {"description": "获取组件状态", "parameters": {}, "returns": "Object - 当前状态信息"}}, "slots": {"content": {"description": "自定义内容插槽，显示在描述文本下方"}, "actions": {"description": "自定义操作按钮插槽，替代默认的操作按钮"}}, "examples": [{"title": "基础使用", "description": "显示基本的空状态提示", "code": {"wxml": "<empty-state icon=\"💕\" title=\"暂无信任券\" description=\"快去创建第一张信任券吧\" action-text=\"立即创建\" bind:action=\"onCreate\" />", "js": "Page({ onCreate(e) { console.log('创建按钮被点击:', e.detail); wx.navigateTo({ url: '/pages/create/index' }); } })"}}, {"title": "网络错误状态", "description": "显示网络连接失败的空状态", "code": {"wxml": "<empty-state icon=\"📶\" title=\"网络连接失败\" description=\"请检查网络连接后重试\" action-text=\"重新连接\" action-type=\"warning\" bind:action=\"onRetry\" />"}}, {"title": "搜索无结果", "description": "显示搜索无结果的空状态", "code": {"wxml": "<empty-state icon=\"🔍\" title=\"没有找到结果\" description=\"试试其他关键词或条件\" action-text=\"重新搜索\" secondary-action-text=\"清空筛选\" bind:action=\"onSearch\" bind:secondaryAction=\"onClear\" />"}}, {"title": "使用图片图标", "description": "使用自定义图片作为空状态图标", "code": {"wxml": "<empty-state icon-image=\"https://example.com/empty.png\" title=\"空状态\" description=\"这里是描述\" action-text=\"操作\" />"}}, {"title": "自定义内容", "description": "使用插槽自定义空状态内容", "code": {"wxml": "<empty-state title=\"自定义空状态\" show-content-slot=\"{{true}}\"><view slot=\"content\"><text>这是自定义内容区域</text><image src=\"custom.png\" /></view></empty-state>"}}, {"title": "不同尺寸", "description": "展示不同尺寸的空状态", "code": {"wxml": "<empty-state size=\"small\" title=\"小尺寸\" />\n<empty-state size=\"normal\" title=\"正常尺寸\" />\n<empty-state size=\"large\" title=\"大尺寸\" />"}}, {"title": "预设配置", "description": "使用预设配置快速设置空状态", "code": {"js": "Page({ onLoad() { this.selectComponent('#emptyState').applyConfig('noData'); } })"}}], "changelog": [{"version": "1.0.0", "date": "2024-01-01", "changes": ["初始版本发布", "支持图标和图片展示", "支持标题和描述文本", "支持主要和次要操作按钮", "支持多种按钮主题类型", "支持按钮加载和禁用状态", "支持多种尺寸选择", "支持自定义内容插槽", "支持预设配置应用", "支持图标加载失败处理", "支持响应式设计", "支持深色模式", "支持无障碍功能"]}]}