/* pages/couponDetail/couponDetail.wxss */
@import "../../app.wxss";

.container {
  padding-bottom: 50rpx;
}

.loading-full-page, .empty-state-full {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 70vh;
  text-align: center;
  padding: 40rpx;
}
.empty-state-full {
  gap: 20rpx;
}
.empty-icon {
  width: 150rpx;
  height: 150rpx;
  opacity: 0.6;
  margin-bottom: 20rpx;
}
.message-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #444;
}
.message-body {
  font-size: 26rpx;
  color: #777;
  margin-bottom: 30rpx;
}

/* Coupon Visual Display */
.coupon-visual-card {
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.15);
  position: relative;
  overflow: hidden;
}
.coupon-visual-card::before { /* Subtle pattern or texture */
  content: '';
  position: absolute;
  top: 0; right: 0; bottom: 0; left: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
}
.coupon-icon-large {
  font-size: 80rpx;
  margin-bottom: 25rpx;
  animation: floatIcon 3s ease-in-out infinite;
}
@keyframes floatIcon {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}
.coupon-title-large {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  line-height: 1.3;
}
.coupon-description-detail {
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 30rpx;
  opacity: 0.9;
  max-width: 95%;
}
.coupon-meta-row {
  display: flex;
  justify-content: space-around;
  width: 100%;
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 15rpx;
}
.meta-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.meta-label {
  font-size: 22rpx;
  margin-bottom: 5rpx;
}
.meta-value {
  font-weight: 500;
}
.coupon-expiry-detail {
  font-size: 22rpx;
  opacity: 0.7;
  margin-top: 10rpx;
  padding: 8rpx 15rpx;
  background-color: rgba(0,0,0,0.1);
  border-radius: 10rpx;
}


/* Info Section */
.info-section {
  padding: 30rpx;
}
.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx dashed #eee;
}
.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.status-tag {
  padding: 8rpx 18rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: white;
}
.status-sent, .status-draft { background-color: #FFB74D; } /* Orangeish */
.status-received, .status-pending_redeem  { background-color: #4FC3F7; } /* Blueish */
.status-redeemed { background-color: #81C784; } /* Greenish */
.status-expired, .status-cancelled  { background-color: #BDBDBD; } /* Greyish */

.info-item {
  display: flex;
  gap: 15rpx;
  font-size: 26rpx;
  margin-bottom: 15rpx;
  line-height: 1.5;
}
.info-label {
  color: #666;
  flex-shrink: 0;
  width: 150rpx; /* Align values */
}
.info-value {
  color: #333;
  font-weight: 500;
}
.info-value.notes {
  background-color: #f9f9f9;
  padding: 10rpx 15rpx;
  border-radius: 8rpx;
  border: 1rpx solid #eee;
  white-space: pre-wrap;
}

/* Actions Section */
.actions-section {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

/* 操作区域样式 */
.action-area {
  padding: 20rpx 0;
}

.action-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
}

.action-desc {
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  border-left: 4rpx solid #ff6b9d;
}

.action-desc text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}
.action-button {
  width: 100%;
  padding: 24rpx;
}
.no-actions-text {
  text-align: center;
  color: #888;
  font-size: 26rpx;
  padding: 20rpx 0;
}
.danger-action {
  background: #FFE0E0;
  color: #e53e3e;
  border: 1rpx solid #FFC0C0;
}
.danger-action:active {
  background: #FFD0D0;
}
.login-prompt-card {
  padding: 40rpx;
  text-align: center;
  font-size: 28rpx;
  color: #555;
}
.full-width {
  width: 100%;
}
/* Ensure /images/coupon-not-found.svg exists */

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content {
  background-color: #fff;
  padding: 40rpx;
  border-radius: 20rpx;
  width: 85vw;
  max-width: 600rpx;
  box-shadow: 0 5rpx 15rpx rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  color: #333;
}
.modal-subtitle {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  margin-bottom: 10rpx;
}
.modal-textarea {
  width: 100%;
  height: 120rpx;
  padding: 15rpx;
  border: 1rpx solid #ddd;
  border-radius: 12rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}
.modal-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}
.modal-button {
  flex: 1;
  padding: 18rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
}
.modal-button.primary {
  background: #FF69B4;
  color: white;
}
.modal-button.secondary {
  background: #f0f0f0;
  color: #555;
}
