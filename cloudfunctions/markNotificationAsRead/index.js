const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 标记单个通知为已读
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    const { notificationId } = event
    
    // 参数验证
    if (!notificationId) {
      return {
        success: false,
        error: 'MISSING_PARAMS',
        message: '缺少必要参数：notificationId'
      }
    }
    
    // 更新通知状态为已读 - 修复：使用recipientId字段
    const updateResult = await db.collection('notifications')
      .where({
        _id: notificationId,
        recipientId: openid
      })
      .update({
        data: {
          isRead: true,
          readTime: new Date()
        }
      })
    
    if (updateResult.stats.updated === 0) {
      return {
        success: false,
        error: 'NOTIFICATION_NOT_FOUND',
        message: '通知不存在或无权限操作'
      }
    }
    
    return {
      success: true,
      message: '通知已标记为已读'
    }
    
  } catch (error) {
    console.error('标记通知已读失败:', error)
    return {
      success: false,
      error: 'SYSTEM_ERROR',
      message: '系统错误，请稍后重试'
    }
  }
} 