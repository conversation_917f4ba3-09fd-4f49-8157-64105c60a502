/**
 * 情侣信誉券卡片组件样式
 * 设计理念：突出"券"的概念，营造浪漫情侣氛围
 * 技术特色：aspect-ratio、backdrop-filter、CSS动画、渐变背景
 */

/* ==================== 卡片主体样式 ==================== */
.coupon-card {
  position: relative;
  width: 100%;
  /* 真实卡片比例 1.59:1 */
  aspect-ratio: 1.59 / 1;
  border-radius: 24rpx;
  overflow: hidden;
  cursor: pointer;
  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 32rpx;
  /* 默认经典渐变风格 */
  background: linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%);
}

/* 模版卡片特殊样式 - 适配固定高度397rpx */
.template-coupon-card.coupon-card {
  aspect-ratio: unset;
  height: 100%;
  margin-bottom: 0;
  border-radius: 20rpx;
}

.coupon-card:hover {
  transform: translateY(-8rpx) scale(1.02);
}

.card-hover {
  transform: scale(0.98);
  opacity: 0.95;
}

/* 券卡片主体容器 */
.coupon-card-body {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 32rpx 40rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: white;
  z-index: 2;
}

/* 券卡片阴影 */
.coupon-shadow {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  right: 8rpx;
  bottom: 8rpx;
  background: rgba(255, 109, 150, 0.3);
  border-radius: 24rpx;
  filter: blur(16rpx);
  z-index: -1;
  transition: all 400ms ease;
}

.coupon-card:hover .coupon-shadow {
  top: 16rpx;
  left: 16rpx;
  right: 16rpx;
  bottom: 16rpx;
  filter: blur(24rpx);
}

/* ==================== 券顶部区域 ==================== */
.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

/* 券类型标签 */
.coupon-type-tag {
  padding: 8rpx 16rpx;
  font-size: 20rpx;
  font-weight: 600;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(8rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  text-transform: uppercase;
  letter-spacing: 1rpx;
}

/* 券编号 */
.coupon-number {
  display: flex;
  align-items: baseline;
  font-family: 'Georgia', serif;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.number-prefix {
  font-size: 24rpx;
  font-weight: 400;
  opacity: 0.9;
  margin-right: 4rpx;
}

.number-value {
  font-size: 28rpx;
  font-weight: 700;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
}

/* ==================== 券中央内容区域 ==================== */
.coupon-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin: 16rpx 0;
}

/* 券主题图标 */
.coupon-main-icon {
  font-size: 64rpx;
  margin-bottom: 16rpx;
  filter: drop-shadow(0 4rpx 12rpx rgba(0, 0, 0, 0.2));
  animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { 
    transform: translateY(0) rotate(0deg); 
  }
  50% { 
    transform: translateY(-4rpx) rotate(2deg); 
  }
}

/* 券标题 */
.coupon-title {
  font-size: 36rpx;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 1rpx;
  max-height: 80rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 券描述 */
.coupon-description {
  font-size: 24rpx;
  line-height: 1.4;
  opacity: 0.95;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
  max-height: 64rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 模版卡片内容适配 - 确保在397rpx高度内完整显示 */
.template-coupon-card .coupon-card-body {
  padding: 28rpx 32rpx;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.template-coupon-card .coupon-title {
  font-size: 32rpx;
  line-height: 1.3;
  margin-bottom: 12rpx;
  -webkit-line-clamp: 2;
  max-height: 84rpx;
}

.template-coupon-card .coupon-description {
  font-size: 22rpx;
  line-height: 1.4;
  max-height: 92rpx;
  -webkit-line-clamp: 3;
  flex: 1;
}

/* ==================== 券底部信息区域 ==================== */
.coupon-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 20rpx;
}

.coupon-meta {
  flex: 1;
  font-size: 20rpx;
  line-height: 1.4;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

.sender-info,
.expiry-info {
  margin-bottom: 4rpx;
}

.sender-label,
.expiry-label {
  opacity: 0.8;
  font-weight: 400;
}

.sender-name,
.expiry-date {
  font-weight: 600;
  opacity: 0.95;
}

/* 券状态指示器 */
.coupon-status-indicator {
  margin-left: 16rpx;
}

.status-badge {
  display: flex;
  align-items: center;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  font-size: 18rpx;
  font-weight: 600;
}

.status-icon {
  margin-right: 4rpx;
  font-size: 20rpx;
}

/* 状态颜色变体 */
.status-received { 
  background: rgba(34, 197, 94, 0.8);
  border-color: rgba(34, 197, 94, 0.3);
}

.status-pending_redeem { 
  background: rgba(245, 158, 11, 0.8);
  border-color: rgba(245, 158, 11, 0.3);
}

.status-redeemed { 
  background: rgba(107, 114, 128, 0.8);
  border-color: rgba(107, 114, 128, 0.3);
}

.status-expired { 
  background: rgba(239, 68, 68, 0.8);
  border-color: rgba(239, 68, 68, 0.3);
}

/* ==================== 券装饰元素 ==================== */
.coupon-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

/* 装饰孔 */
.decoration-holes {
  position: absolute;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}

.decoration-left {
  left: -8rpx;
}

.decoration-right {
  right: -8rpx;
}

.hole {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(4rpx);
}

/* 装饰线 */
.decoration-line {
  position: absolute;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  background: linear-gradient(
    90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 50%, 
    transparent 100%
  );
}

.decoration-top {
  top: 24rpx;
}

.decoration-bottom {
  bottom: 24rpx;
}

/* 背景装饰图案 */
.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2rpx, transparent 3rpx),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 1.5rpx, transparent 2rpx),
    linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 75%, rgba(255, 255, 255, 0.05) 75%);
  background-size: 60rpx 60rpx, 40rpx 40rpx, 20rpx 20rpx;
  background-position: 0 0, 20rpx 20rpx, 0 0;
  opacity: 0.6;
  animation: patternFloat 20s ease-in-out infinite;
  z-index: 0;
}

@keyframes patternFloat {
  0%, 100% { 
    transform: translate(0, 0) rotate(0deg); 
  }
  25% { 
    transform: translate(10rpx, -5rpx) rotate(0.5deg); 
  }
  50% { 
    transform: translate(-5rpx, 10rpx) rotate(-0.5deg); 
  }
  75% { 
    transform: translate(-10rpx, -5rpx) rotate(0.3deg); 
  }
}

/* ==================== 券类型风格变体 ==================== */

/* 承诺券 - 粉色渐变 */
.type-promise {
  background: linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%);
}

.type-promise .coupon-shadow {
  background: rgba(255, 109, 150, 0.3);
}

/* 任务券 - 蓝色渐变 */
.type-task {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.type-task .coupon-shadow {
  background: rgba(102, 126, 234, 0.3);
}

/* 奖励券 - 金色渐变 */
.type-reward {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  color: #2C2C2C;
}

.type-reward .coupon-shadow {
  background: rgba(255, 215, 0, 0.3);
}

.type-reward .number-value {
  background: linear-gradient(45deg, #8B4513, #654321);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 道歉券 - 紫色渐变 */
.type-apology {
  background: linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%);
}

.type-apology .coupon-shadow {
  background: rgba(138, 43, 226, 0.3);
}

/* 特别券 - 彩虹渐变 */
.type-special {
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 25%, #45B7D1 50%, #96CEB4 75%, #FFEAA7 100%);
  animation: rainbowShift 6s ease-in-out infinite;
}

.type-special .coupon-shadow {
  background: rgba(255, 107, 107, 0.3);
}

@keyframes rainbowShift {
  0%, 100% {
    background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 25%, #45B7D1 50%, #96CEB4 75%, #FFEAA7 100%);
  }
  50% {
    background: linear-gradient(135deg, #FFEAA7 0%, #FF6B6B 25%, #4ECDC4 50%, #45B7D1 75%, #96CEB4 100%);
  }
}

/* ==================== 玻璃拟态风格 ==================== */
.style-glass {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(15px);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.style-glass .coupon-shadow {
  background: rgba(255, 255, 255, 0.1);
}

/* ==================== 简约风格 ==================== */
.style-minimal {
  background: #FFFFFF;
  color: #333333;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.style-minimal .coupon-shadow {
  background: rgba(0, 0, 0, 0.1);
}

.style-minimal .coupon-type-tag {
  background: #FF6D96;
  color: white;
}

.style-minimal .number-value {
  background: linear-gradient(45deg, #FF6D96, #FF8FA3);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ==================== 3D效果风格 ==================== */
.style-3d {
  transform: perspective(1000rpx) rotateX(5deg);
  box-shadow: 0 25rpx 50rpx rgba(255, 109, 150, 0.4);
}

.style-3d:hover {
  transform: perspective(1000rpx) rotateX(0deg) translateY(-8rpx) scale(1.02);
}

/* ==================== 快捷操作区域 ==================== */
.coupon-actions {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  display: flex;
  gap: 8rpx;
  z-index: 3;
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .coupon-card-body {
    padding: 28rpx 32rpx;
  }
  
  .coupon-title {
    font-size: 32rpx;
  }
  
  .coupon-main-icon {
    font-size: 56rpx;
  }
  
  .number-value {
    font-size: 24rpx;
  }
}

/* ==================== 深色模式适配 ==================== */
@media (prefers-color-scheme: dark) {
  .style-minimal {
    background: #2C2C2C;
    color: #FFFFFF;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
  }
  
  .style-minimal .coupon-shadow {
    background: rgba(0, 0, 0, 0.3);
  }
}
