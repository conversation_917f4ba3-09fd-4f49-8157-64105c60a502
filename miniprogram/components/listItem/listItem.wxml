<!--
  通用列表项组件 WXML
  适用于设置菜单、通知列表等，支持左侧图标/图片、主标题、副标题/描述、右侧箭头或操作元素
-->
<view class="list-item {{itemClass}} {{disabled ? 'disabled' : ''}} {{customClass}}" 
      bind:tap="onItemTap"
      hover-class="{{clickable ? 'item-hover' : ''}}" 
      hover-stay-time="150">
  
  <!-- 左侧图标/图片 -->
  <view class="list-item-icon" wx:if="{{icon || iconImage}}">
    <image wx:if="{{iconImage}}" 
           class="icon-image" 
           src="{{iconImage}}" 
           mode="aspectFill"
           lazy-load 
           bindload="onIconLoad"
           binderror="onIconError" />
    <text wx:else class="icon-text">{{icon}}</text>
  </view>
  
  <!-- 主要内容区域 -->
  <view class="list-item-content">
    <view class="list-item-title">
      <text class="title-text">{{title}}</text>
      
      <!-- 标题旁的徽章 -->
      <view class="title-badge" wx:if="{{badge}}">
        <text class="badge-text">{{badge}}</text>
      </view>
      
      <!-- 标题旁的标签 -->
      <view class="title-tags" wx:if="{{tags && tags.length > 0}}">
        <text class="tag" wx:for="{{tags}}" wx:key="index">{{item}}</text>
      </view>
    </view>
    
    <!-- 副标题/描述 -->
    <view class="list-item-subtitle" wx:if="{{subtitle}}">
      <text class="subtitle-text">{{subtitle}}</text>
    </view>
    
    <!-- 额外描述 -->
    <view class="list-item-description" wx:if="{{description}}">
      <text class="description-text">{{description}}</text>
    </view>
    
    <!-- 自定义内容插槽 -->
    <slot name="content"></slot>
  </view>
  
  <!-- 右侧内容区域 -->
  <view class="list-item-right">
    <!-- 右侧文本信息 -->
    <view class="right-text" wx:if="{{rightText}}">
      <text class="right-text-content">{{rightText}}</text>
    </view>
    
    <!-- 右侧状态指示器 -->
    <view class="right-status" wx:if="{{status}}">
      <text class="status-text status-{{status}}">{{statusText}}</text>
    </view>
    
    <!-- 自定义右侧内容插槽 -->
    <slot name="right"></slot>
    
    <!-- 右侧箭头 -->
    <view class="list-item-arrow" wx:if="{{showArrow}}">
      <text class="arrow-icon">{{arrowIcon}}</text>
    </view>
  </view>
  
</view>

<!-- 分隔线 -->
<view class="list-item-divider" wx:if="{{showDivider}}"></view>

<!-- 组件样式 -->
<style>
.list-item {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  background: white;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  cursor: pointer;
  transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 112rpx;
  position: relative;
  overflow: hidden;
}

.list-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(233, 30, 99, 0.02) 0%, rgba(244, 143, 177, 0.02) 100%);
  opacity: 0;
  transition: opacity 200ms ease;
  pointer-events: none;
}

.list-item:hover::before {
  opacity: 1;
}

.item-hover {
  transform: scale(0.98);
  background: #f9fafb;
  border-color: #e5e7eb;
}

.list-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 左侧图标区域 */
.list-item-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
  border-radius: 16rpx;
  background: #f9fafb;
  transition: all 200ms ease;
}

.list-item:hover .list-item-icon {
  background: #f3f4f6;
  transform: scale(1.05);
}

.icon-image {
  width: 64rpx;
  height: 64rpx;
  border-radius: 12rpx;
  object-fit: cover;
}

.icon-text {
  font-size: 48rpx;
  line-height: 1;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

/* 主要内容区域 */
.list-item-content {
  flex: 1;
  min-width: 0;
  padding-right: 16rpx;
}

.list-item-title {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
  gap: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #111827;
  line-height: 1.4;
}

.title-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 36rpx;
  height: 36rpx;
  padding: 0 12rpx;
  background: #ef4444;
  color: white;
  border-radius: 18rpx;
  font-size: 20rpx;
  font-weight: 600;
  line-height: 1;
}

.badge-text {
  transform: scale(0.9);
}

.title-tags {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}

.tag {
  padding: 4rpx 12rpx;
  background: #e5e7eb;
  color: #6b7280;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1;
}

.list-item-subtitle {
  margin-bottom: 6rpx;
}

.subtitle-text {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.4;
}

.list-item-description {
  margin-top: 4rpx;
}

.description-text {
  font-size: 24rpx;
  color: #9ca3af;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 右侧内容区域 */
.list-item-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-shrink: 0;
}

.right-text {
  text-align: right;
}

.right-text-content {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 500;
}

.right-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  text-align: center;
}

.status-text {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1;
}

.status-success {
  color: #059669;
  background: #d1fae5;
}

.status-warning {
  color: #d97706;
  background: #fef3c7;
}

.status-error {
  color: #dc2626;
  background: #fee2e2;
}

.status-info {
  color: #2563eb;
  background: #dbeafe;
}

.list-item-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  transition: transform 200ms ease;
}

.list-item:hover .list-item-arrow {
  transform: translateX(4rpx);
}

.arrow-icon {
  font-size: 32rpx;
  color: #d1d5db;
  font-weight: 300;
}

/* 分隔线 */
.list-item-divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #e5e7eb 20%, #e5e7eb 80%, transparent);
  margin: 0 24rpx;
}

/* 响应式适配 */
@media screen and (max-width: 480rpx) {
  .list-item {
    padding: 24rpx 20rpx;
    min-height: 96rpx;
  }
  
  .list-item-icon {
    width: 64rpx;
    height: 64rpx;
    margin-right: 20rpx;
  }
  
  .icon-image {
    width: 48rpx;
    height: 48rpx;
  }
  
  .icon-text {
    font-size: 40rpx;
  }
  
  .title-text {
    font-size: 30rpx;
  }
  
  .subtitle-text {
    font-size: 24rpx;
  }
  
  .right-text-content {
    font-size: 26rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .list-item {
    background: #1f2937;
    border-color: #374151;
  }
  
  .item-hover {
    background: #374151;
    border-color: #4b5563;
  }
  
  .list-item-icon {
    background: #374151;
  }
  
  .list-item:hover .list-item-icon {
    background: #4b5563;
  }
  
  .title-text {
    color: #f9fafb;
  }
  
  .subtitle-text {
    color: #d1d5db;
  }
  
  .description-text {
    color: #9ca3af;
  }
  
  .right-text-content {
    color: #d1d5db;
  }
  
  .tag {
    background: #4b5563;
    color: #d1d5db;
  }
  
  .list-item-divider {
    background: linear-gradient(90deg, transparent, #374151 20%, #374151 80%, transparent);
  }
}
</style>
