# 情侣信任券小程序集成与测试指南

## 1. 代码整合指南

本部分提供将各个模块代码整合到微信开发者工具标准项目结构中的详细步骤，并说明公共组件、工具函数及配置文件的使用方法。

### 1.1 项目结构概览

假设您已在微信开发者工具中创建并导入了微信云开发项目。标准的项目结构通常如下：

```
your-miniprogram-project/
├── cloudfunctions/         # 云函数目录
│   ├── bindWithInvitationCode/
│   │   ├── index.js
│   │   ├── package.json
│   ├── checkBindingStatus/
│   │   ├── index.js
│   │   ├── package.json
│   ├── generateInvitationCode/
│   │   ├── index.js
│   │   ├── package.json
│   ├── login/
│   │   ├── index.js
│   │   ├── package.json
│   ├── unbindCouple/
│   │   ├── index.js
│   │   ├── package.json
│   ├── autoUpdateCouponToExpired/
│   │   ├── index.js
│   │   ├── package.json
│   ├── confirmCouponRedemption/
│   │   ├── index.js
│   │   ├── package.json
│   ├── couponRedeemAction/ # 核销操作统一函数
│   │   ├── index.js
│   │   ├── package.json
│   ├── requestCouponRedemption/
│   │   ├── index.js
│   │   ├── package.json
│   ├── sendCoupon/         # 发送券函数 (MVP)
│   │   ├── index.js
│   │   ├── package.json
│   ├── receiveCoupon/      # 接收券函数 (MVP)
│   │   ├── index.js
│   │   ├── package.json
│   |── getCouponDetail/    # 获取券详情
│   │   ├── index.js
│   │   ├── package.json
│   |── getCouponList/      # 获取券列表
│   │   ├── index.js
│   │   ├── package.json
│   ├── ... 其他云函数
│   └── js/                 # 云函数公共JS工具库
|       ├── couponService.js
|       ├── database.js
|       ├── messageTemplate.js
|       ├── notification.js
|       ├── subscribeMessageService.js
|       └── utils.js
├── miniprogram/            # 小程序前端代码目录
│   ├── pages/              # 页面目录
│   │   ├── index/
│   │   │   ├── index.js
│   │   │   ├── index.json
│   │   │   ├── index.wxml
│   │   │   └── index.wxss
│   │   ├── binding/        # 情侣绑定页面
│   │   │   ├── binding.js
│   │   │   ├── binding.json
│   │   │   ├── binding.wxml
│   │   │   └── binding.wxss
│   │   ├── createCoupon/   # 创建信任券页面
│   │   │   ├── createCoupon.js
│   │   │   ├── createCoupon.json
│   │   │   ├── createCoupon.wxml
│   │   │   └── createCoupon.wxss
│   │   ├── myCoupons/      # 我的券包页面
│   │   │   ├── myCoupons.js
│   │   │   ├── myCoupons.json
│   │   │   ├── myCoupons.wxml
│   │   │   └── myCoupons.wxss
│   │   ├── couponDetail/   # 券详情页面
│   │   │   ├── couponDetail.js
│   │   │   ├── couponDetail.json
│   │   │   ├── couponDetail.wxml
│   │   │   └── couponDetail.wxss
│   │   ├── couponRedeem/   # 券核销相关页面（申请/确认/拒绝）
│   │   │   ├── couponRedeem.js
│   │   │   ├── couponRedeem.json
│   │   │   ├── couponRedeem.wxml
│   │   │   └── couponRedeem.wxss
│   │   ├── notifications/  # 消息通知列表页面
│   │   │   ├── notifications.js
│   │   │   ├── notifications.json
│   │   │   ├── notifications.wxml
│   │   │   └── notifications.wxss
│   │   ├── profile/        # 个人中心页面
│   │   │   ├── profile.js
│   │   │   ├── profile.json
│   │   │   ├── profile.wxml
│   │   │   └── profile.wxss
│   │   ├── settings/       # 设置页面
│   │   │   ├── settings.js
│   │   │   ├── settings.json
│   │   │   ├── settings.wxml
│   │   │   └── settings.wxss
│   │   └── about/          # 关于我们页面
│   │       ├── about.js
│   │       ├── about.json
│   │       ├── about.wxml
│   │       └── about.wxss
│   ├── components/         # 可复用UI组件目录
│   │   ├── couponCard/       # 券卡片组件
│   │   │   ├── couponCard.js
│   │   │   ├── couponCard.json
│   │   │   ├── couponCard.wxml
│   │   │   └── couponCard.wxss
│   │   ├── customModal/      # 自定义模态框
│   │   │   ├── customModal.js
│   │   │   ├── customModal.json
│   │   │   ├── customModal.wxml
│   │   │   └── customModal.wxss
│   │   ├── emptyState/       # 空状态组件
│   │   │   ├── emptyState.js
│   │   │   ├── emptyState.json
│   │   │   ├── emptyState.wxml
│   │   │   └── emptyState.wxss
│   │   ├── listItem/         # 列表项组件
│   │   │   ├── listItem.js
│   │   │   ├── listItem.json
│   │   │   ├── listItem.wxml
│   │   │   └── listItem.wxss
│   │   └── loadingIndicator/ # 加载指示器
│   │       ├── loadingIndicator.js
│   │       ├── loadingIndicator.json
│   │       ├── loadingIndicator.wxml
│   │       └── loadingIndicator.wxss
│   ├── utils/              # 前端JS工具函数目录
│   │   ├── api.js          # API封装
│   │   ├── commonUtil.js   # 通用辅助
│   │   ├── dateUtil.js     # 日期处理
│   │   ├── userUtil.js     # 用户状态管理
│   │   └── validator.js    # 表单验证
│   ├── images/             # 图片资源
│   │   └── ... (如 tabbar 图标, share 图片, default avatar)
│   ├── app.js              # 应用逻辑
│   ├── app.json            # 全局配置
│   ├── app.wxss            # 全局样式
│   └── sitemap.json        # 页面索引
├── project.config.json     # 项目配置
└── README.md               # 项目说明
```

### 1.2 代码文件放置与结构调整

1.  **前端页面 (miniprogram/pages/)**: 将每个页面的 `.js`, `.json`, `.wxml`, `.wxss` 文件分别放置到 `miniprogram/pages/` 目录下对应的页面文件夹中 (如 `index`, `binding`, `createCoupon` 等)。
2.  **前端组件 (miniprogram/components/)**: 将每个组件的四个文件 (`.js`, `.json`, `.wxml`, `.wxss`) 放置到 `miniprogram/components/` 目录下对应的组件文件夹中 (如 `couponCard`, `customModal`, `emptyState`, `listItem`, `loadingIndicator`)。
3.  **前端公共JS工具函数 (miniprogram/utils/)**: 将公共的JS工具函数文件 (如 `api.js`, `commonUtil.js`, `dateUtil.js`, `userUtil.js`, `validator.js`) 统一放置到 `miniprogram/utils/` 目录下。
4.  **全局配置文件 (miniprogram/app.json, project.config.json)**: 将提供的 `app.json` 和 `project.config.json` 文件直接替换或合并到项目根目录下的同名文件和 `miniprogram/` 目录下的 `app.json` 文件。特别注意，`project.config.json` 位于项目根目录，`app.json` 位于 `miniprogram/` 目录下。
5.  **云函数 (cloudfunctions/)**: 在 `cloudfunctions/` 目录下为每个云函数创建一个独立的文件夹（如 `login`, `generateInvitationCode`, `bindWithInvitationCode` 等），将对应的 `index.js` 和 `package.json` 文件拷贝进去。
6.  **云函数公共JS库 (cloudfunctions/js/)**: 在 `cloudfunctions/` 目录下创建一个 `js` 文件夹，将云函数通用的JS文件 (如 `couponService.js`, `database.js`, `messageTemplate.js`, `notification.js`, `subscribeMessageService.js`, `utils.js`) 拷贝到其中。

### 1.3 公共组件和工具函数的引入与使用

#### 1.3.1 前端页面中使用公共组件

要在页面中使用自定义组件，需要在页面的 `.json` 文件中进行引用声明：

```json
// miniprogram/pages/myCoupons/myCoupons.json
{
  "usingComponents": {
    "coupon-card": "/components/couponCard/couponCard",
    "empty-state": "/components/emptyState/emptyState",
    "loading-indicator": "/components/loadingIndicator/loadingIndicator",
    "list-item": "/components/listItem/listItem",
    "custom-modal": "/components/customModal/customModal"
    /* 其他组件 */
  },
  "navigationBarTitleText": "我的券包",
  "enablePullDownRefresh": true,
  "backgroundColor": "#FFB6C1",
  "backgroundTextStyle": "light",
  "navigationBarBackgroundColor": "#FFB6C1",
  "navigationBarTextStyle": "white",
  "onReachBottomDistance": 100
}
```
然后在页面的 `.wxml` 文件中像使用原生组件一样使用它们：

```wxml
<!-- miniprogram/pages/myCoupons/myCoupons.wxml -->
<view class="coupon-list">
  <coupon-card 
    wx:for="{{couponList}}" 
    wx:key="_id"
    couponId="{{item._id}}"
    title="{{item.name}}"
    description="{{item.description}}"
    status="{{item.status}}"
    sender="{{item.senderNickname}}"
    expiryDate="{{item.expiryDate}}"
    bind:tap="goToCouponDetail"
  />
</view>

<empty-state wx:if="{{couponList.length === 0 && !isLoading}}" ... />
<loading-indicator wx:if="{{isLoading}}" ... />
```

#### 1.3.2 前端页面中使用JS工具函数

在页面的 `.js` 文件中，可以使用 `require` 导入公共工具函数：

```javascript
// miniprogram/pages/myCoupons/myCoupons.js
const app = getApp();
const userUtil = require('../../utils/userUtil'); // 引入userUtil
const dateUtil = require('../../utils/dateUtil'); // 引入dateUtil
const api = require('../../utils/api'); // 引入api请求封装

Page({
  data: {
    // ...
  },

  onLoad: function() {
    console.log('我的券包页面加载');
    const userInfo = userUtil.getUserInfo(); // 使用userUtil
    // ...
  },
  
  formatDate: dateUtil.formatDate, // 页面内引用或直接使用

  loadCoupons() {
    // 使用API调用云函数
    api.cloudRequest('getCouponList', { type: this.data.currentTab }).then(res => {
      if (res.success) {
        this.setData({ couponList: res.data.coupons });
      } else {
        console.error('获取列表失败', res.error);
      }
    });
  },
  
  // ...
});
```
您也可以将常用的工具函数直接挂载到页面的方法中，或者在 `app.js` 中全局挂载。

#### 1.3.3 云函数中使用公共JS库

在云函数的 `index.js` 中，可以使用相对路径 `require` 导入云函数公共JS文件：

```javascript
// cloudfunctions/yourFunctionName/index.js
const cloud = require('wx-server-sdk');
// 根据实际存放位置调整路径
const database = require('../js/database'); 
const utils = require('../js/utils'); 

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

exports.main = async (event, context) => {
  console.log('云函数执行');
  const result = await database.getUser(event.userId);
  console.log(utils.formatDate(new Date()));
  // ... your cloud function logic
};
```
请确保路径与您实际的文件结构一致。

### 1.4 配置文件最终确认和应用

1.  **`app.json` (miniprogram/)**: 确保 `pages` 列表包含了所有页面路径，`tabBar` 配置正确（如果你的设计包含 TabBar）。`window` 配置影响全局样式。`cloud: true` 是启用云开发的关键配置。提供的 `app.json` 似乎包含 `tabBar` 配置，请根据实际UI设计验证路径和图标。
2.  **`project.config.json` (项目根目录)**: 确保 `appid` 替换为您自己的小程序 AppID，`projectname` 正确填写，`cloudfunctionRoot` 指向 `cloudfunctions/` 目录，`miniprogramRoot` 指向 `miniprogram/` 目录。其他设置如 `es6`, `postcss`, `minified`, `enhance` 等通常保持默认即可。

### 1.5 云函数部署和前端调用联调

1.  **部署云函数**: 在微信开发者工具中，右键点击 `cloudfunctions` 目录下的各个云函数文件夹，选择 "**上传部署** -> **云端安装依赖(不上传node_modules)**"。确保每个函数都部署成功且依赖安装无误。
2.  **初始化云开发环境**: 在 `app.js` 或首页 `index.js` 中，确保已正确初始化云开发环境，并指定您的云开发环境ID：
    ```javascript
    // app.js
    App({
      onLaunch: function () {
        if (!wx.cloud) {
          console.error('请使用 2.2.3 或以上的基础库进行开发');
        } else {
          wx.cloud.init({
            // env 参数说明：
            //   env 参数决定小程序云开发的环境
            //   默认是
            // env: wx.cloud.getCloudEnv(),
            // env: 'YOUR_CLOUD_ENV_ID', // 替换为你的云开发环境ID
            env: 'cloud1-xxxxxx', // 替换为你的实际环境 ID
            traceUser: true,
          });
        }
        // ... 其他启动逻辑
      },
      globalData: {
        userInfo: null
      }
    });
    ```
    或者在需要调用云函数的页面或工具函数中局部初始化：
    ```javascript
    // miniprogram/pages/index/index.js
    const cloud = wx.cloud; // 获取 cloud 对象
    Page({
      onLoad: function() {
        // cloud.init({ env: 'YOUR_CLOUD_ENV_ID' }); // 如果未在 app.js 初始化，可以在此局部初始化
        // ...
      }
    });
    ```
3.  **前端调用云函数**: 在前端代码中使用 `wx.cloud.callFunction` 调用已部署的云函数。例如：
    ```javascript
    // 调用登录云函数
    wx.cloud.callFunction({
      name: 'login',
      data: { js_code: loginRes.code, userInfo: profileRes.userInfo },
      success: res => {
        console.log('登录成功', res.result);
      },
      fail: err => {
        console.error('登录失败', err);
      }
    });
    
    // 调用创建券云函数
    wx.cloud.callFunction({
      name: 'createCoupon',
      data: { name, description, rules, expiryDate },
      success: res => {
        console.log('创建券成功', res.result);
      },
      fail: err => {
        console.error('创建券失败', err);
      }
    });
    ```
    确保 `name` 正确对应云函数文件夹的名称。
4.  **数据库权限配置**: 在云开发控制台中，为 `users`, `couples`, `coupons`, `notifications`, `message_logs` 等集合配置安全规则。根据规格说明书中的数据安全考虑，应严格限制读写权限，通常只允许当前用户对与自己相关的记录进行读写。复杂的权限控制（如只有情侣方可读写 `coupons`）需要在云函数中通过代码强制执行。
5.  **消息模板配置**: 在微信公众平台中申请必要的订阅消息模板，并将申请到的模板ID替换到云函数公共库 `js/messageTemplate.js` 中的 `TEMPLATE_IDS` 配置项。

### 1.6 注意事项

*   **云函数依赖**: 每个云函数都需要独立的 `package.json` 文件，并在部署时安装其依赖 (`wx-server-sdk` 是必须的)。如果多个云函数共享公共JS文件（如 `cloudfunctions/js/`），这些共享文件不需要单独的 `package.json`，只需在调用它们的云函数中正确 `require` 即可。
*   **权限问题**: `wx.cloud.getWXContext()` 只能在云函数中获取当前用户的 OpenID，用于后端验证用户身份和权限。前端无法直接伪造用户身份。
*   **日志监控**: 开发过程中，利用云开发控制台的日志功能查看云函数执行情况和错误信息。
*   **代码同步**: 前端和云函数代码修改后，都需要在开发者工具中保存并上传部署（云函数）或编译预览（前端）。

## 2. 单元测试用例

单元测试主要关注代码中的独立逻辑单元，例如特定的函数、方法的输入和输出是否符合预期。

### 2.1 前端JavaScript逻辑单元测试用例描述

前端单元测试通常可以使用 Jest 或模拟小程序环境的框架 (如 `miniprogram-simulate`) 来进行。以下为测试用例描述：

| 测试模块/文件        | 功能说明             | 测试函数/方法（名称） | 输入参数/前置 Mock 数据                                  | 预期输出/行为                                                                        | 测试目的                            |
| :------------------- | :------------------- | :-------------------- | :------------------------------------------------------- | :--------------------------------------------------------------------------------- | :---------------------------------- |
| `miniprogram/utils/validator.js` | 表单验证工具         | `validateRequired`    | `''`, `null`, `undefined`, `' '`, `'abc'`, `[]`, `['a']`, `{}` | `false`, `false`, `false`, `false`, `true`, `false`, `true`, `false`                 | 验证非空判断的正确性                |
|                      |                      | `validateEmail`       | `'<EMAIL>'`, `'invalid-email'`, `'test@.com'` | `true`, `false`, `false`                                                           | 验证邮箱格式校验的准确性            |
|                      |                      | `validatePhone`       | `'13800138000'`, `'12345678900'`, `'1381234567'`         | `true`, `false`, `false`                                                           | 验证手机号格式校验的准确性          |
|                      |                      | `validateLength`      | `('abc', 2, 4)`, `('a', 2, 4)`, `('abcde', 2, 4)`        | `{ valid: true }`, `{ valid: false, errors: [...] }`, `{ valid: false, errors: [...] }` | 验证字符串长度限制的正确性          |
| `miniprogram/utils/dateUtil.js` | 日期时间处理工具     | `formatDate`          | `new Date('2024-01-01T10:30:00Z')`, `'YYYY-MM-DD HH:mm'` | `'2024-01-01 18:30'` (假设东八区)                                                  | 验证日期格式化功能                  |
|                      |                      | `timeAgo`             | `Date.now() - 60000`, `Date.now() - 3600000 * 5`       | `'1分钟前'`, `'5小时前'`                                                           | 验证相对时间计算功能                |
| `miniprogram/utils/userUtil.js` | 用户状态管理工具     | `setUserInfo`         | `{ nickName: 'Test', avatarUrl: 'url', openid: 'abc' }` | 用户信息成功存储到本地缓存                                                         | 验证用户信息保存功能                |
|                      |                      | `getUserInfo`         | 设置并过期用户信息 => 调用获取 | 返回 `null` 或最新信息                                                               | 验证用户信息获取及过期判断          |
| `miniprogram/utils/api.js` | API请求封装工具      | `cloudRequest`        | Mock `wx.cloud.callFunction` 成功返回数据            | 返回 `{ success: true, data: ... }` 且无错误提示                                     | 验证封装对成功响应的处理            |
|                      |                      | `cloudRequest`        | Mock `wx.cloud.callFunction` 失败返回错误码          | 返回 `{ success: false, error: ... }` 并弹出错误提示                               | 验证封装对失败响应及错误提示的处理 |
| `miniprogram/pages/createCoupon/createCoupon.js` | 创建券页面逻辑       | `validateForm`        | 合法的表单数据对象                                       | `true`                                                                             | 验证表单验证函数是否能识别合法输入  |
|                      |                      | `validateForm`        | 缺少必填项或长度不合法的表单数据对象                       | `false` 并填充 `errors` 数据                                                       | 验证表单验证函数是否能识别非法输入  |
| `miniprogram/pages/myCoupons/myCoupons.js` | 我的券包页面逻辑     | `getStatusText`       | `'received'`, `'pending_redeem'`, `'expired'`            | `'已接收'`, `'待兑现'`, `'已过期'`                                                | 验证券状态文本映射                  |
|                      |                      | `isExpired`           | 晚于今天日期字符串, 早于今天日期字符串                     | `false`, `true`                                                                    | 验证过期日期 판단                   |
| `miniprogram/pages/couponDetail/couponDetail.js` | 券详情页面逻辑       | `getStatusExplanation`| `('received', false)`, `('pending_redeem', true)`          | 正确的状态说明文本                                                               | 验证状态说明文本返回的正确性        |

### 2.2 后端云函数单元测试用例描述

云函数单元测试主要验证 `index.js` 中 `exports.main` 函数的逻辑。通常需要 Mock 微信云开发 SDK 的相关方法 (如 `cloud.database()`, `cloud.getWXContext()`, `db.collection().where().get()`, `db.collection().doc().update()`, `db.runTransaction()`)。

| 测试模块/云函数        | 功能说明               | 测试事件对象/参数                                              | 预期云数据库操作/返回值                                                                                                                                                                                             | 测试目的                                     |
| :--------------------- | :--------------------- | :------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :------------------------------------------- |
| `cloudfunctions/login` | 用户登录               | `event = { js_code: 'test_code', userInfo: { ... } }`         | 如果用户不存在：调用 `db.collection('users').add()` 添加新用户，`_id` 和 `_openid` 为 OpenID。 <br> 如果用户存在：调用 `db.collection('users').doc(openid).update()` 更新用户登录时间。 <br> 返回 `{ success: true, data: { openid, isNewUser, bindingStatus, userInfo } }` | 验证新用户注册和老用户登录的正确性及数据更新 |
|                        |                        | `event = { js_code: 'test_code', userInfo: { ... } }` with Mock `db.collection('users').doc(openid).get()` returning existing user with `partner_openid` | 返回 `{ success: true, data: { isNewUser: false, bindingStatus: { isBound: true, ... } } }`                                                                                                                          | 验证已绑定用户的登录状态返回                 |
| `cloudfunctions/generateInvitationCode` | 生成邀请码             | `openid = 'test_openid'` with Mock `db.collection('users').doc('test_openid').get()` returning unbound user | 调用 `db.collection('users').doc(openid).update()` 更新用户的 `invitation_code` (6位随机码) 和 `invitation_code_expires_at` (未来24小时)。 <br> 返回 `{ success: true, data: { invitationCode, expiresAt, expiresIn: '24小时' } }` | 验证邀请码生成和设置有效期的逻辑             |
|                        |                        | `openid = 'test_openid'` with Mock `db.collection('users').doc('test_openid').get()` returning bound user | 返回 `{ success: false, error: 'ALREADY_BOUND' }`                                                                                                                                                                   | 验证已绑定用户生成邀请码的限制               |
| `cloudfunctions/bindWithInvitationCode` | 使用邀请码绑定         | `event = { invitationCode: 'TEST12' }` with Mock 获取邀请方和非绑定接受方数据 | 调用 `db.runTransaction()` 执行两个 `db.collection('users').doc().update()`，分别更新双方的 `partner_openid`，并清除邀请方的邀请码。 <br> 返回 `{ success: true, data: { partnerInfo, bindingTime } }`                 | 验证情侣绑定流程和状态更新的原子性           |
|                        |                        | `event = { invitationCode: 'INVALID' }`                            | 返回 `{ success: false, error: 'INVALID_INVITATION_CODE' }` 或 `'INVALID_OR_EXPIRED_CODE'`                                                                                                                            | 验证无效/过期邀请码的处理                    |
|                        |                        | `event = { invitationCode: 'TEST12' }` with Mock 接受方已绑定       | 返回 `{ success: false, error: 'ACCEPTING_USER_ALREADY_BOUND' }`                                                                                                                                                      | 验证接受方已绑定情侣的限制                   |
| `cloudfunctions/createCoupon` | 创建信任券             | `event = { name: '测试券', description: '测试描述', expiryDate: '2099-12-31' }` with Mock 绑定用户数据 | 调用 `db.collection('coupons').add()` 添加券记录，状态为 `'created'`，`senderId` 为当前用户，`receiverId` 为伙伴，`coupleId` 为双方ID排序组合。 <br> 返回 `{ success: true, data: { couponId, coupon } }`             | 验证券创建和数据存储的正确性                 |
|                        |                        | `event = { name: '短', description: '描述过短', expiryDate: '2024-01-01' }` | 返回 `{ success: false, error: 'INVALID_NAME' }`, `'INVALID_DESCRIPTION'`, 或 `'INVALID_EXPIRY_DATE'`                                                                                                                      | 验证输入参数校验的正确性                     |
| `cloudfunctions/sendCoupon` | 发送信任券             | `event = { couponId: 'coupon_id_1' }` with Mock 未发送状态券数据和绑定用户数据 | 调用 `db.collection('coupons').doc('coupon_id_1').update()` 更新券状态为 `'sent'`，设置 `sendTime`。 <br> 调用 `db.collection('notifications').add()` 创建新券通知记录。 <br> 返回 `{ success: true, data: {...} }` | 验证券发送流程和状态、通知记录更新           |
|                        |                        | `event = { couponId: 'coupon_id_1' }` with Mock 已发送状态券数据                                | 返回 `{ success: false, error: 'INVALID_STATUS' }`                                                                                                                                                                   | 验证券状态限制的正确性                     |
| `cloudfunctions/receiveCoupon` | 接收信任券             | `event = { couponId: 'coupon_id_2' }` with Mock 已发送状态券数据和接收方绑定数据 | 调用 `db.collection('coupons').doc('coupon_id_2').update()` 更新券状态为 `'received'`，设置 `receiveTime`。 <br> 调用 `db.collection('notifications').add()` 创建接收确认通知记录。 <br> 返回 `{ success: true, data: {...} }` | 验证券接收流程和状态、通知记录更新           |
|                        |                        | `event = { couponId: 'coupon_id_2' }` with Mock 已接收状态券数据                              | 返回 `{ success: false, error: 'INVALID_STATUS' }`                                                                                                                                                                   | 验证券状态限制的正确性                     |
| `cloudfunctions/couponRedeemAction` | 核销操作统一函数       | `event = { action: 'requestRedeem', couponId: 'coupon_id_3', redeemNote: '...', redeemImages: [...] }` with Mock 已接收状态券和接收方数据 | 调用 `db.collection('coupons').doc('coupon_id_3').update()` 更新状态为 `'pending_redeem'`，记录核销说明和图片，设置 `redeemApplyTime`。 <br> 调用 `db.collection('notifications').add()` 创建核销申请通知。 <br> 返回 `{ success: true, data: {...} }` | 验证接收方申请核销流程                     |
|                        |                        | `event = { action: 'confirmRedeem', couponId: 'coupon_id_4' }` with Mock 待核销状态券和发送方数据 | 调用 `db.collection('coupons').doc('coupon_id_4').update()` 更新状态为 `'redeemed'`，设置 `redeemTime`。 <br> 调用 `db.collection('notifications').add()` 创建核销完成通知（给双方）。 <br> 返回 `{ success: true, data: {...} }` | 验证发送方确认核销流程及通知发送           |
|                        |                        | `event = { action: 'rejectRedeem', couponId: 'coupon_id_5', rejectReason: '...' }` with Mock 待核销状态券和发送方数据 | 调用 `db.collection('coupons').doc('coupon_id_5').update()` 更新状态为 `'received'`，记录拒绝原因，清除申请相关信息。 <br> 调用 `db.collection('notifications').add()` 创建拒绝通知（给接收方）。 <br> 返回 `{ success: true, data: {...} }` | 验证发送方拒绝核销流程                     |
| `cloudfunctions/autoUpdateCouponToExpired` | 自动更新过期券状态   | 无参数 (定时触发) with Mock 存在状态非 redeemed/expired 且 expiryDate < now 的券 | 调用 `db.collection('coupons').where(...).update()` 批量更新这些券状态为 `'expired'`。 <br> 为每张过期券调用 `db.collection('notifications').add()` 创建过期通知（给双方）。 <br> 返回成功处理的数量 | 验证自动任务检查和更新过期券状态           |
| `cloudfunctions/unbindCouple` | 解除情侣绑定           | `openid = 'user1_openid'` with Mock 该用户已绑定，且其伙伴存在 | 调用 `db.runTransaction()` 执行两个 `db.collection('users').doc().update()`，分别清除双方的 `partner_openid`。 <br> 返回 `{ success: true, data: {...} }`                                                               | 验证情侣解绑的原子性操作                  |
|                        |                        | `openid = 'user1_openid'` with Mock 该用户已绑定但伙伴不存在 | 调用 `db.collection('users').doc('user1_openid').update()` 清除当前用户绑定信息。 <br> 返回 `{ success: true, data: {...}, warning: '...' }`                                                                         | 验证伙伴信息不存在时的解绑处理             |

## 3. 集成测试场景

集成测试关注不同模块（前端页面、组件、云函数、数据库）之间的协同工作是否正常，覆盖用户端对应用的整体使用流程。

### 3.1 测试环境与准备

*   **环境**：微信开发者工具，连接真实的云开发环境（需要开通并配置云开发服务）。
*   **用户**：准备至少两个微信用户测试账号，用于模拟情侣互动。
*   **数据准备**：
    *   清空或重置 `users`, `couples`, `coupons`, `notifications` 集合数据（**谨慎操作，尤其在非开发环境**）。
    *   确保云函数已部署。
    *   （如果在测试环境）确保数据库安全规则允许测试用户进行基本操作。

### 3.2 测试场景设计

#### 3.2.1 场景1：新用户注册与情侣绑定流程 (A用户发起邀请，B用户接受)

| 测试项编号 | 前提条件                       | 操作步骤                                                                                                                                                                                               | 预期结果                                                                                                                                                                                                                                                           |
| :--------- | :----------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1.1        | 用户A和用户B均未登录小程序     | 1. 用户A打开小程序，同意微信授权登录。<br> 2. 进入首页/引导页，提示未绑定。<br> 3. 用户A进入情侣绑定页面。<br> 4. 用户A选择“创建邀请”，点击“生成邀请码”。<br> 5. 成功生成邀请码，用户A复制邀请码。<br> 6. 用户B打开小程序，同意微信授权登录。<br> 7. 进入首页/引导页，提示未绑定。<br> 8. 用户B进入情侣绑定页面。<br> 9. 用户B选择“输入邀请码”，输入用户A生成的邀请码。<br> 10. 验证码有效，页面显示用户A信息。<br> 11. 用户B点击“确认加入”。 | 用户A和用户B均成功登录，`users`集合中各自有记录。<br> 用户A生成邀请码成功，`users`集合中用户A记录的 `invitation_code` 和 `invitation_code_expires_at` 被设置。<br> 用户B验证邀请码成功，页面正确显示用户A昵称/头像。<br> **点击确认加入后**: <br> - `users`集合中用户A和用户B的 `partner_openid` 分别更新为对方的 OpenID。<br> - `users`集合中用户A的邀请码相关字段被清除。<br> - 用户A和用户B的绑定状态检查结果均为“已绑定”，可见对方信息。<br> - 页面显示“绑定成功”，跳转至首页。<br> - （可选）后台日志显示绑定成功。 |
| 1.2        | 用户A已生成邀请码，但已过期    | 1. 用户A生成邀请码（确保后台邀请码已过期或手动修改数据库使之过期）。<br> 2. 用户B尝试输入该过期邀请码。                                                                                             | 验证邀请码失败，提示“邀请码已过期”。                                                                                                                                                                                                                            |
| 1.3        | 用户A已生成邀请码，但已绑定他人 | 1. 用户A已生成邀请码。<br> 2. 用户A已通过其他方式（或模拟）与用户C绑定。<br> 3. 用户B尝试输入用户A的邀请码。                                                                                               | 验证邀请码失败，提示“邀请码无效”或类似错误（取决于云函数实现，可能返回用户已绑定）。                                                                                                                                                                                |
| 1.4        | 用户A已发起邀请，用户B接受前A解绑情侣关系 | 1. 用户A创建邀请码。<br> 2. 用户A与用户C绑定（通过其他流程）。<br> 3. 用户B输入用户A的邀请码。                                                                                             | 验证邀请码失败，提示“邀请码无效”或邀请用户已绑定。                                                                                                                                                                                                                 |

#### 3.2.2 场景2：创建并发送信任券流程

| 测试项编号 | 前提条件           | 操作步骤                                                                                                                                                                  | 预期结果                                                                                                                                                                                                     |
| :--------- | :----------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 2.1        | 用户A和B已绑定情侣 | 1. 用户A进入小程序首页，确认已绑定。<br> 2. 用户A进入“创建信任券”页面。<br> 3. 填写券名称、描述、规则和有效期（未来日期）。<br> 4. 点击“预览”按钮。<br> 5. 预览弹窗正确显示券信息。<br> 6. 在预览弹窗点击“确认创建”。 | 创建券成功，`coupons`集合中新增一条记录。<br> 记录的 `senderId` 为用户A，`receiverId` 为用户B，`coupleId` 正确关联双方。<br> 券状态为 `'created'`，有效期正确。<br> 页面提示创建成功，并返回（通常是券详情页或券包）。                                                               |
| 2.2        | 用户A和B未绑定情侣 | 1. 用户A登录小程序，但未绑定情侣。<br> 2. 用户A尝试进入“创建信任券”页面。                                                                                                      | 被阻止进入创建页面，提示“请先绑定情侣关系”。                                                                                                                                                                  |
| 2.3        | 用户A和B已绑定情侣 | 1. 用户A进入“创建信任券”页面。<br> 2. 只填写券名称，其他必填项为空或非法（如过期日期）。<br> 3. 点击“确认创建”。                                                                                               | 表单验证失败，对应输入框下方或弹窗提示错误信息（如“券描述不能为空”，“有效期必须是未来时间”）。<br> 券未被创建。                                                                                                  |
| 2.4        | 用户A已创建券，状态created | 1. 用户A进入“我的券包”，找到状态为“已创建”的券。<br> 2. 进入该券详情页。<br> 3. 点击“发送给另一半”按钮。                                                                                             | 调用云函数 `sendCoupon` 成功。<br> `coupons`集合中该券状态更新为 `'sent'`，设置 `sendTime`。<br> 后台创建新券通知记录（给用户B）。<br> 用户B收到新券通知服务消息。<br> 页面提示发送成功，券状态更新，操作按钮改变。                                         |
| 2.5        | 用户A已创建券，尝试发送已过期券 | 1. 用户A创建券并设置有效期为过去日期。<br> 2. 用户A在券详情页尝试点击“发送给另一半”。                                                                                               | 调用云函数 `sendCoupon` 失败，提示“券已过期，无法发送”。券状态不变。                                                                                                                                          |

#### 3.2.3 场景3：接收并查看信任券流程

| 测试项编号 | 前提条件                     | 操作步骤                                                                                                                                                                                                                                | 预期结果                                                                                                                                                                                                                                                              |
| :--------- | :--------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 3.1        | 用户A已向用户B发送券，用户B登录 | 1. 用户A向用户B发送券（通过后台脚本或测试场景2操作）。<br> 2. 用户B登录小程序首页，可能看到消息中心 Tab 有未读提醒。<br> 3. 用户B进入“我的券包”，在“我收到的”或“待处理”列表看到该券，状态为“待接收”。<br> 4. 用户B点击该券进入详情页。<br> 5. 详情页展示券信息，包含“确认接收”按钮。<br> 6. 用户B点击“确认接收”。 | 用户B在券包列表看到待接收券，状态正确。<br> 用户B在券详情页看到正确信息和操作按钮。<br> **点击确认接收后**: <br> - 调用云函数 `receiveCoupon` 成功。<br> - `coupons`集合中该券状态更新为 `'received'`，设置 `receiveTime`。<br> - 后台创建接收确认通知记录（给用户A）。<br> - 用户A收到接收确认服务消息（取决于是否订阅）。<br> - 用户B页面提示接收成功，券状态更新为“已接收”，操作按钮变为“申请兑现”。<br> - 在用户B的“我的券包”中，该券从“待接收”列表消失，出现在“已接收”列表。                          |
| 3.2        | 用户A已向用户B发送券，用户B在消息中心收到通知 | 1. 用户A向用户B发送券。<br> 2. 用户B收到微信服务消息通知（如“新券提醒”）。<br> 3. 用户B点击服务消息直接进入券详情页。                                                                                                      | 用户B成功跳转到该券的详情页，页面正确显示券信息和“确认接收”按钮。后续操作同 3.1.6 往后步骤。                                                                                                                                                                             |
| 3.3        | 用户A已向用户B发送券，但券已过期，用户B未接收 | 1. 用户A向用户B发送券（确保有效期已过）。<br> 2. 用户B查看该券详情页或券包列表。                                                                                                                          | 券状态可能显示“已过期”。如果状态未自动更新，查看详情时云函数应检查并更新状态。<br> 详情页不显示“确认接收”按钮或点击后提示券已过期。                                                                                                                                         |
| 3.4        | 用户A已向用户B发送券，用户B已接收，但用户A将券删除 | 1. 用户A创建券并发送给用户B。<br> 2. 用户B确认接收。<br> 3. 用户A在“我的券包”中（如果能看到已发送券且支持删除created以外的状态），尝试删除该券。<br> 4. 用户B尝试查看或操作该券。                             | 用户A尝试删除已发送/接收的券失败（通常不允许删除已互动券）。<br> 用户B尝试查看券详情应能看到信息（数据未删除，但券状态流转应受控），操作按钮应根据实际状态显示。                                                                                                                            |

#### 3.2.4 场景4：信任券核销流程 (申请与确认)

此场景涉及接收方发起“申请兑现”，发送方进行“确认兑现”或“拒绝兑现”。

| 测试项编号 | 前提条件                       | 操作步骤                                                                                                                                                                                                                                                                                                                                                             | 预期结果                                                                                                                                                                                                                                                                                                                                   |
| :--------- | :----------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 4.1        | 用户A已发送券给B，用户B已接收    | 1. 用户B进入“我的券包”，在“我收到的”列表中找到已接收券。<br> 2. 进入该券详情页，看到“申请兑现”按钮。<br> 3. 用户B点击“申请兑现”。<br> 4. 可选填写核销说明或上传图片。<br> 5. 确认提交申请。                                                                                                                                                                                             | 调用云函数 `couponRedeemAction` (action: 'requestRedeem') 成功。<br> `coupons`集合中该券状态更新为 `'pending_redeem'`，设置 `redeemApplyTime`，保存核销说明和图片fileIDs。<br> 后台创建核销申请通知记录（给用户A）。<br> 用户A收到核销申请服务消息（取决于是否订阅）。<br> 用户B页面提示申请提交成功，券状态更新，操作按钮消失或变为“等待对方确认”。<br> 该券在用户B的券包中从“已接收”列表移动到“待处理”列表。 |
| 4.2        | 用户B已申请核销，用户A收到通知   | 1. 用户B完成场景 4.1。<br> 2. 用户A收到微信服务消息通知（如“核销申请”）。<br> 3. 用户A点击通知直接进入该券详情页。<br> 4. 详情页展示券信息、核销申请说明及图片（如果上传了），看到“确认兑现”和“拒绝兑现”按钮。<br> 5. 用户A点击“确认兑现”。                                                                                                                                                                  | 用户A成功跳转到券详情页，看到正确信息和操作按钮。<br> **点击确认兑现后**: <br> - 调用云函数 `couponRedeemAction` (action: 'confirmRedeem') 成功。<br> - `coupons`集合中该券状态更新为 `'redeemed'`，设置 `redeemTime`。<br> - 后台创建核销完成通知记录（给用户B）。<br> - 用户B收到核销完成服务消息。<br> - 用户A页面提示确认成功，券状态更新，操作按钮消失。<br> 该券在用户A的券包（如果显示已发送券）和用户B的券包中都显示为“已兑现”状态。                  |
| 4.3        | 用户B已申请核销，用户A在待处理列表查看 | 1. 用户B完成场景 4.1。<br> 2. 用户A进入“我的券包”，在“我送出的”或“待处理”列表看到该券，状态为“待核销”。<br> 3. 用户A点击券进入详情页。<br> 4. 详情页展示券信息，看到“确认兑现”和“拒绝兑现”按钮。<br> 5. 用户A点击“拒绝兑现”。<br> 6. （如果需要）填写拒绝原因。<br> 7. 确认提交拒绝。                                                                                                                                                   | 用户A在券包列表看到待核销券，状态正确。<br> 用户A在券详情页看到正确信息和操作按钮。<br> **点击拒绝兑现后**: <br> - 调用云函数 `couponRedeemAction` (action: 'rejectRedeem') 成功。<br> - `coupons`集合中该券状态回退为 `'received'`，记录 `rejectTime` 和 `rejectReason`，清除核销申请相关字段。<br> - 后台创建核销拒绝通知记录（给用户B）。<br> - 用户B收到核销拒绝服务消息。<br> - 用户A页面提示操作成功，券状态更新，操作按钮消失。<br> 该券在用户A的券包中状态更新，在用户B的券包中也更新为“已接收”。                               |
| 4.4        | 用户A已发送券给B，券已过期而B尝试申请核销 | 1. 用户A发送券给B，有效期设为过去日期。<br> 2. 用户B尝试在券详情页点击“申请兑现”。                                                                                                                                        | 调用云函数 `couponRedeemAction` 失败，提示“券已过期，无法执行此操作”。券状态可能更新为已过期。                                                                                                                                                              |
| 4.5        | 用户B已申请核销，券已过期而A尝试确认核销 | 1. 用户A发送券给B，用户B接收。<br> 2. 用户B申请核销。<br> 3. 券过期。<br> 4. 用户A尝试在券详情页点击“确认兑现”。                                                                                                                              | 调用云函数 `couponRedeemAction` 失败，提示“券已过期，无法执行此操作”。券状态可能更新为已过期。                                                                                                                                                              |
| 4.6        | 用户B已申请核销，A或B解除绑定关系 | 1. 用户A发送券给B，用户B接收。<br> 2. 用户B申请核销。<br> 3. 用户A或用户B解除情侣关系。<br> 4. 用户A或用户B尝试处理或查看该券。                                                                                                                             | 解绑后，相关券记录可能被标记失效或无法操作。用户尝试操作时应返回权限不足或券无效的提示。                                                                                                                                                                                |

#### 3.2.5 场景5：消息通知接收与查看流程

| 测试项编号 | 前提条件                                       | 操作步骤                                                                                                                                                                                                                              | 预期结果                                                                                                                                                                                                                                                                                                                                                        |
| :--------- | :--------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 5.1        | 已有未读消息（例如新券、核销申请）的用户登录小程序 | 1. 用户登录小程序。<br> 2. 首页 TabBar 的消息中心图标显示未读气泡/数量。<br> 3. 用户进入“消息通知”页面。<br> 4. "全部"标签显示消息列表，未读消息标示为未读（如小红点）。<br> 5. 未读计数在页面头部和 TabBar 中显示正确。 | 消息中心 TabBar 显示未读数量（取决于真实消息数量）。<br> 通知列表页正确加载并显示所有消息。<br> 未读消息在列表中有明确标识。<br> 页面头部正确显示总消息数和未读数。                                                                                                                                                                                               |
| 5.2        | 用户查看通知详情并标记已读                       | 1. 用户在消息通知列表点击一条未读消息。<br> 2. 页面跳转到该通知关联的券详情页（如果关联了券）。<br> 3. 返回消息通知列表。                                                                                                                             | 用户成功跳转到关联详情页。<br> 返回列表后，被点击的消息状态更新为“已读”，列表中的未读标识消失。<br> 页面头部的未读计数减少1。<br> （可选）后台日志记录该通知被标记为已读。                                                                                                                                                                                   |
| 5.3        | 用户批量标记已读                               | 1. 用户在消息通知列表中有多个未读消息。<br> 2. 用户点击页面头部的“全部已读”按钮。                                                                                                                                                      | 调用云函数 `markAllNotificationsAsRead` 成功。<br> 列表中的所有未读消息状态更新为“已读”，未读标识消失。<br> 页面头部的未读计数变为0。<br> （可选）后台日志记录批量标记已读操作。                                                                                                                                                                                         |
| 5.4        | 用户删除通知                                   | 1. 用户在消息通知列表中找到要删除的消息。<br> 2. 调用删除操作（如右滑、点击删除按钮，取决于UI实现）。<br> 3. 确认删除。                                                                                                                                 | 调用云函数 `deleteNotification` 成功。<br> 该消息从列表中移除。<br> 页面总消息数和未读数（如果该消息未读）更新。<br> （可选）后台日志记录删除操作。                                                                                                                                                                                               |
| 5.5        | 用户清空所有通知                               | 1. 用户在消息通知列表中有消息。<br> 2. 用户点击页面头部的“清空”按钮。<br> 3. 确认清空。                                                                                                                                                    | 调用云函数 `clearAllNotifications` 成功。<br> 消息列表清空，总消息数和未读数变为0。<br> （可选）后台日志记录清空操作。                                                                                                                                                                                             |

#### 3.2.6 场景6：券列表筛选与管理流程

| 测试项编号 | 前提条件               | 操作步骤                                                                                                                                                                                                                             | 预期结果                                                                                                                                                                                              |
| :--------- | :--------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 6.1        | 用户有不同状态的券     | 1. 用户进入“我的券包”页面。<br> 2. 默认显示“全部”券列表。<br> 3. 点击筛选标签（如“收到的”）。<br> 4. 点击其他筛选标签（如“送出的”、“待处理”等）。 | “全部”列表显示用户作为发送方或接收方的所有券（除created状态）。<br> 点击不同筛选标签后，列表内容正确切换，只显示符合筛选条件的券。<br> 各个标签旁的计数（如果已实现）正确显示对应状态的券数量。                                                               |
| 6.2        | 用户券数量较多         | 1. 用户的券数量超过每页显示数（假设10条）。<br> 2. 在券列表底部向上滑动。                                                                                                                                                              | 触发加载更多。<br> 调用云函数 `getCouponList` 传入下一页参数。<br> 新加载的券追加到当前列表底部。<br> 如果还有更多数据，继续向上滑动可再次加载。                                                                                                    |
| 6.3        | 用户在特定筛选下无券 | 1. 用户切换到某个筛选标签（如“待处理”）。<br> 2. 该筛选条件下用户没有任何券。                                                                                                                                                            | 列表显示空状态提示（例如“暂无待处理券”），提示文案符合当前筛选条件。<br> 可选显示引导操作（如“创建第一张券”）。                                                                                                              |
| 6.4        | 用户在券包列表操作券 | 1. 用户在“我的券包”列表中找到一张处于待接收状态的券。<br> 2. 点击列表项上的“确认接收”按钮（如果列表项直接支持操作）。                                                                                                                            | 触发对应的券操作事件。<br> 调用云函数 `couponAction` 或 `receiveCoupon` 成功。<br> 券状态更新，在列表中的状态显示和所属列表发生变化。<br> 弹出操作成功提示。                                                                                             |
| 6.5        | 用户在券包删除券       | 1. 用户A进入我的券包，找到状态为“已创建”的券。<br> 2. 在列表项上进行删除操作。（取决于UI实现，可能在详情页或滑动删除）。<br> 3. 确认删除。                                                                                                                      | 调用云函数 `deleteCoupon` 成功。<br> 该券从列表中移除。<br> 页面总券数和相关筛选标签计数更新。                                                                                                                                    |

#### 3.2.7 场景7：异常处理流程 (网络错误、操作失败等)

| 测试项编号 | 前提条件                 | 操作步骤                                                                                                                                                                                                           | 预期结果                                                                                                                                                       |
| :--------- | :----------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 7.1        | 前端请求云函数时网络异常 | 1. 在某个前端页面触发需要调用云函数的行为（如登录、加载券列表）。<br> 2. 模拟网络断开。<br> 3. 触发云函数调用。                                                                                                                   | 前端调用云函数失败，`wx.cloud.callFunction` 的 `fail` 回调被触发。<br> API封装层捕获错误，弹出网络错误提示（如“网络请求失败”或“加载失败，请重试”）。<br> 页面可能显示加载失败状态或保留上一次加载成功的数据。 |
| 7.2        | 云函数执行失败           | 1. 触发已部署的云函数，但在云函数内部模拟抛出错误或返回失败结果（如数据库操作失败、参数校验失败）。<br> 2. 前端调用该云函数。                                                                                                                   | 前端调用云函数失败，`wx.cloud.callFunction` 的 `success` 回调中 `res.result.success` 为 `false` 或 `fail` 回调被触发。<br> API封装层捕获错误，弹出失败提示（如“操作失败”或云函数返回的特定错误信息）。 |
| 7.3        | 用户尝试操作已过期券     | 1. 用户A发送券给B，券已过期。<br> 2. 用户B尝试在券详情页点击“申请兑现”。<br> 3. 用户A尝试在券详情页点击“确认兑现”（如果券状态仍为待核销）。                                                                                                   | 调用相关云函数失败，返回业务错误码和信息（如 `COUPON_EXPIRED`）。<br> 前端弹出提示“券已过期，无法执行此操作”。<br> 页面按钮状态可能更新为禁用。                                                                           |
| 7.4        | 用户尝试越权操作券     | 1. 用户B尝试修改用户A创建但未发送的券。<br> 2. 用户A尝试接收用户B发送给C的券。<br> 3. 用户B尝试确认用户A发送给B的待核销券。                                                                                                                       | 调用相关云函数失败，返回错误码和信息（如 `NO_PERMISSION`）。<br> 前端弹出提示“没有权限操作此券”或类似信息。                                                                                                            |
| 7.5        | 绑定关系不一致         | 1. 用户A和B已绑定。<br> 2. 后台手动修改用户A的 `partner_openid` 指向用户C，但用户B的 `partner_openid` 仍指向用户A（数据不一致）。<br> 3. 用户A或用户B尝试执行情侣相关操作（如创建券、发送券）。                                                          | 情侣相关云函数检查绑定关系失败，返回错误（如 `INVALID_RELATIONSHIP` 或 `NO_PARTNER`）。<br> 前端提示错误，可能引导用户重新绑定或联系客服。                                                                                    |
| 7.6        | 删除券时关联其他数据   | 1. 删除一张券。<br> 2. 检查关联的通知记录是否被清理或失效（取决于产品设计）。                                                                                                                                                           | （取决于是否实现了同步清理）删除券后，关联该券的通知记录可能仍然存在但点击无法跳转，或者在后台定时清理任务中被处理。更好的做法是删除券时同步清理关联通知。                                                                              |

## 4. 测试总结报告模板

本模板旨在结构化记录小程序集成与测试的成果，发现的问题，以及最终的测试结论。

**情侣信任券小程序集成与测试报告**

**文档版本：** 1.0
**测试日期：** YYYY年MM月DD日

---

### 1. 引言

*   **测试目的：** 验证情侣信任券小程序前端、后端云函数、公共组件和配置文件整合后的功能完整性、系统稳定性、用户体验及各项业务逻辑的正确性，确保符合产品规格说明书要求，达到可上线标准。
*   **测试范围：** 本次测试覆盖小程序的以下主要功能模块：用户登录与情侣绑定、信任券创建与管理（发送、接收、详情）、信任券核销（申请、确认、拒绝）、消息通知展示与处理，以及相关的用户界面、交互逻辑和后端数据处理。测试环境为微信开发者工具及云开发测试环境。
*   **参照文档：**
    *   《“情侣信任券/信用券”微信小程序规格说明书》 vX.X
    *   已提交的前端代码模块（任务hdmcm, EKc9s, D4AMI, bwJ8F, MjH4d, UdLaW）
    *   已提交的后端云函数代码（任务4UT3j, INEYX, AG7bF, 6KPjm）
    *   全局配置文件（任务huXn8 的 `app.json`, `project.config.json`）
    *   可复用的UI组件和公共JS工具函数（任务mllSP的成果）
    *   《情侣信任券小程序集成与测试指南》（本文）

---

### 2. 测试环境与配置

*   **微信开发者工具版本：** [填写使用的开发者工具版本号]
*   **基础库版本：** [填写测试使用的小程序基础库版本号]
*   **设备型号及系统版本：** [填写测试使用的主要设备型号及操作系统版本，例如：iPhone 13 / iOS 15.5, 华为 P40 / Android 12]
*   **网络环境：** [填写测试网络环境，例如：Wifi, 4G, 模拟断网]
*   **云开发环境ID：** [填写测试所用的云开发环境ID]
*   **小程序 AppID：** [填写测试所用的小程序 AppID]
*   **测试账号：**
    *   测试用户A的OpenID及昵称：[填写]
    *   测试用户B的OpenID及昵称：[填写]
*   **消息模板配置：** [确认已在公众号后台配置并获取所有必要的订阅消息模板ID]

---

### 3. 单元测试执行记录与结果

**前端JS工具函数单元测试**

| 测试模块/文件 | 测试项名称/描述 | 测试结果 (通过/失败) | 详细说明/异常情况                                     |
| :------------ | :-------------- | :------------------- | :---------------------------------------------------- |
| `validator.js`| `validateRequired` 测试 | [通过/失败]          | [填写遇到的问题，如：空字符串带有空格时判断错误]        |
|               | `validateEmail` 测试    | [通过/失败]          |                                                       |
|               | `validatePhone` 测试    | [通过/失败]          |                                                       |
|               | `validateLength` 测试   | [通过/失败]          |                                                       |
| `dateUtil.js` | `formatDate` 测试       | [通过/失败]          |                                                       |
|               | `timeAgo` 测试          | [通过/失败]          | [填写遇到的问题，如：时间差边界值处理不准确]            |
| `userUtil.js` | `setUserInfo` & `getUserInfo` | [通过/失败]          |                                                       |
| `api.js`      | `cloudRequest` 成功响应 | [通过/失败]          | [填写遇到的问题，如：Loading 状态未正确隐藏]          |
|               | `cloudRequest` 失败响应 | [通过/失败]          | [填写遇到的问题，如：错误提示信息不明确]              |
| ... 其他前端JS单元测试 |                 |                      |                                                       |

**后端云函数单元测试**

| 云函数名称                   | 测试项名称/描述         | 测试结果 (通过/失败) | 详细说明/异常情况                                                                                                     |
| :--------------------------- | :---------------------- | :------------------- | :-------------------------------------------------------------------------------------------------------------------- |
| `login`                      | 新用户注册              | [通过/失败]          | [填写遇到的问题，如：新用户首次登录时，用户信息未完全保存]                                                            |
|                              | 已存在用户登录          | [通过/失败]          | [填写遇到的问题，如：更新时间字段未刷新]                                                                                |
| `generateInvitationCode`     | 生成邀请码              | [通过/失败]          | [填写遇到的问题，如：邀请码未按预期长度或字符集生成]                                                                |
|                              | 已绑定用户尝试生成邀请码 | [通过/失败]          |                                                                                                                       |
| `bindWithInvitationCode`     | 正常情侣绑定流程        | [通过/失败]          | [填写遇到的问题，如：事务回滚失败，导致数据不一致]                                                                    |
|                              | 过期邀请码绑定          | [通过/失败]          |                                                                                                                       |
|                              | 接受方已绑定尝试绑定他人 | [通过/失败]          |                                                                                                                       |
| `createCoupon`               | 创建券 (合法输入)       | [通过/失败]          | [填写遇到的问题，如：创建券时，接收方或coupleId字段未正确设置]                                                      |
|                              | 创建券 (非法输入)       | [通过/失败]          |                                                                                                                       |
| `sendCoupon`                 | 发送已创建券            | [通过/失败]          | [填写遇到的问题，如：发送后券状态未更新，或通知记录未创建]                                                            |
|                              | 发送非创建状态券        | [通过/失败]          |                                                                                                                       |
| `receiveCoupon`              | 接收已发送券            | [通过/失败]          | [填写遇到的问题，如：接收后通知记录发送给了错误的用户]                                                                |
|                              | 接收非已发送状态券      | [通过/失败]          |                                                                                                                       |
| `couponRedeemAction`         | 申请核销                | [通过/失败]          | [填写遇到的问题，如：核销说明或截图 fileIDs 未保存]                                                                   |
|                              | 确认核销                | [通过/失败]          | [填写遇到的问题，如：核销完成后券状态仍为 pending_redeem]                                                             |
|                              | 拒绝核销                | [通过/失败]          | [填写遇到的问题，如：拒绝后券状态未回退到 received]                                                                 |
| `autoUpdateCouponToExpired`  | 定时检查并更新过期券    | [通过/失败]          | [填写遇到的问题，如：过期券状态未被更新，或批量更新失败]                                                              |
| `unbindCouple`               | 正常解除绑定            | [通过/失败]          | [填写遇到的问题，如：未清除双方的 partner_openid，导致数据不一致]                                                       |
| ... 其他云函数单元测试       |                         |                      |                                                                                                                       |

---

### 4. 集成测试执行记录与结果

| 场景编号及名称                     | 测试项编号 | 前置条件准备是否就绪 | 操作步骤 (记录实际执行过程)                                                                                                                                                                                                                | 预期结果是否符合 | 实际结果及截图/日志 (<details><summary>点击展开</summary>截图/日志链接)</details>)                                                                                                                                                                   | 缺陷/问题编号 (如果发现) |
| :--------------------------------- | :--------- | :----------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------- |
| 1. 新用户注册与情侣绑定           | 1.1        | [是/否]            | [记录用户A、用户B具体操作步骤，如：用户A授权登录 -> 进入绑定页 -> 生成邀请码 ABCD12 -> 用户B授权登录 -> 进入绑定页 -> 输入 ABCD12 -> 确认加入]                                                                                               | [符合/不符]      | [记录实际界面的显示、数据变化、日志情况。例如：用户A和B均显示绑定成功，首页显示对方信息。云数据库users表中双方partner_openid互指，userA的邀请码已清除。]                                                                                                      | [无 或 BUG-001]          |
|                                    | 1.2        | [是/否]            | [记录用户B尝试输入过期邀请码过程]                                                                                                                                                                                                          | [符合/不符]      | [记录实际提示信息，例如：输入框下方提示“邀请码已过期”]                                                                                                                                                                                        | [无]                     |
| 2. 创建并发送信任券流程           | 2.1        | [是/否]            | [记录用户A填写表单 -> 预览 -> 确认创建过程]                                                                                                                                                                                                  | [符合/不符]      | [记录券列表和详情页显示状态是否正确。数据库coupons表是否新增记录，字段是否正确。例如：创建成功后页面跳转到详情页，状态显示“已创建”。coupons表新增了一条senderId=userA, receiverId=userB, status=created的记录]                                | [无 或 BUG-002]          |
|                                    | 2.4        | [是/否]            | [ 기록 User A finds the created coupon in My Coupons -> enters detail page -> clicks "Send to Partner"]                                                                                                                                     | [符合/不符]      | [record whether the coupon status is updated to 'sent' in DB, whether a notification is created for User B, whether User B receives a subscription message, whether the page shows success and updates buttons. For example: Status updated to 'sent', User B receives a notification.] | [없음 or BUG-003]        |
| 3. Recept and Viewing Trust Coupon Process | 3.1        | [Yes/No]           | [record User B entering My Coupons -> finding the coupon -> entering detail page -> clicking "Confirm Receipt"]                                                                                                              | [Conforms/Does not conform] | [record whether the status is updated to 'received' in DB, whether a notification is created for User A, whether User A receives a subscription message, whether User B page shows success and updates buttons. For example: Status updated to 'received', coupon moves to received list.] | [None or BUG-004]        |
| 4. Trust Coupon Redemption Process (Apply and Confirm) | 4.1        | [Yes/No]           | [record User B enters detail page of received coupon -> clicks "Apply for Redemption" -> submits application (with/without note/images)]                                                                                     | [Conforms/Does not conform] | [record whether the status is updated to 'pending_redeem' in DB, note/images are saved, notification is created for User A, User A receives subscription message, User B page updates. For example: Status updated to 'pending_redeem', User A receives a redemption request notification.] | [None or BUG-005]        |
|                                    | 4.2        | [Yes/No]           | [record User A clicks on the redemption request notification -> enters detail page -> clicks "Confirm Redemption"]                                                                                                                       | [Conforms/Does not conform] | [record whether status is updated to 'redeemed' in DB, notification created for User B, User B receives result notification, User A page updates. For example: Status updated to 'redeemed', both users see coupon as redeemed.]                               | [None or BUG-006]        |
|                                    | 4.3        | [Yes/No]           | [record User A clicks on the redemption request notification -> enters detail page -> clicks "Reject Redemption" -> (if needed) enters reason -> confirms rejection]                                                                    | [Conforms/Does not conform] | [record whether status is updated back to 'received' in DB, reject reason saved, notification created for User B, User B receives rejection notification, User A page updates. For example: Status reverted to 'received', User B notified of rejection.] | [None or BUG-007]        |
| 5. Message Notification Reception and Viewing | 5.1        | [Yes/No]           | [record user logging in with unread messages -> observing TabBar badge -> entering Notification Center]                                                                                                                        | [Conforms/Does not conform] | [record if TabBar badge is correct, list displays all/unread messages correctly, header counts are correct. For example: TabBar shows badge 3, Notification Center displays 3 unread messages.]                                                 | [None or BUG-008]        |
|                                    | 5.2        | [Yes/No]           | [record clicking an unread message in the list -> navigating to detail page -> returning to list]                                                                                                                                          | [Conforms/Does not conform] | [record if message is marked as read in list, unread count decreases, status is updated in DB. For example: Message is marked read, unread count updates.]                                                                      | [None]                   |
| 6. Coupon List Filtering and Management | 6.1        | [Yes/No]           | [record switching between "All", "Received", "Sent", "Pending" tabs]                                                                                                                                                                     | [Conforms/Does not conform] | [record if the list content updates correctly for each filter, if counts (if displayed) are accurate. For example: Switching to "Received" only displays coupons received by the user.]                                                            | [None]                   |
|                                    | 6.2        | [Yes/No]           | [record scrolling to the bottom of a list with more than one page of coupons]                                                                                                                                                            | [Conforms/Does not conform] | [record if "Load More" appears/is clicked, new items are appended to the list, loading indicator is shown/hidden. For example: More items load successfully when scrolling to the bottom.]                                                      | [None or BUG-009]        |
| 7. Exception Handling Flow          | 7.1        | [模拟网络异常]         | [记录触发云函数调用时的操作，例如：尝试登录]                                                                                                                                                                                           | [符合/不符]      | [记录前端弹出的错误提示信息，是否与预期一致。例如：弹出“网络请求失败，请重试”]                                                                                                                                                           | [无]                     |
|                                    | 7.2        | [模拟云函数失败]       | [记录触发云函数调用时的操作，例如：尝试创建券]                                                                                                                                                                                         | [符合/不符]      | [记录前端弹出的错误提示信息，是否显示云函数返回的特定错误信息。例如：弹出“创建失败，券名称长度不符合要求”]                                                                                                                             | [无]                     |
|                                    | 7.3        | [券已过期]           | [记录用户尝试操作过期券的过程]                                                                                                                                                                                                           | [符合/不符]      | [记录前端弹出的提示信息，是否阻止了操作。例如：弹出“该券已过期，无法继续操作”]                                                                                                                                                            | [无]                     |
|                                    | 7.4        | [用户越权]           | [记录用户尝试越权操作的过程，例如：用户B修改用户A的券]                                                                                                                                                                                       | [符合/不符]      | [记录前端弹出的提示信息，是否显示权限不足。例如：弹出“没有权限操作此券”]                                                                                                                                                               | [无]                     |

---

### 5. 发现的缺陷/问题列表

使用以下表格记录本次测试中发现的所有缺陷或问题。

| 缺陷ID   | 缺陷描述                                                                                                                               | 发现时间         | 发现人   | 严重级别 (高/中/低) | 复现步骤                                                                                                          | 当前状态 (待修复/修复中/已修复待验证/关闭) | 备注                                                                                                                               |
| :------- | :------------------------------------------------------------------------------------------------------------------------------------- | :--------------- | :------- | :------------------ | :---------------------------------------------------------------------------------------------------------------- | :--------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------- |
| BUG-001  | 情侣绑定成功后，双方用户记录中 `partner_openid` 虽已互指，但各自的邀请码字段未清除，导致再次检查绑定状态时误判仍有未完成的邀请。                                     | YYYY-MM-DD HH:mm | [姓名]   | 中                  | 1. 用户A创建邀请码。 2. 用户B接受邀请并绑定成功。 3. 用户A的 `invitation_code` 字段未变为 `null`。                                                 | [选择状态]                               |                                                                                                                    |
| BUG-002  | 券详情页面加载图片时，若图片 URL 无效或加载失败，会显示空白，但无错误提示或占位图。                                                                    | YYYY-MM-DD HH:mm | [姓名]   | 低                  | 1. 在数据库中修改某券的核销截图 `redeemImages` 字段为一个无效的 URL。 2. 在券详情页查看该券。                                                         | [选择状态]                               | 可以考虑使用 `<image>` 组件的 `binderror` 事件处理。                                                                               |
| BUG-003  | 在券列表页使用侧滑删除功能时，如果快速滑动删除多条消息，偶现列表更新异常，部分消息未移除或位置错乱。（如果此功能已实现）                                         | YYYY-MM-DD HH:mm | [姓名]   | 中                  | 1. 加载包含多条消息的列表。 2. 快速对多条消息执行侧滑删除操作。                                                                       | [选择状态]                               | 可能是前端列表数据更新与 UI 渲染不同步问题。                                                                                         |
| BUG-004  | 后台定时任务 `autoUpdateCouponToExpired` 在更新券状态为过期时，如果批量更新操作失败（例如网络波动），可能导致少量过期券状态未被更新。                               | YYYY-MM-DD HH:mm | [姓名]   | 低                  | 1. 后台准备一批即将过期的券。 2. 在定时任务执行的关键时刻模拟数据库写入失败。 3. 检查数据库中过期券的状态是否全部更新为 `expired`。                                           | [选择状态]                               | 云函数中应增加重试机制或批量操作回调处理逻辑。                                                                                       |
| BUG-005  | 消息通知列表中，点击已读消息后，虽标记为已读，但页面未读计数立刻更新，列表中的“未读圆点”未及时同步消失，需要下拉刷新才能正确显示。                                     | YYYY-MM-DD HH:mm | [姓名]   | 低                  | 1. 打开消息通知页面，存在未读消息。 2. 点击一条未读消息，跳转后返回。                                                                            | [选择状态]                               | 前端数据更新后未强制刷新列表视图。                                                                                                 |
| BUG-006  | 解除情侣绑定后，原共同创建/拥有的信任券数据未被逻辑删除（如标记为无效）或转移归属，可能导致解绑后数据混乱。                                                         | YYYY-MM-DD HH:mm | [姓名]   | 高                  | 1. 用户A和B创建/接收一些券。 2. 用户A解除绑定。 3. 用户A或B查看自己券包中原属于共同情侣关系的券。                                                    | [选择状态]                               | 需要在解绑云函数中增加处理历史券的逻辑，或实施后台定时清理任务。                                                                             |
| BUG-007  | 创建信任券或问题反馈时，如果输入内容包含换行符，在前端显示时换行符未被正确保留，显示为一整行文本。                                                               | YYYY-MM-DD HH:mm | [姓名]   | 低                  | 1. 在券描述或问题描述输入框中输入多行文本（包含换行）。 2. 提交并查看详情或反馈记录。                                                               | [选择状态]                               | 前端显示时可以使用 `<text selectable space="pre">` 或类似 WXML 组件/样式处理换行。                                                        |
| BUG-008  | 在慢网络环境下，用户首次进入小程序时，加载状态提示过快消失或未出现，导致用户以为应用卡顿。                                                                       | YYYY-MM-DD HH:mm | [姓名]   | 低                  | 1. 模拟慢速网络。 2. 用户首次进入小程序。                                                                                        | [选择状态]                               | 加载指示器组件的最小显示时间(`minDuration`) 可以帮助优化此类体验。                                                                     |

---

### 6. 测试覆盖率分析 (可选)

本节可分析当前测试对功能模块、代码路径的覆盖程度。

*   **功能覆盖率：**
    *   已覆盖核心用户流程比例：约 [百分비율]% (基于测试场景数量与总场景预估)
    *   未覆盖但重要的功能点：[例如：情侣关系解除时的通知发送、券编辑功能（如果未实现）、更多边界条件验证等]
*   **代码覆盖率 (需工具支持)：** [如果集成了代码覆盖率工具，在此填写覆盖率百分比和关键模块覆盖详情]

---

### 7. 总结与建议

*   **测试总结：** 本轮集成与测试基本验证了情侣信任券小程序的MVP核心功能整合情况。用户登录、基础情侣绑定、券的创建、发送、接收以及基本的核销流程主体功能通过测试，各模块间初步协同工作正常。部分核心依赖的云函数经过单元测试，功能符合预期。
*   **发现的问题：** 本次测试共发现 [缺陷总数] 个缺陷，其中 [高/中/低对应数量] 个高/中/低级别问题。主要问题集中在 [填写问题类型概述，例如：数据一致性、异常流程处理、用户体验细节] 等方面。具体问题详见缺陷列表。
*   **测试建议：**
    1.  优先修复高/中级别缺陷，重点关注数据一致性问题（特别是解绑和核销等关键流程）。
    2.  完善前端异常处理逻辑，优化网络异常和操作失败时的用户提示和页面状态展示。
    3.  加强边界条件和异常流程的测试覆盖，例如：重复操作、并发请求、不完整数据状态等。
    4.  对消息通知的关键触发点进行端到端验证，包括服务消息的送达。（需配置真实的订阅消息模板）
    5.  考虑引入自动化测试工具，提高测试效率和覆盖度。
    6.  进行真实的设备兼容性测试和性能测试。
    7.  审校 UI 组件和工具函数的详细文档，确保易用性和可维护性。

---

**测试报告结束**