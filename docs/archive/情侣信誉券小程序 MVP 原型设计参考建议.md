# 情侣信誉券小程序 MVP 原型设计参考建议

本文档基于提供的信誉券小程序需求文档、用户旅程图、业务流程图、功能结构思维导图和关键交互时序图，为MVP阶段的原型设计提供详细参考与建议。目标是建立一个简洁、易用且能高效传递信誉券核心价值的产品原型。

## 1. 关键页面线框图标注

以下为MVP阶段关键页面的线框图参考与主要构成元素、大致布局和信息层级标注。所有图示均采用SVG格式生成，确保完整显示。

### 1.1 首页/券列表页

作为用户进入小程序后的主要 landing page，应直观展示当前所有信誉券的状态。

#### 构成元素与布局：
- **顶部导航/标题：** 小程序名称，可能包含伴侣头像和名称。
- **信息概览区：** 可选，展示当前待使用/已核销券数量等简要统计。
- **券列表：**
    - **列表项：** 每项代表一张信誉券。
    - **关键信息：** 券名称、有效期限（如有）、状态（待使用、已使用/已核销、已过期）、创建人（对方/自己）。
    - **视觉区分：** 可通过颜色、图标或字体样式区分券的状态和类型。
- **底部导航栏：** 核心功能的快速入口，如首页、创建券、历史记录、伴侣管理。

#### SVG 线框图参考:

```svg
<svg width="300" height="500" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <rect x="10" y="10" width="280" height="40" fill="#ddd"/>
  <text x="20" y="35" font-family="Arial" font-size="16" fill="#555">顶部导航/伴侣信息</text>

  <rect x="10" y="60" width="280" height="60" fill="#ddd"/>
  <text x="20" y="90" font-family="Arial" font-size="14" fill="#555">信息概览区 (可选)</text>

  <rect x="10" y="140" width="280" height="80" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <text x="20" y="165" font-family="Arial" font-size="16" fill="#333">券名称 1</text>
  <text x="20" y="185" font-family="Arial" font-size="12" fill="#666">状态：待使用 | 创建人：对方</text>
  <text x="20" y="200" font-family="Arial" font-size="12" fill="#666">有效期：xxxx-xx-xx</text>

  <rect x="10" y="230" width="280" height="80" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <text x="20" y="255" font-family="Arial" font-size="16" fill="#333">券名称 2</text>
  <text x="20" y="275" font-family="Arial" font-size="12" fill="#666">状态：已使用/核销 | 创建人：自己</text>
  <text x="20" y="290" font-family="Arial" font-size="12" fill="#666">有效期：不限</text>

  <rect x="0" y="450" width="300" height="50" fill="#ddd"/>
  <text x="30" y="475" font-family="Arial" font-size="14" fill="#555">首页</text>
  <text x="100" y="475" font-family="Arial" font-size="14" fill="#555">创建券</text>
  <text x="170" y="475" font-family="Arial" font-size="14" fill="#555">历史</text>
  <text x="240" y="475" font-family="Arial" font-size="14" fill="#555">伴侣</text>
</svg>
```

### 1.2 创建券页面

用户生成新信誉券的入口。应提供清晰的表单项。

#### 构成元素与布局：
- **顶部导航/标题：** “创建信誉券”。
- **表单区：**
    - **券名称输入框：** 必填项。
    - **描述文本域：** 非必填，详细说明券内容。
    - **有效期选择：** 可选，日期选择器或“无期限”选项。
- **所属伴侣选择：** MVP阶段仅支持已绑定伴侣。
- **创建按钮：** 提交表单，生成信誉券。

#### SVG 线框图参考:

```svg
<svg width="300" height="500" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <rect x="10" y="10" width="280" height="40" fill="#ddd"/>
  <text x="20" y="35" font-family="Arial" font-size="16" fill="#555">创建信誉券</text>

  <text x="20" y="80" font-family="Arial" font-size="14" fill="#555">券名称:</text>
  <rect x="20" y="95" width="260" height="30" fill="#fff" stroke="#ccc" stroke-width="1"/>

  <text x="20" y="140" font-family="Arial" font-size="14" fill="#555">券描述 (可选):</text>
  <rect x="20" y="155" width="260" height="80" fill="#fff" stroke="#ccc" stroke-width="1"/>

  <text x="20" y="250" font-family="Arial" font-size="14" fill="#555">有效期:</text>
  <rect x="20" y="265" width="260" height="30" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <text x="30" y="285" font-family="Arial" font-size="12" fill="#666">选择日期或无期限</text>

  <text x="20" y="310" font-family="Arial" font-size="14" fill="#555">所属伴侣:</text>
  <rect x="20" y="325" width="260" height="30" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <text x="30" y="345" font-family="Arial" font-size="12" fill="#666">已绑定伴侣名称</text>


  <rect x="20" y="380" width="260" height="40" fill="#007bff"/>
  <text x="110" y="405" font-family="Arial" font-size="18" fill="#fff">创建</text>

   <rect x="0" y="450" width="300" height="50" fill="#ddd"/>
  <text x="30" y="475" font-family="Arial" font-size="14" fill="#555">首页</text>
  <text x="100" y="475" font-family="Arial" font-size="14" fill="#555">创建券</text>
  <text x="170" y="475" font-family="Arial" font-size="14" fill="#555">历史</text>
  <text x="240" y="475" font-family="Arial" font-size="14" fill="#555">伴侣</text>
</svg>
```

### 1.3 券详情页

展示单张券的详细信息，并提供核心操作（核销）。

#### 构成元素与布局：
- **顶部导航/标题：** 券名称。
- **券信息展示区：**
    - **券名称：** prominently displayed.
    - **描述：** 详细说明。
    - **创建人/获得人：** 显示关系。
    - **创建日期/获得日期：**
    - **有效期限：**
    - **状态：** 当前状态（待使用、已核销、已过期）。
- **操作按钮区：**
    - **“立即核销”按钮：** 仅在“待使用”状态下显示。点击后需要确认。
    - **删除按钮 (可选，根据需求决定是否MVP包含):**
- **核销确认弹窗：** 点击“立即核销”后弹出，提示用户确认。

#### SVG 线框图参考:

```svg
<svg width="300" height="500" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <rect x="10" y="10" width="280" height="40" fill="#ddd"/>
  <text x="20" y="35" font-family="Arial" font-size="16" fill="#555">券名称 1</text>

  <rect x="10" y="60" width="280" height="200" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <text x="20" y="85" font-family="Arial" font-size="18" fill="#333">券名称 1</text>
  <text x="20" y="110" font-family="Arial" font-size="14" fill="#555">描述:</text>
  <text x="20" y="130" font-family="Arial" font-size="12" fill="#666">这里是券的详细描述内容。</text>
  <text x="20" y="160" font-family="Arial" font-size="14" fill="#555">状态: 待使用</text>
  <text x="20" y="180" font-family="Arial" font-size="14" fill="#555">创建人: 对方</text>
  <text x="20" y="200" font-family="Arial" font-size="14" fill="#555">有效期: xxxx-xx-xx</text>

  <rect x="20" y="280" width="260" height="40" fill="#28a745"/>
  <text x="105" y="305" font-family="Arial" font-size="18" fill="#fff">立即核销</text>

  <!-- 核销确认弹窗 (逻辑上在此位置) -->
  <!-- <rect x="50" y="150" width="200" height="100" fill="#fff" stroke="#333" stroke-width="1"/>
  <text x="60" y="180" font-family="Arial" font-size="14" fill="#333">确定要核销此券吗?</text>
  <rect x="60" y="200" width="80" height="30" fill="#ccc"/>
  <text x="70" y="220" font-family="Arial" font-size="12" fill="#333">取消</text>
  <rect x="160" y="200" width="80" height="30" fill="#28a745"/>
  <text x="170" y="220" font-family="Arial" font-size="12" fill="#fff">确认</text> -->

     <rect x="0" y="450" width="300" height="50" fill="#ddd"/>
  <text x="30" y="475" font-family="Arial" font-size="14" fill="#555">首页</text>
  <text x="100" y="475" font-family="Arial" font-size="14" fill="#555">创建券</text>
  <text x="170" y="475" font-family="Arial" font-size="14" fill="#555">历史</text>
  <text x="240" y="475" font-family="Arial" font-size="14" fill="#555">伴侣</text>
</svg>
```

### 1.4 伴侣管理页

处理伴侣绑定、展示伴侣信息。

#### 构成元素与布局：
- **顶部导航/标题：** “伴侣管理”。
- **伴侣信息区：**
    - **当前伴侣头像/名称：** 如已绑定。
    - **解绑按钮。**
- **邀请/绑定区：** 如未绑定。
    - **生成邀请码按钮：** 当前用户生成一个唯一码供伴侣使用。
    - **输入邀请码输入框：** 输入伴侣提供的邀请码进行绑定。
    - **确认绑定按钮：** 使用邀请码后进行绑定。

#### SVG 线框图参考:

```svg
<svg width="300" height="500" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <rect x="10" y="10" width="280" height="40" fill="#ddd"/>
  <text x="20" y="35" font-family="Arial" font-size="16" fill="#555">伴侣管理</text>

  <!-- 已绑定状态 -->
  <rect x="10" y="60" width="280" height="80" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <rect x="20" y="70" width="60" height="60" fill="#ddd"/>
  <text x="90" y="95" font-family="Arial" font-size="16" fill="#333">伴侣名称</text>
  <text x="90" y="115" font-family="Arial" font-size="12" fill="#666">已绑定</text>
  <rect x="200" y="85" width="70" height="30" fill="#dc3545"/>
  <text x="210" y="105" font-family="Arial" font-size="14" fill="#fff">解绑</text>

  <!-- 未绑定状态 (展示其一) -->
  <!-- 生成邀请码 -->
  <!-- <rect x="10" y="150" width="280" height="80" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <text x="20" y="170" font-family="Arial" font-size="14" fill="#555">我的邀请码:</text>
  <text x="20" y="195" font-family="Arial" font-size="18" fill="#007bff">ABCDEF</text>
  <text x="20" y="215" font-family="Arial" font-size="12" fill="#666">分享给伴侣</text> -->

  <!-- 输入邀请码 -->
  <rect x="10" y="150" width="280" height="120" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <text x="20" y="170" font-family="Arial" font-size="14" fill="#555">输入伴侣提供的邀请码:</text>
  <rect x="20" y="185" width="260" height="30" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <rect x="20" y="230" width="260" height="40" fill="#007bff"/>
  <text x="110" y="255" font-family="Arial" font-size="18" fill="#fff">绑定</text>


     <rect x="0" y="450" width="300" height="50" fill="#ddd"/>
  <text x="30" y="475" font-family="Arial" font-size="14" fill="#555">首页</text>
  <text x="100" y="475" font-family="Arial" font-size="14" fill="#555">创建券</text>
  <text x="170" y="475" font-family="Arial" font-size="14" fill="#555">历史</text>
  <text x="240" y="475" font-family="Arial" font-size="14" fill="#555">伴侣</text>
</svg>
```

### 1.5 历史记录页

展示所有已使用/已核销和已过期的信誉券。

#### 构成元素与布局：
- **顶部导航/标题：** “历史记录”。
- **筛选/排序选项 (可选):** 按状态、创建日期等筛选。
- **历史券列表：**
    - **列表项：** 类似于首页列表项，但强调状态和核销/过期日期。
    - **关键信息：** 券名称、状态、创建人、核销/过期日期。

#### SVG 线框图参考:

```svg
<svg width="300" height="500" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f0f0f0"/>
  <rect x="10" y="10" width="280" height="40" fill="#ddd"/>
  <text x="20" y="35" font-family="Arial" font-size="16" fill="#555">历史记录</text>

  <rect x="10" y="60" width="280" height="80" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <text x="20" y="85" font-family="Arial" font-size="16" fill="#333">券名称 A</text>
  <text x="20" y="105" font-family="Arial" font-size="12" fill="#666">状态：已核销 | 创建人：对方</text>
  <text x="20" y="120" font-family="Arial" font-size="12" fill="#666">核销日期：xxxx-xx-xx</text>

  <rect x="10" y="150" width="280" height="80" fill="#fff" stroke="#ccc" stroke-width="1"/>
  <text x="20" y="175" font-family="Arial" font-size="16" fill="#333">券名称 B</text>
  <text x="20" y="195" font-family="Arial" font-size="12" fill="#666">状态：已过期 | 创建人：自己</text>
  <text x="20" y="210" font-family="Arial" font-size="12" fill="#666">过期日期：xxxx-xx-xx</text>

     <rect x="0" y="450" width="300" height="50" fill="#ddd"/>
  <text x="30" y="475" font-family="Arial" font-size="14" fill="#555">首页</text>
  <text x="100" y="475" font-family="Arial" font-size="14" fill="#555">创建券</text>
  <text x="170" y="475" font-family="Arial" font-size="14" fill="#555">历史</text>
  <text x="240" y="475" font-family="Arial" font-size="14" fill="#555">伴侣</text>
</svg>
```

## 2. 核心交互流程说明

### 2.1 创建新券流程

1.  用户进入小程序，点击底部导航栏的“创建券”按钮或首页醒目入口。
2.  进入“创建信誉券”页面。
3.  用户输入券名称（必填），可选择输入描述和有效期。
4.  用户点击“创建”按钮。
5.  系统创建信誉券，并将其添加到当前用户的“送出”列表和伴侣方的“待使用”列表中。
6.  页面跳转回首页或显示创建成功提示。

### 2.2 查看并核销券流程

1.  用户在首页/券列表页看到有新的或待使用的信誉券。
2.  用户点击列表项，进入“券详情页”。
3.  在详情页查看券的详细信息（名称、描述、创建人、有效期等）。
4.  如果券状态为“待使用”，用户（通常是获得券的一方）可以看到并点击“立即核销”按钮。
5.  出现核销确认弹窗，用户点击“确认”。
6.  系统将券状态更新为“已核销”，记录核销时间。
7.  页面返回首页或历史记录页，或在当前页更新券状态显示。

### 2.3 伴侣绑定流程

1.  用户进入“伴侣管理”页面。
2.  **如果未绑定：**
    -   用户选择“生成邀请码”或“输入邀请码”。
    -    선택生成邀请码：系统生成唯一码并展示，用户将其分享给伴侣。
    -    선택输入邀请码：用户在输入框输入伴侣提供的邀请码，点击“绑定”。
    -   系统验证邀请码，如果有效且未被使用，则完成绑定。
    -   显示绑定成功信息。
3.  **如果已绑定：**
    -   显示当前伴侣信息。
    -   提供“解绑”按钮。点击后需要确认，系统解除绑定关系。

## 3. 设计原则强调（MVP 阶段）

在MVP阶段，设计应重点遵循以下原则：

*   **简洁 (Simplicity):**
    *   界面元素和信息布局尽量简洁，避免冗余功能和信息。
    *   核心流程（创建、使用、核销）路径清晰，一步到位。
    *   视觉风格保持统一，易于理解。
*   **易用 (Usability):**
    *   关键操作按钮醒目且易于触达。
    *   输入框、选择器等表单元素符合小程序用户习惯。
    *   提供必要的反馈（如创建成功提示、核销确认弹窗）。
*   **聚焦核心价值 (Focus on Core Value):**
    *   优先保证信誉券的创建、展示和核销功能稳定可用。
    *   伴侣绑定功能保障核心用户关系建立。
    *   历史记录功能满足基本追溯需求。
    *   非核心或复杂的附加功能（如券分类、模板、复杂统计等）可留待后续版本。
*   **快速迭代基础搭建 (Foundation for Fast Iteration):**
    *   原型设计应考虑未来功能的扩展性，但MVP阶段不需过度设计。
    *   确保基础数据结构和页面流转合理，为后续版本迭代打下基础。

遵循这些原则，MVP阶段的情侣信誉券小程序将能快速上线，验证核心功能，并根据用户反馈迭代优化。