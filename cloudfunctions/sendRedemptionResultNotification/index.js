/**
 * 发送核销结果通知云函数
 * 当发券方处理核销请求后，向双方发送处理结果通知
 */

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数运行上下文
 * @returns {Object} 执行结果
 */
exports.main = async (event, context) => {
  console.log('开始执行发送核销结果通知云函数:', event);
  
  try {
    // 参数验证
    const { couponId, result, rejectReason, approvalNote } = event;
    
    if (!couponId || !result) {
      throw new Error('缺少必要参数: couponId 或 result');
    }

    // 验证result参数
    if (!['approved', 'rejected'].includes(result)) {
      throw new Error('result 参数值无效，应该为 approved 或 rejected');
    }

    // 获取当前调用者的openid
    const { OPENID } = cloud.getWXContext();
    
    // 获取券详细信息
    const couponQuery = await db.collection('coupons').doc(couponId).get();
    if (!couponQuery.data) {
      throw new Error('券信息不存在');
    }
    
    const coupon = couponQuery.data;
    
    // 权限验证：只有券的创建方可以处理核销请求
    if (coupon.creatorId !== OPENID) {
      throw new Error('无权限处理此券的核销请求');
    }

    // 验证券状态：应该是待核销状态
    if (coupon.status !== 'pending_redeem') {
      throw new Error(`券状态不正确，当前状态: ${coupon.status}，应该为 pending_redeem`);
    }

    // 如果是拒绝操作，验证是否提供了拒绝原因
    if (result === 'rejected' && !rejectReason) {
      throw new Error('拒绝核销时必须提供拒绝原因');
    }

    console.log('准备处理核销请求并发送结果通知:', {
      couponId,
      couponTitle: coupon.title,
      result,
      creatorId: coupon.creatorId,
      recipientId: coupon.recipientId,
      rejectReason: rejectReason || null,
      approvalNote: approvalNote || null
    });

    // 准备更新数据
    const updateData = {
      redeemResultTime: new Date(),
      redeemResult: result,
      updateTime: new Date()
    };

    if (result === 'approved') {
      updateData.status = 'redeemed';
      updateData.redeemTime = new Date();
      updateData.approvalNote = approvalNote || '';
    } else {
      updateData.status = 'received'; // 拒绝后状态回到已接收
      updateData.rejectReason = rejectReason;
    }

    // 更新券状态
    await db.collection('coupons').doc(couponId).update({
      data: updateData
    });

    // 获取处理人信息
    const userQuery = await db.collection('users').where({
      _openid: OPENID
    }).get();
    
    const userInfo = userQuery.data[0] || {};

    // 发送通知给申请人
    const notificationTitle = result === 'approved' ? '兑现申请已通过' : '兑现申请被拒绝';
    const notificationContent = result === 'approved' 
      ? `您的信任券"${coupon.title}"兑现申请已通过`
      : `您的信任券"${coupon.title}"兑现申请被拒绝：${rejectReason}`;

    await db.collection('notifications').add({
      data: {
        type: 'redeem_result',
        title: notificationTitle,
        content: notificationContent,
        recipientId: coupon.recipientId,
        senderId: OPENID,
        relatedId: couponId,
        relatedType: 'coupon',
        isRead: false,
        createTime: new Date(),
        data: {
          couponId: couponId,
          couponTitle: coupon.title,
          result: result,
          rejectReason: rejectReason || null,
          approvalNote: approvalNote || null,
          processorNickname: userInfo.nickName || '对方'
        }
      }
    });

    console.log('核销结果通知已发送');

    // 返回结果
    return {
      success: true,
      message: '核销结果处理完成',
      data: {
        couponId,
        couponTitle: coupon.title,
        result,
        newStatus: updateData.status,
        creatorId: coupon.creatorId,
        recipientId: coupon.recipientId,
        processTime: updateData.redeemResultTime,
        rejectReason: rejectReason || null,
        approvalNote: approvalNote || null,
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('发送核销结果通知云函数执行失败:', error);

    return {
      success: false,
      message: '云函数执行失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};
