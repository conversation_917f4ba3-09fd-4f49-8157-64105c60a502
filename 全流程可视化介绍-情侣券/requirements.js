// Content for the Early Product Requirements Document (PRD) section

function renderRequirements() {
    return `
        <h3 class="text-xl font-bold text-pink-800 mb-3">1. 引言</h3>
        
        <div class="mb-6">
            <p class="font-semibold mb-2">项目背景：</p>
            <p class="mb-3">源于情侣/夫妻间承诺难以记录和履约的痛点，旨在将承诺具象化为虚拟票券，增加趣味互动和仪式感。</p>
            
            <p class="font-semibold mb-2">项目目标：</p>
            <p class="mb-3">MVP核心目标是快速实现券的创建、发送、接收、查看、核销闭环，验证产品概念；长远愿景是成为情侣/夫妻间维系情感、增进互动、提升生活趣味和仪式感的首选线上工具。</p>
            
            <p class="font-semibold mb-2">目标用户：</p>
            <p>处于稳定恋爱关系或已婚的伴侣双方。</p>
        </div>
        
        <h3 class="text-xl font-bold text-pink-800 mb-3">2. 产品概述</h3>
        
        <div class="mb-6">
            <p class="font-semibold mb-2">产品定位：</p>
            <p class="mb-3">一款增进情侣/夫妻情感互动与承诺履约的迷你工具，以轻松趣味的方式，用具象化的"信誉券"作为媒介。</p>
            
            <p class="font-semibold mb-2">核心价值：</p>
            <ul class="list-disc pl-5 mb-3">
                <li>承诺具象化</li>
                <li>互动趣味化</li>
                <li>履约激励</li>
                <li>情感记录</li>
                <li>促进诚实守信</li>
            </ul>
        </div>
        
        <h3 class="text-xl font-bold text-pink-800 mb-3">3. 用户场景与故事</h3>
        
        <div class="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="p-4 border rounded-lg bg-pink-50 border-pink-200">
                <div class="font-medium text-pink-800 mb-2">主动表达爱意</div>
                <p class="text-sm">小明想给女友一个惊喜，创建一张"一起看电影"的券发给她，附带甜蜜留言"想和你一起看这部新电影"，女友收到后感到惊喜和期待。</p>
            </div>
            
            <div class="p-4 border rounded-lg bg-indigo-50 border-indigo-200">
                <div class="font-medium text-indigo-800 mb-2">鼓励与奖励</div>
                <p class="text-sm">小红的男友最近在备考，她创建了一张"按摩券"作为鼓励，男友在考试后使用了这张券，两人度过了轻松愉快的时光。</p>
            </div>
            
            <div class="p-4 border rounded-lg bg-green-50 border-green-200">
                <div class="font-medium text-green-800 mb-2">承诺兑现</div>
                <p class="text-sm">小李承诺陪女友去她一直想去的餐厅，但总是因为工作推迟。他创建了"高级餐厅一日游"的券，女友在适当时间核销，提醒和确保了承诺的兑现。</p>
            </div>
            
            <div class="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                <div class="font-medium text-yellow-800 mb-2">情感记录</div>
                <p class="text-sm">一对长期恋爱的情侣，通过查看历史券记录，回顾过去一年的甜蜜瞬间和履约情况，增进了相互理解和感情联结。</p>
            </div>
        </div>
        
        <h3 class="text-xl font-bold text-pink-800 mb-3">4. 核心功能需求 (MVP)</h3>
        
        <div class="mb-6">
            <div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden mb-6">
                <table class="w-full text-sm">
                    <thead class="bg-gray-50 border-b border-gray-200">
                        <tr>
                            <th class="py-3 px-4 text-left font-medium text-gray-500">功能模块</th>
                            <th class="py-3 px-4 text-left font-medium text-gray-500">详细功能点</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-b border-gray-200">
                            <td class="py-3 px-4 font-medium text-pink-600">用户账户管理</td>
                            <td class="py-3 px-4">
                                <ul class="list-disc pl-5">
                                    <li>微信授权登录</li>
                                    <li>获取昵称头像</li>
                                    <li>伴侣绑定机制 (生成链接/码、接受邀请、唯一绑定)</li>
                                </ul>
                            </td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="py-3 px-4 font-medium text-pink-600">信誉券创建</td>
                            <td class="py-3 px-4">
                                <ul class="list-disc pl-5">
                                    <li>填写名称</li>
                                    <li>选择/自定义类型</li>
                                    <li>填写描述 (可选)</li>
                                    <li>设置有效期</li>
                                    <li>记录发行人</li>
                                    <li>(MVP可选) 券面简单自定义</li>
                                </ul>
                            </td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="py-3 px-4 font-medium text-pink-600">信誉券发送</td>
                            <td class="py-3 px-4">
                                <ul class="list-disc pl-5">
                                    <li>选择已绑定伴侣</li>
                                    <li>填写发送前留言 (可选)</li>
                                    <li>确认发送后券状态变为"待使用"并关联接收人</li>
                                </ul>
                            </td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="py-3 px-4 font-medium text-pink-600">信誉券接收与提醒</td>
                            <td class="py-3 px-4">
                                <ul class="list-disc pl-5">
                                    <li>"收到的券"列表新券提示</li>
                                    <li>(如条件允许) 微信服务通知提醒</li>
                                </ul>
                            </td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="py-3 px-4 font-medium text-pink-600">信誉券查看与管理</td>
                            <td class="py-3 px-4">
                                <ul class="list-disc pl-5">
                                    <li>券列表 (我发出的/我收到的 Tab)</li>
                                    <li>展示券状态 (待发送, 待使用, 已使用, 已过期)</li>
                                    <li>券详情页 (展示信息，根据状态显示操作按钮)</li>
                                </ul>
                            </td>
                        </tr>
                        <tr class="border-b border-gray-200">
                            <td class="py-3 px-4 font-medium text-pink-600">信誉券核销</td>
                            <td class="py-3 px-4">
                                <ul class="list-disc pl-5">
                                    <li>由接收方发起</li>
                                    <li>点击"使用此券/核销"按钮</li>
                                    <li>系统单方面更新券状态为"已使用"并记录时间</li>
                                </ul>
                            </td>
                        </tr>
                        <tr>
                            <td class="py-3 px-4 font-medium text-pink-600">历史记录</td>
                            <td class="py-3 px-4">
                                <ul class="list-disc pl-5">
                                    <li>查看所有非"待发送"或非"待使用"状态的券</li>
                                    <li>可按状态过滤</li>
                                    <li>查看详情</li>
                                </ul>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="mb-6">
                <p class="font-semibold mb-2">用户数据结构定义：</p>
                <div class="code-block">
<pre>{
  "_id": "string",      // 云开发自动生成
  "openid": "string",   // 微信用户唯一标识
  "nickName": "string", // 微信昵称
  "avatarUrl": "string",// 微信头像URL
  "partner_id": "string" // 绑定的伴侣用户ID (_id)，如果未绑定则为null
}</pre>
                </div>
            </div>
            
            <div>
                <p class="font-semibold mb-2">信誉券数据结构定义：</p>
                <div class="code-block">
<pre>{
  "_id": "string",              // 云开发自动生成
  "name": "string",             // 券名称
  "type": "string",             // 券类型（预设或自定义）
  "description": "string",      // 券描述 (可选)
  "issuer_id": "string",        // 发行人用户ID (_id)
  "recipient_id": "string",     // 接收人用户ID (_id)，发送时填写，创建时可为空
  "expiration_date": "Date",    // 过期时间 (Date对象，或Timestamp)
  "status": "string",           // 状态：pending (待发送或待使用), used (已使用), expired (已过期), revoked (已撤回 - MVP可选)
  "creation_time": "Date",      // 创建时间
  "used_time": "Date",          // 核销时间 (null 或 Date对象)
  "voucher_background": "string", // 券面背景标识或URL (MVP可选)
  "issue_message": "string"     // 发送附带留言 (可选)
}</pre>
                </div>
            </div>
        </div>
        
        <h3 class="text-xl font-bold text-pink-800 mb-3">5. 非功能性需求</h3>
        
        <div class="mb-6">
            <ul class="space-y-3">
                <li class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center">
                        <i class="fas fa-hand-pointer text-pink-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="font-medium">易用性</p>
                        <p class="text-sm text-gray-600">界面简洁直观，操作流程不超过3步，新用户无需培训即可上手。</p>
                    </div>
                </li>
                
                <li class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <i class="fas fa-tachometer-alt text-blue-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="font-medium">性能</p>
                        <p class="text-sm text-gray-600">页面加载时间不超过2秒，操作响应时间不超过1秒，支持至少1000对活跃用户同时在线。</p>
                    </div>
                </li>
                
                <li class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                        <i class="fas fa-shield-alt text-green-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="font-medium">安全性</p>
                        <p class="text-sm text-gray-600">用户数据加密存储，仅允许已绑定伴侣访问相关信息，防止未授权访问。</p>
                    </div>
                </li>
                
                <li class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                        <i class="fas fa-expand-arrows-alt text-purple-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="font-medium">可扩展性</p>
                        <p class="text-sm text-gray-600">系统架构设计应便于后续功能扩展，如添加新的券类型、支持更多的互动方式等。</p>
                    </div>
                </li>
                
                <li class="flex items-start">
                    <div class="flex-shrink-0 w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center">
                        <i class="fas fa-tools text-yellow-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="font-medium">可维护性</p>
                        <p class="text-sm text-gray-600">代码结构清晰，有完整注释，便于后续维护和迭代开发。</p>
                    </div>
                </li>
            </ul>
        </div>
        
        <h3 class="text-xl font-bold text-pink-800 mb-3">6. 未来迭代功能建议 (Post-MVP)</h3>
        
        <div class="mb-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-white p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="text-pink-600 mb-2 flex items-center">
                        <i class="fas fa-list-alt mr-2"></i>
                        <span class="font-medium">情侣共享愿望清单</span>
                    </div>
                    <p class="text-sm text-gray-600">双方可以添加、查看、更新共享的愿望清单，并可直接将愿望转化为券。</p>
                </div>
                
                <div class="bg-white p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="text-pink-600 mb-2 flex items-center">
                        <i class="fas fa-calendar-check mr-2"></i>
                        <span class="font-medium">特殊纪念日提醒与发券模板</span>
                    </div>
                    <p class="text-sm text-gray-600">自动识别或手动设置重要日期，提供特定场合的券模板和提醒。</p>
                </div>
                
                <div class="bg-white p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="text-pink-600 mb-2 flex items-center">
                        <i class="fas fa-trophy mr-2"></i>
                        <span class="font-medium">情侣积分/成就体系</span>
                    </div>
                    <p class="text-sm text-gray-600">基于券的使用情况累积积分，解锁不同等级和成就徽章。</p>
                </div>
                
                <div class="bg-white p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="text-pink-600 mb-2 flex items-center">
                        <i class="fas fa-palette mr-2"></i>
                        <span class="font-medium">高级券面自定义</span>
                    </div>
                    <p class="text-sm text-gray-600">支持更多样化的券面设计，包括上传图片、选择主题、添加装饰元素等。</p>
                </div>
                
                <div class="bg-white p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="text-pink-600 mb-2 flex items-center">
                        <i class="fas fa-random mr-2"></i>
                        <span class="font-medium">"摇一摇"随机券</span>
                    </div>
                    <p class="text-sm text-gray-600">通过摇晃手机获得系统随机生成的惊喜券，增加互动趣味性。</p>
                </div>
                
                <div class="bg-white p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="text-pink-600 mb-2 flex items-center">
                        <i class="fas fa-chart-pie mr-2"></i>
                        <span class="font-medium">数据统计与回顾</span>
                    </div>
                    <p class="text-sm text-gray-600">统计券的使用情况，生成可视化报表，支持年度/月度回顾功能。</p>
                </div>
                
                <div class="bg-white p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="text-pink-600 mb-2 flex items-center">
                        <i class="fas fa-comment-dots mr-2"></i>
                        <span class="font-medium">券的评论与心情记录</span>
                    </div>
                    <p class="text-sm text-gray-600">在券使用后，双方可以添加评论和记录当时的心情。</p>
                </div>
                
                <div class="bg-white p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="text-pink-600 mb-2 flex items-center">
                        <i class="fas fa-gift mr-2"></i>
                        <span class="font-medium">券转赠</span>
                    </div>
                    <p class="text-sm text-gray-600">允许用户将收到但未使用的券转赠回对方或保存为模板。</p>
                </div>
                
                <div class="bg-white p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow">
                    <div class="text-pink-600 mb-2 flex items-center">
                        <i class="fas fa-comments mr-2"></i>
                        <span class="font-medium">小程序内聊天</span>
                    </div>
                    <p class="text-sm text-gray-600">提供简单的聊天功能，让用户可以在发送/接收券时直接进行沟通。</p>
                </div>
            </div>
        </div>
    `;
}
