/**
 * 通用列表项组件样式
 * 现代化的列表项设计，支持多种布局和状态
 */

/* 列表项主体 */
.list-item {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx;
  background: white;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  cursor: pointer;
  transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 120rpx;
  position: relative;
  overflow: hidden;
  margin-bottom: 8rpx;
}

/* 悬浮背景效果 */
.list-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(233, 30, 99, 0.03) 0%, rgba(244, 143, 177, 0.02) 100%);
  opacity: 0;
  transition: opacity 300ms ease;
  pointer-events: none;
}

.list-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(233, 30, 99, 0.08);
  border-color: rgba(233, 30, 99, 0.1);
}

.list-item:hover::before {
  opacity: 1;
}

/* 点击效果 */
.item-hover {
  transform: scale(0.98);
  background: #f8fafc;
  border-color: #e2e8f0;
}

/* 禁用状态 */
.list-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  filter: grayscale(0.3);
}

/* 激活状态 */
.list-item.active {
  background: rgba(233, 30, 99, 0.05);
  border-color: rgba(233, 30, 99, 0.2);
}

.list-item.active .title-text {
  color: #e91e63;
  font-weight: 600;
}

/* 左侧图标区域 */
.list-item-icon {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  flex-shrink: 0;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transition: all 250ms ease;
  position: relative;
  overflow: hidden;
}

.list-item-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(233, 30, 99, 0.1) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 200ms ease;
}

.list-item:hover .list-item-icon {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  transform: scale(1.05);
  box-shadow: 0 4rpx 16rpx rgba(233, 30, 99, 0.1);
}

.list-item:hover .list-item-icon::before {
  opacity: 1;
}

.icon-image {
  width: 72rpx;
  height: 72rpx;
  border-radius: 16rpx;
  object-fit: cover;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.icon-text {
  font-size: 52rpx;
  line-height: 1;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
  transition: transform 200ms ease;
}

.list-item:hover .icon-text {
  transform: scale(1.1);
}

/* 主要内容区域 */
.list-item-content {
  flex: 1;
  min-width: 0;
  padding-right: 20rpx;
}

.list-item-title {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  flex-wrap: wrap;
  gap: 16rpx;
}

.title-text {
  font-size: 34rpx;
  font-weight: 500;
  color: #1e293b;
  line-height: 1.4;
  transition: color 200ms ease;
}

/* 徽章样式 */
.title-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
  height: 40rpx;
  padding: 0 16rpx;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
  line-height: 1;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.25);
  animation: badge-pulse 2s infinite;
}

@keyframes badge-pulse {
  0%, 100% {
    box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.25);
  }
  50% {
    box-shadow: 0 2rpx 16rpx rgba(239, 68, 68, 0.4);
  }
}

.badge-text {
  font-variant-numeric: tabular-nums;
}

/* 标签样式 */
.title-tags {
  display: flex;
  gap: 12rpx;
  flex-wrap: wrap;
}

.tag {
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #64748b;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1;
  border: 1rpx solid #e2e8f0;
  transition: all 200ms ease;
}

.tag:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #cbd5e1;
}

/* 副标题样式 */
.list-item-subtitle {
  margin-bottom: 8rpx;
}

.subtitle-text {
  font-size: 28rpx;
  color: #64748b;
  line-height: 1.5;
  font-weight: 400;
}

/* 描述文本样式 */
.list-item-description {
  margin-top: 8rpx;
}

.description-text {
  font-size: 24rpx;
  color: #94a3b8;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 右侧内容区域 */
.list-item-right {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex-shrink: 0;
}

.right-text {
  text-align: right;
}

.right-text-content {
  font-size: 30rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.3;
}

/* 状态指示器 */
.right-status {
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  text-align: center;
  backdrop-filter: blur(8rpx);
  border: 1rpx solid transparent;
  transition: all 200ms ease;
}

.status-text {
  font-size: 22rpx;
  font-weight: 600;
  line-height: 1;
  letter-spacing: 0.5rpx;
}

/* 状态颜色变体 */
.status-success {
  color: #059669;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-color: rgba(5, 150, 105, 0.2);
}

.status-warning {
  color: #d97706;
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border-color: rgba(217, 119, 6, 0.2);
}

.status-error {
  color: #dc2626;
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border-color: rgba(220, 38, 38, 0.2);
}

.status-info {
  color: #2563eb;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: rgba(37, 99, 235, 0.2);
}

.status-neutral {
  color: #475569;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: rgba(71, 85, 105, 0.2);
}

/* 箭头指示器 */
.list-item-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: rgba(233, 30, 99, 0.08);
  transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
  margin-left: 8rpx;
}

.list-item:hover .list-item-arrow {
  transform: translateX(6rpx) scale(1.1);
  background: rgba(233, 30, 99, 0.15);
  box-shadow: 0 4rpx 12rpx rgba(233, 30, 99, 0.2);
}

.arrow-icon {
  font-size: 28rpx;
  color: #e91e63;
  font-weight: bold;
  transition: transform 200ms ease;
}

.list-item:hover .arrow-icon {
  transform: translateX(2rpx);
}

/* 分隔线 */
.list-item-divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent, rgba(226, 232, 240, 0.8) 10%, rgba(226, 232, 240, 0.8) 90%, transparent);
  margin: 16rpx 32rpx;
  position: relative;
}

.list-item-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: #e2e8f0;
}

/* 列表项变体 */
.list-item.compact {
  padding: 24rpx 32rpx;
  min-height: 96rpx;
}

.list-item.compact .list-item-icon {
  width: 64rpx;
  height: 64rpx;
  margin-right: 24rpx;
}

.list-item.large {
  padding: 48rpx 32rpx;
  min-height: 160rpx;
}

.list-item.large .list-item-icon {
  width: 112rpx;
  height: 112rpx;
  margin-right: 40rpx;
}

/* 响应式适配 */
@media screen and (max-width: 480rpx) {
  .list-item {
    padding: 28rpx 24rpx;
    min-height: 104rpx;
  }
  
  .list-item-icon {
    width: 72rpx;
    height: 72rpx;
    margin-right: 24rpx;
  }
  
  .icon-image {
    width: 56rpx;
    height: 56rpx;
  }
  
  .icon-text {
    font-size: 44rpx;
  }
  
  .title-text {
    font-size: 30rpx;
  }
  
  .subtitle-text {
    font-size: 26rpx;
  }
  
  .right-text-content {
    font-size: 26rpx;
  }
  
  .list-item-arrow {
    width: 48rpx;
    height: 48rpx;
  }
  
  .arrow-icon {
    font-size: 24rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .list-item {
    background: #1e293b;
    border-color: #334155;
  }
  
  .list-item:hover {
    background: #334155;
    border-color: #475569;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
  }
  
  .item-hover {
    background: #334155;
    border-color: #475569;
  }
  
  .list-item-icon {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
  }
  
  .list-item:hover .list-item-icon {
    background: linear-gradient(135deg, #475569 0%, #64748b 100%);
  }
  
  .title-text {
    color: #f1f5f9;
  }
  
  .subtitle-text {
    color: #cbd5e1;
  }
  
  .description-text {
    color: #94a3b8;
  }
  
  .right-text-content {
    color: #cbd5e1;
  }
  
  .tag {
    background: linear-gradient(135deg, #475569 0%, #64748b 100%);
    color: #e2e8f0;
    border-color: #64748b;
  }
  
  .list-item-divider {
    background: linear-gradient(90deg, transparent, rgba(71, 85, 105, 0.8) 10%, rgba(71, 85, 105, 0.8) 90%, transparent);
  }
  
  .list-item-divider::before {
    background: #475569;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .list-item {
    border: 2rpx solid #000;
  }
  
  .title-text {
    color: #000;
    font-weight: 600;
  }
  
  .subtitle-text {
    color: #333;
    font-weight: 500;
  }
  
  .list-item-arrow {
    border: 2rpx solid #000;
  }
  
  .arrow-icon {
    color: #000;
  }
}

/* 无障碍支持 - 减少动画 */
@media (prefers-reduced-motion: reduce) {
  .list-item,
  .list-item-icon,
  .list-item-arrow,
  .tag,
  .title-badge {
    transition: none;
    animation: none;
  }
  
  .list-item:hover {
    transform: none;
  }
  
  .list-item:hover .list-item-icon {
    transform: none;
  }
  
  .list-item:hover .list-item-arrow {
    transform: none;
  }
}

/* 打印样式 */
@media print {
  .list-item {
    box-shadow: none;
    border: 1rpx solid #ccc;
    break-inside: avoid;
  }
  
  .list-item:hover {
    transform: none;
    box-shadow: none;
  }
  
  .list-item-arrow {
    display: none;
  }
  
  .title-badge {
    background: #000;
    color: #fff;
  }
}
