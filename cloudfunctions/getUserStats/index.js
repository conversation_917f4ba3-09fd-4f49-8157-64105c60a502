/**
 * 获取用户统计数据
 * 包括券数量、情侣天数等
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  console.log('获取用户统计, openid:', openid);
  
  try {
    // 首先查找用户信息
    const userResult = await db.collection('users').where({
      _openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    
    // 并行查询各种统计数据 - 修复：使用正确的字段名
    const [
      totalCouponsResult,
      activeCouponsResult,
      redeemedCouponsResult,
      receivedCouponsResult,
      coupleResult
    ] = await Promise.all([
      // 创建的券总数 - 使用creatorId和openid
      db.collection('coupons').where({
        creatorId: openid
      }).count(),
      
      // 有效券数量 - 使用creatorId和recipientId与openid
      db.collection('coupons').where({
        $or: [
          { creatorId: openid },
          { recipientId: openid }
        ],
        status: db.command.in(['sent', 'received', 'pending_redeem'])
      }).count(),
      
      // 已兑换券数量 - 使用creatorId和recipientId与openid
      db.collection('coupons').where({
        $or: [
          { creatorId: openid },
          { recipientId: openid }
        ],
        status: 'redeemed'
      }).count(),
      
      // 收到的券数量 - 使用recipientId和openid
      db.collection('coupons').where({
        recipientId: openid
      }).count(),
      
      // 情侣关系信息
      user.partnerId ? db.collection('couples').where({
        $or: [
          { userId1: user._id },
          { userId2: user._id }
        ],
        status: 'active'
      }).get() : Promise.resolve({ data: [] })
    ]);
    
    // 计算恋爱天数
    let loveDays = 0;
    let coupleInfo = null;
    
    if (coupleResult.data.length > 0) {
      coupleInfo = coupleResult.data[0];
      if (coupleInfo.relationshipStartDate) {
        const startDate = new Date(coupleInfo.relationshipStartDate);
        const today = new Date();
        const timeDiff = today.getTime() - startDate.getTime();
        loveDays = Math.floor(timeDiff / (1000 * 3600 * 24));
      }
    }
    
    // 查询最近的券活动 - 修复：使用正确的字段名
    const recentCouponsResult = await db.collection('coupons')
      .where({
        $or: [
          { creatorId: openid },
          { recipientId: openid }
        ]
      })
      .orderBy('createTime', 'desc')
      .limit(5)
      .get();
    
    const stats = {
      // 券相关统计
      totalCoupons: totalCouponsResult.total,
      activeCoupons: activeCouponsResult.total,
      redeemedCoupons: redeemedCouponsResult.total,
      receivedCoupons: receivedCouponsResult.total,
      
      // 关系统计
      loveDays: loveDays,
      isCoupleBound: !!user.partnerId,
      
      // 活跃度统计
      recentActivity: recentCouponsResult.data.length,
      
      // 最近券列表
      recentCoupons: recentCouponsResult.data
    };
    
    return {
      success: true,
      message: '获取用户统计成功',
      data: {
        userInfo: user,
        stats: stats,
        coupleInfo: coupleInfo
      }
    };
    
  } catch (error) {
    console.error('获取用户统计失败:', error);
    return {
      success: false,
      message: '获取用户统计失败',
      error: error.message
    };
  }
}; 