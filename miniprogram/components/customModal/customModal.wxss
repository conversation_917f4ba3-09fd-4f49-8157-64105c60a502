/**
 * 自定义模态框组件样式
 * 提供现代化的模态框样式，支持动画和响应式设计
 */

/* 模态框遮罩层 */
.custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8rpx);
  transition: opacity 300ms ease;
}

.custom-modal.show {
  opacity: 1;
  animation: fadeIn 300ms ease;
}

.custom-modal.hide {
  opacity: 0;
  animation: fadeOut 300ms ease;
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes modalShow {
  from {
    transform: scale(0.9) translateY(-20rpx);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* 模态框主体 */
.modal-content {
  background: white;
  border-radius: 32rpx;
  max-width: 600rpx;
  width: 90vw;
  max-height: 80vh;
  box-shadow: 0 40rpx 120rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: modalShow 350ms cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* 模态框头部 */
.modal-header {
  position: relative;
  padding: 48rpx 48rpx 24rpx;
  text-align: center;
  border-bottom: 2rpx solid #f3f4f6;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #111827;
  line-height: 1.4;
  margin: 0;
}

.modal-close {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  transition: all 200ms ease;
  cursor: pointer;
}

.modal-close:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.modal-close:active {
  transform: scale(0.95);
  background: #e5e7eb;
}

.close-icon {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: bold;
}

/* 模态框内容 */
.modal-body {
  padding: 32rpx 48rpx;
  text-align: center;
  max-height: 400rpx;
  overflow-y: auto;
}

.modal-content-text {
  font-size: 30rpx;
  color: #4b5563;
  line-height: 1.6;
  margin: 0;
}

/* 模态框底部 */
.modal-footer {
  padding: 24rpx 48rpx 48rpx;
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.modal-btn::after {
  border: none;
}

/* 按钮样式变体 */
.cancel-btn {
  background: #f9fafb;
  color: #374151;
  border: 2rpx solid #e5e7eb;
}

.cancel-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.confirm-btn {
  background: linear-gradient(135deg, #e91e63 0%, #f48fb1 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(233, 30, 99, 0.25);
  border: 2rpx solid transparent;
}

.confirm-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(233, 30, 99, 0.35);
}

/* 按钮按下效果 */
.btn-hover {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 单按钮样式 */
.modal-footer .modal-btn:only-child {
  background: linear-gradient(135deg, #e91e63 0%, #f48fb1 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(233, 30, 99, 0.25);
}

/* 按钮加载状态 */
.modal-btn.loading {
  pointer-events: none;
  opacity: 0.6;
}

.modal-btn.loading::before {
  content: '';
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid transparent;
  border-top: 4rpx solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 16rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media screen and (max-width: 480rpx) {
  .modal-content {
    width: 95vw;
    margin: 24rpx;
  }
  
  .modal-header {
    padding: 40rpx 32rpx 20rpx;
  }
  
  .modal-title {
    font-size: 32rpx;
  }
  
  .modal-body {
    padding: 24rpx 32rpx;
  }
  
  .modal-content-text {
    font-size: 28rpx;
  }
  
  .modal-footer {
    padding: 20rpx 32rpx 40rpx;
    gap: 20rpx;
  }
  
  .modal-btn {
    height: 80rpx;
    font-size: 30rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modal-content {
    background: #1f2937;
    box-shadow: 0 40rpx 120rpx rgba(0, 0, 0, 0.5);
  }
  
  .modal-header {
    border-bottom-color: #374151;
  }
  
  .modal-title {
    color: #f9fafb;
  }
  
  .modal-content-text {
    color: #d1d5db;
  }
  
  .modal-close {
    background: #374151;
    border-color: #4b5563;
  }
  
  .modal-close:hover {
    background: #4b5563;
    border-color: #6b7280;
  }
  
  .close-icon {
    color: #9ca3af;
  }
  
  .cancel-btn {
    background: #374151;
    color: #d1d5db;
    border-color: #4b5563;
  }
  
  .cancel-btn:hover {
    background: #4b5563;
    border-color: #6b7280;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .custom-modal,
  .modal-content,
  .modal-btn {
    animation: none;
    transition: none;
  }
  
  .modal-content {
    transform: none;
    opacity: 1;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .modal-content {
    border: 4rpx solid #000;
  }
  
  .modal-btn {
    border-width: 4rpx;
  }
  
  .cancel-btn {
    border-color: #000;
  }
  
  .confirm-btn {
    background: #000;
    border-color: #000;
  }
}
