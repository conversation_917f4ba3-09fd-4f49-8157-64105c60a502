/**
 * 获取通知列表
 * 支持分页和筛选
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  console.log('获取通知列表, openid:', openid, 'params:', event);
  
  try {
    // 首先查找用户信息
    const userResult = await db.collection('users').where({
      _openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    const page = event.page || 1;
    const pageSize = event.pageSize || 20;
    const skip = (page - 1) * pageSize;
    
    // 构建查询条件 - 修复：使用正确的字段名
    let queryCondition = {
      recipientId: openid,
      // 修复问题3：查询所有活跃状态的通知，确保显示完整历史记录
      $or: [
        { notificationStatus: 'active' },
        { notificationStatus: db.command.exists(false) }
      ]
    };

    // 如果指定了类型，添加类型过滤
    if (event.type && event.type !== 'all') {
      queryCondition.type = event.type;
    }

    // 如果只查询未读 - 修复：使用isRead字段
    if (event.unreadOnly) {
      queryCondition.isRead = false;
    }
    
    // 查询通知列表 - 修复：使用createTime字段
    const notificationsResult = await db.collection('notifications')
      .where(queryCondition)
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(pageSize)
      .get();
    
    // 关联查询券信息
    const notifications = [];
    for (const notification of notificationsResult.data) {
      let couponInfo = null;
      
      // 如果通知有关联的券ID，查询券信息
      if (notification.couponId) {
        try {
          const couponResult = await db.collection('coupons').doc(notification.couponId).get();
          if (couponResult.data) {
            couponInfo = couponResult.data;
          }
        } catch (error) {
          console.warn(`查询券信息失败，券ID: ${notification.couponId}`, error);
        }
      }
      
      notifications.push({
        ...notification,
        isRead: notification.isRead,
        couponInfo: couponInfo
      });
    }
    
    // 查询总数
    const totalResult = await db.collection('notifications')
      .where(queryCondition)
      .count();
    
    // 查询未读数量 - 修复：使用正确的字段名
    const unreadResult = await db.collection('notifications')
      .where({
        recipientId: openid,
        isRead: false
      })
      .count();
    
    return {
      success: true,
      message: '获取通知列表成功',
      data: {
        notifications: notifications,
        totalCount: totalResult.total,
        unreadCount: unreadResult.total,
        currentPage: page,
        pageSize: pageSize,
        hasMore: notificationsResult.data.length === pageSize
      }
    };
    
  } catch (error) {
    console.error('获取通知列表失败:', error);
    return {
      success: false,
      message: '获取通知列表失败',
      error: error.message
    };
  }
}; 