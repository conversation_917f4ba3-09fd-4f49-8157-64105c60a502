/**
 * 检查情侣绑定状态
 * 返回用户的情侣绑定信息
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  console.log('检查情侣状态, openid:', openid);
  
  try {
    // 首先查找用户信息
    const userResult = await db.collection('users').where({
      _openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      console.log('用户不存在:', openid);
      return {
        success: false,
        message: '用户不存在',
        data: {
          isBound: false
        }
      };
    }
    
    const user = userResult.data[0];
    console.log('找到用户信息:', {
      _id: user._id,
      _openid: user._openid,
      nickName: user.nickName,
      partnerId: user.partnerId
    });
    
    // 检查用户是否有partnerId
    if (!user.partnerId) {
      console.log('用户没有partnerId，未绑定情侣');
      return {
        success: true,
        message: '用户未绑定情侣',
        data: {
          isBound: false,
          userInfo: user
        }
      };
    }
    
    console.log('用户有partnerId:', user.partnerId, '开始查找couples记录...');
    
    // 直接通过partnerId查找couples记录
    let coupleResult = await db.collection('couples').doc(user.partnerId).get();
    
    if (!coupleResult.data) {
      console.log('通过partnerId未找到couples记录，尝试通过user1Id/user2Id查找...');
      
      // 如果通过partnerId没找到，尝试通过user1Id/user2Id查找
      coupleResult = await db.collection('couples').where({
        $or: [
          { user1Id: user._openid },
          { user2Id: user._openid }
        ],
        status: 'active'
      }).get();
      
      if (coupleResult.data.length === 0) {
        console.log('没有找到有效的情侣关系，清除用户的partnerId');
        // 如果没有找到有效的情侣关系，清除用户的partnerId
        await db.collection('users').doc(user._id).update({
          data: {
            partnerId: null
          }
        });
        
        return {
          success: true,
          message: '情侣关系已失效',
          data: {
            isBound: false,
            userInfo: {
              ...user,
              partnerId: null
            }
          }
        };
      }
      
      coupleResult.data = coupleResult.data[0];
    }
    
    const coupleInfo = coupleResult.data;
    console.log('找到couples记录:', {
      _id: coupleInfo._id,
      user1Id: coupleInfo.user1Id,
      user2Id: coupleInfo.user2Id,
      status: coupleInfo.status
    });
    
    // 确定伙伴的openid
    const partnerOpenid = coupleInfo.user1Id === user._openid ? coupleInfo.user2Id : coupleInfo.user1Id;
    console.log('伙伴openid:', partnerOpenid);
    
    // 获取伴侣信息
    const partnerResult = await db.collection('users').where({
      _openid: partnerOpenid
    }).get();
    
    let partnerInfo = null;
    if (partnerResult.data.length > 0) {
      partnerInfo = partnerResult.data[0];
      console.log('找到伙伴信息:', {
        _id: partnerInfo._id,
        _openid: partnerInfo._openid,
        nickName: partnerInfo.nickName
      });
    } else {
      console.log('未找到伙伴信息');
    }
    
    // 计算相恋天数
    let togetherDays = 0;
    if (coupleInfo.bindTime) {
      const bindDate = new Date(coupleInfo.bindTime);
      const today = new Date();
      const timeDiff = today.getTime() - bindDate.getTime();
      togetherDays = Math.max(0, Math.floor(timeDiff / (1000 * 3600 * 24)));
    }
    
    const result = {
      success: true,
      message: '获取情侣状态成功',
      data: {
        isBound: true,
        userInfo: user,
        coupleInfo: {
          ...coupleInfo,
          partner: partnerInfo,
          togetherDays: togetherDays,
          relationshipStartDate: coupleInfo.bindTime
        }
      }
    };
    
    console.log('返回结果:', {
      isBound: result.data.isBound,
      hasPartner: !!result.data.coupleInfo.partner,
      togetherDays: result.data.coupleInfo.togetherDays
    });
    
    return result;
    
  } catch (error) {
    console.error('检查情侣状态失败:', error);
    return {
      success: false,
      message: '检查情侣状态失败',
      error: error.message,
      data: {
        isBound: false
      }
    };
  }
}; 