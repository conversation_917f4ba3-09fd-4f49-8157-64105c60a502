/**
 * 自定义模态框组件 JS
 * 提供灵活的模态框控制逻辑
 */

Component({
  /**
   * 组件属性列表
   */
  properties: {
    // 是否显示模态框
    visible: {
      type: Boolean,
      value: false,
      observer: 'onVisibleChange'
    },
    
    // 模态框标题
    title: {
      type: String,
      value: '提示'
    },
    
    // 模态框内容文本
    content: {
      type: String,
      value: ''
    },
    
    // 确认按钮文本
    confirmText: {
      type: String,
      value: '确定'
    },
    
    // 取消按钮文本
    cancelText: {
      type: String,
      value: '取消'
    },
    
    // 是否显示取消按钮
    showCancel: {
      type: Boolean,
      value: true
    },
    
    // 是否显示关闭图标
    showCloseIcon: {
      type: Boolean,
      value: false
    },
    
    // 是否显示底部按钮区域
    showActions: {
      type: Boolean,
      value: true
    },
    
    // 点击遮罩层是否关闭
    maskClosable: {
      type: Boolean,
      value: true
    },
    
    // 确认按钮是否加载状态
    confirmLoading: {
      type: Boolean,
      value: false
    },
    
    // 自定义样式类名
    customClass: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件数据
   */
  data: {
    animating: false // 是否正在执行动画
  },

  /**
   * 组件方法列表
   */
  methods: {
    /**
     * 显示状态变化监听
     */
    onVisibleChange(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.setData({
          animating: true
        });
        
        // 动画完成后重置状态
        setTimeout(() => {
          this.setData({
            animating: false
          });
        }, 300);
      }
    },

    /**
     * 确认按钮点击事件
     */
    onConfirm(e) {
      // 阻止事件冒泡
      e.stopPropagation();
      
      // 如果正在加载则返回
      if (this.properties.confirmLoading) {
        return;
      }
      
      // 触发确认事件
      this.triggerEvent('confirm', {
        type: 'confirm',
        timeStamp: Date.now()
      });
    },

    /**
     * 取消按钮点击事件
     */
    onCancel(e) {
      // 阻止事件冒泡
      e.stopPropagation();
      
      // 触发取消事件
      this.triggerEvent('cancel', {
        type: 'cancel',
        timeStamp: Date.now()
      });
      
      // 关闭模态框
      this.hide();
    },

    /**
     * 遮罩层点击事件
     */
    onMaskTap(e) {
      // 阻止事件冒泡
      e.stopPropagation();
      
      // 如果允许点击遮罩关闭
      if (this.properties.maskClosable && !this.data.animating) {
        this.triggerEvent('maskClick', {
          type: 'maskClick',
          timeStamp: Date.now()
        });
        
        this.hide();
      }
    },

    /**
     * 阻止事件冒泡
     */
    prevent(e) {
      e.stopPropagation();
    },

    /**
     * 显示模态框
     */
    show() {
      if (!this.properties.visible) {
        this.triggerEvent('show', {
          type: 'show',
          timeStamp: Date.now()
        });
        
        this.setData({
          visible: true
        });
      }
    },

    /**
     * 隐藏模态框
     */
    hide() {
      if (this.properties.visible) {
        this.triggerEvent('hide', {
          type: 'hide',
          timeStamp: Date.now()
        });
        
        this.setData({
          animating: true
        });
        
        // 延迟隐藏，让关闭动画播放完成
        setTimeout(() => {
          this.setData({
            visible: false,
            animating: false
          });
        }, 300);
      }
    },

    /**
     * 切换模态框显示状态
     */
    toggle() {
      if (this.properties.visible) {
        this.hide();
      } else {
        this.show();
      }
    },

    /**
     * 设置确认按钮加载状态
     */
    setConfirmLoading(loading) {
      this.setData({
        confirmLoading: loading
      });
    }
  },

  /**
   * 生命周期函数
   */
  lifetimes: {
    /**
     * 组件实例被创建
     */
    created() {
      console.log('[CustomModal] Component created');
    },

    /**
     * 组件实例进入页面节点树
     */
    attached() {
      console.log('[CustomModal] Component attached');
    },

    /**
     * 组件实例被从页面节点树移除
     */
    detached() {
      console.log('[CustomModal] Component detached');
    }
  },

  /**
   * 页面生命周期函数
   */
  pageLifetimes: {
    /**
     * 页面显示
     */
    show() {
      // 页面显示时的处理逻辑
    },

    /**
     * 页面隐藏
     */
    hide() {
      // 页面隐藏时自动关闭模态框
      if (this.properties.visible) {
        this.hide();
      }
    }
  },

  /**
   * 组件数据字段监听器
   */
  observers: {
    /**
     * 监听确认加载状态变化
     */
    'confirmLoading': function(loading) {
      if (loading) {
        console.log('[CustomModal] Confirm button loading started');
      } else {
        console.log('[CustomModal] Confirm button loading ended');
      }
    }
  },

  /**
   * 组件选项
   */
  options: {
    // 启用多插槽支持
    multipleSlots: true,
    // 使用纯数据字段优化性能
    pureDataPattern: /^_/,
    // 虚拟化组件节点
    virtualHost: false
  },

  /**
   * 外部样式类
   */
  externalClasses: [
    'custom-class',
    'modal-class',
    'content-class',
    'header-class',
    'body-class',
    'footer-class'
  ]
});
