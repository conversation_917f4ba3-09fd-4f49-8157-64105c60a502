# 首页背景图片设置指南

## 📸 图片处理步骤

### 1. 保存背景图片
请将您提供的情侣插画图片保存为：
```
miniprogram/images/couple-background.png
```

### 2. 图片优化建议
为了确保最佳的显示效果和性能，建议：

**尺寸优化：**
- 宽度：750px（对应小程序设计稿标准）
- 高度：1334px（iPhone 6/7/8标准屏幕比例）
- 格式：PNG（保持透明度）或 WEBP（更小体积）

**质量设置：**
- 压缩质量：80-90%（平衡质量和体积）
- 文件大小：建议控制在 200KB 以内

### 3. 图片处理工具推荐
- **在线工具**：TinyPNG、Squoosh
- **本地工具**：Photoshop、GIMP
- **小程序工具**：微信开发者工具内置压缩

## 🎨 设计效果说明

### 背景效果特点
1. **层次分明**：背景图片在最底层，不影响功能使用
2. **适度模糊**：使用 `filter: blur(1px)` 轻微模糊，减少视觉干扰
3. **渐变遮罩**：白色半透明渐变，确保内容清晰可读
4. **毛玻璃效果**：卡片使用 `backdrop-filter` 增强层次感

### 透明度设计
- 顶部：85% 透明度（更多显示背景）
- 中间：90% 透明度（适度遮罩）
- 底部：95-98% 透明度（确保内容清晰）

### 响应式适配
- 自动适配不同屏幕尺寸
- 小屏幕设备增加遮罩透明度
- 保持图片比例不变形

## 🔧 自定义调整

### 调整背景透明度
在 `index.wxss` 中修改 `.background-overlay` 的透明度值：
```css
rgba(255, 255, 255, 0.85) /* 第四个参数控制透明度 */
```

### 调整模糊程度
修改 `.background-image` 的模糊值：
```css
filter: blur(2px); /* 增加模糊程度 */
```

### 更换背景图片
只需替换 `/images/couple-background.png` 文件即可

## 📱 测试建议

### 功能测试
1. 确保所有文字清晰可读
2. 验证按钮点击正常
3. 检查卡片内容不受干扰

### 视觉测试
1. 不同光线环境下的可读性
2. 多种设备屏幕的显示效果
3. 背景与内容的和谐度

## 🎯 最终效果

实现后的页面将呈现：
- 浪漫的情侣插画背景
- 清晰的功能界面
- 优雅的毛玻璃卡片效果
- 完美的内容可读性

背景图片将成为页面的点睛之笔，既增加了视觉美感，又不会影响用户的正常使用体验。

## 当前状态
✅ 背景图片文件：`couple-background.png` (750x1333px, 226KB)  
✅ 代码已更新：支持背景图片显示  
🔧 调试模式：已添加调试信息帮助排查问题  

## 故障排除步骤

### 1. 检查调试信息
重新编译后，首页应该显示：
- 左上角红色"背景层"文字 → 证明背景层容器正常
- 图片加载成功/失败的Toast提示

### 2. 如果看不到背景图片，请尝试以下方法：

#### 方法A：清除缓存
1. 微信开发者工具 → 工具 → 清除缓存
2. 重新编译项目
3. 刷新模拟器

#### 方法B：检查图片路径
确认以下路径都能正常访问：
- `/images/couple-background.png`
- `../../images/couple-background.png`

#### 方法C：使用网络图片（临时测试）
如果本地图片不显示，可以临时使用网络图片测试：
```wxml
<image 
  class="background-image" 
  src="https://example.com/your-image.jpg" 
  mode="aspectFill">
</image>
```

#### 方法D：转换为Base64（最稳定）
将图片转换为Base64编码内嵌到CSS中：
```css
.background-layer {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgA...');
}
```

### 3. 自定义调整参数

#### 透明度调整
在 `index.wxss` 中修改：
```css
.background-image {
  opacity: 0.5; /* 0.1-1.0，数值越小越透明 */
}

.background-overlay {
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.8) 0%,  /* 调整这些数值 */
    rgba(255, 255, 255, 0.9) 100%
  );
}
```

#### 模糊程度调整
```css
.background-image {
  filter: blur(2px); /* 0-5px，数值越大越模糊 */
}
```

#### 位置调整
```css
.background-image {
  object-position: center top; /* 调整图片显示位置 */
}
```

## 预期效果
- 背景图片应该覆盖整个页面
- 图片会有轻微模糊效果（1px）
- 透明度设置为80%
- 白色渐变遮罩确保内容可读性
- 所有卡片都有半透明毛玻璃效果

## 技术说明
- 使用 `position: fixed` 确保背景图片固定
- 使用 `z-index` 层级管理确保正确显示顺序
- 使用 `pointer-events: none` 防止背景阻挡点击
- 支持响应式设计，小屏幕自动调整透明度

## 如果问题持续存在
请检查：
1. 微信开发者工具版本是否最新
2. 图片文件是否损坏
3. 项目是否正确编译
4. 是否有其他CSS样式冲突

---
最后更新：2025-01-19  
版本：v2.5.4 深度优化版 