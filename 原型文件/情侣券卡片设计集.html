<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情侣信誉券 - 卡片设计集</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }
        
        .header h1 {
            font-size: 36px;
            margin-bottom: 16px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 18px;
            opacity: 0.9;
        }
        
        .design-section {
            margin-bottom: 80px;
        }
        
        .section-title {
            color: white;
            font-size: 24px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(to right, #FF6D96, #FFD700);
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .design-description {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            color: white;
            margin-top: 20px;
        }
        
        /* 卡片基础样式 */
        .coupon-card {
            width: 320px;
            /* height: 200px; */
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 auto;
            /* padding-bottom: 20px; */
            box-sizing: border-box;
        }
        
        .coupon-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        /* 1. 经典渐变系列 */
        .gradient-pink {
            background: linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%);
            color: white;
        }
        
        .gradient-blue {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .gradient-gold {
            background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
            color: white;
        }
        
        /* 2. 玻璃拟态系列 */
        .glass-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .glass-dark {
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        /* 3. 简约系列 */
        .minimal-white {
            background: #FFFFFF;
            color: #333333;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .minimal-dark {
            background: #2C2C2C;
            color: #FFFFFF;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        /* 4. 奢华系列 */
        .luxury-gold {
            background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #FFA500 100%);
            color: #2C2C2C;
            position: relative;
        }
        
        .luxury-gold::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .luxury-purple {
            background: linear-gradient(135deg, #8A2BE2 0%, #9370DB 50%, #DDA0DD 100%);
            color: white;
        }
        
        /* 5. 可爱系列 */
        .cute-pink {
            background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
            color: #FF1493;
        }
        
        .cute-mint {
            background: linear-gradient(135deg, #98FB98 0%, #90EE90 100%);
            color: #006400;
        }
        
        /* 6. 3D效果系列 */
        .card-3d {
            background: linear-gradient(135deg, #FF6D96 0%, #FF8FA3 100%);
            color: white;
            transform: perspective(1000px) rotateX(5deg);
            box-shadow: 0 25px 50px rgba(255, 109, 150, 0.4);
        }
        
        .card-3d-blue {
            background: linear-gradient(135deg, #4A90E2 0%, #7BB3F0 100%);
            color: white;
            transform: perspective(1000px) rotateY(5deg);
            box-shadow: 0 25px 50px rgba(74, 144, 226, 0.4);
        }
        
        /* 装饰元素 */
        .pattern-dots {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 2px, transparent 2px),
                              radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 2px, transparent 2px);
            background-size: 40px 40px;
            z-index: 1;
        }
        
        .pattern-lines {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            z-index: 1;
        }
        
        /* 心形装饰 */
        .heart-decoration {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            opacity: 0.3;
        }
        
        .heart-decoration::before,
        .heart-decoration::after {
            content: '';
            position: absolute;
            width: 10px;
            height: 16px;
            background: currentColor;
            border-radius: 10px 10px 0 0;
            transform: rotate(-45deg);
            transform-origin: 0 100%;
        }
        
        .heart-decoration::after {
            left: 8px;
            transform: rotate(45deg);
            transform-origin: 100% 100%;
        }
        
        /* 卡片内容布局 */
        .coupon-content {
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            z-index: 2;
        }
        
        .coupon-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .coupon-number {
            font-size: 12px;
            opacity: 0.8;
            font-weight: 500;
        }
        
        .coupon-type {
            font-size: 10px;
            opacity: 0.7;
            padding: 2px 8px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
        }
        
        .coupon-icon {
            text-align: center;
            margin: 10px 0;
            font-size: 24px;
        }
        
        .coupon-title {
            font-size: 20px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 8px;
        }
        
        .coupon-description {
            font-size: 12px;
            text-align: center;
            opacity: 0.9;
            line-height: 1.4;
            margin-bottom: 15px;
        }
        
        .coupon-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            opacity: 0.8;
            
        }
        
        /* 装饰元素 */
        .pattern-dots {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 2px, transparent 2px),
                              radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 2px, transparent 2px);
            background-size: 40px 40px;
            z-index: 1;
        }
        
        .pattern-lines {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: linear-gradient(45deg, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            z-index: 1;
        }
        
        /* 心形装饰 */
        .heart-decoration {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 20px;
            height: 20px;
            opacity: 0.3;
        }
        
        .heart-decoration::before,
        .heart-decoration::after {
            content: '';
            position: absolute;
            width: 10px;
            height: 16px;
            background: currentColor;
            border-radius: 10px 10px 0 0;
            transform: rotate(-45deg);
            transform-origin: 0 100%;
        }
        
        .heart-decoration::after {
            left: 8px;
            transform: rotate(45deg);
            transform-origin: 100% 100%;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .cards-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .coupon-card {
                width: 100%;
                max-width: 320px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💕 情侣信誉券卡片设计集</h1>
            <p>多种风格的高级卡片设计方案</p>
        </div>
        
        <!-- 1. 经典渐变系列 -->
        <div class="design-section">
            <h2 class="section-title">🌈 经典渐变系列</h2>
            <div class="cards-grid">
                <div class="coupon-card gradient-pink">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">服务券</span>
                            <span class="coupon-number">No.001</span>
                        </div>
                        <div class="coupon-icon">🎁</div>
                        <h3 class="coupon-title">爱心早餐券</h3>
                        <p class="coupon-description">承诺为你制作一顿温馨早餐<br>包含你最爱的荷包蛋和小粥</p>
                        <div class="coupon-footer">
                            <span>发券人：大宝贝</span>
                            <span>有效期至：2024-02-14</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card gradient-blue">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">体验券</span>
                            <span class="coupon-number">No.002</span>
                        </div>
                        <div class="coupon-icon">🌙</div>
                        <h3 class="coupon-title">星空约会券</h3>
                        <p class="coupon-description">一起在阳台看星星数月亮<br>聊聊我们的小秘密</p>
                        <div class="coupon-footer">
                            <span>发券人：小甜心</span>
                            <span>有效期至：2024-03-01</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card gradient-gold">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">奖励券</span>
                            <span class="coupon-number">No.003</span>
                        </div>
                        <div class="coupon-icon">👑</div>
                        <h3 class="coupon-title">公主特权券</h3>
                        <p class="coupon-description">今天你就是小公主<br>所有要求都要被满足</p>
                        <div class="coupon-footer">
                            <span>发券人：骑士先生</span>
                            <span>有效期至：2024-02-20</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>经典渐变系列采用温暖的色彩过渡，营造浪漫氛围。粉色象征甜蜜爱情，蓝色代表深邃情感，金色寓意珍贵承诺。渐变效果让卡片更有层次感和现代感。</p>
            </div>
        </div>
        
        <!-- 2. 玻璃拟态系列 -->
        <div class="design-section">
            <h2 class="section-title">🔮 玻璃拟态系列</h2>
            <div class="cards-grid">
                <div class="coupon-card glass-card">
                    <div class="pattern-dots"></div>
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">按摩券</span>
                            <span class="coupon-number">No.004</span>
                        </div>
                        <div class="coupon-icon">💆‍♀️</div>
                        <h3 class="coupon-title">肩颈舒缓券</h3>
                        <p class="coupon-description">专业手法按摩肩颈<br>帮你缓解一天的疲劳</p>
                        <div class="coupon-footer">
                            <span>发券人：贴心宝贝</span>
                            <span>有效期至：2024-02-25</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card glass-dark">
                    <div class="pattern-lines"></div>
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">游戏券</span>
                            <span class="coupon-number">No.005</span>
                        </div>
                        <div class="coupon-icon">🎮</div>
                        <h3 class="coupon-title">游戏时光券</h3>
                        <p class="coupon-description">不限时间畅玩游戏<br>今晚你说了算</p>
                        <div class="coupon-footer">
                            <span>发券人：游戏管家</span>
                            <span>有效期至：2024-02-28</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>玻璃拟态设计追求现代科技感，通过半透明效果和模糊背景营造未来感。点状和线性装饰图案增加设计细节，整体风格简约而高级。</p>
            </div>
        </div>
        
        <!-- 3. 简约系列 -->
        <div class="design-section">
            <h2 class="section-title">⚪ 简约系列</h2>
            <div class="cards-grid">
                <div class="coupon-card minimal-white">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type" style="background: #FF6D96; color: white;">家务券</span>
                            <span class="coupon-number">No.006</span>
                        </div>
                        <div class="coupon-icon">🧹</div>
                        <h3 class="coupon-title">家务免做券</h3>
                        <p class="coupon-description">本周所有家务全包<br>你只需要美美地休息</p>
                        <div class="coupon-footer">
                            <span>发券人：勤劳小蜜蜂</span>
                            <span>有效期至：2024-03-10</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card minimal-dark">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type" style="background: #FFD700; color: #333;">电影券</span>
                            <span class="coupon-number">No.007</span>
                        </div>
                        <div class="coupon-icon">🎬</div>
                        <h3 class="coupon-title">私人影院券</h3>
                        <p class="coupon-description">在家打造专属影院<br>一起看你想看的电影</p>
                        <div class="coupon-footer">
                            <span>发券人：影院老板</span>
                            <span>有效期至：2024-02-29</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>简约系列去除多余装饰，专注于内容本身。黑白对比突出信息层次，彩色标签作为点缀增加活力。适合追求极简美学的用户。</p>
            </div>
        </div>
        
        <!-- 4. 奢华系列 -->
        <div class="design-section">
            <h2 class="section-title">✨ 奢华系列</h2>
            <div class="cards-grid">
                <div class="coupon-card luxury-gold">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type" style="background: rgba(0,0,0,0.2); color: white;">钻石券</span>
                            <span class="coupon-number">No.008</span>
                        </div>
                        <div class="coupon-icon">💎</div>
                        <h3 class="coupon-title">购物陪伴券</h3>
                        <p class="coupon-description">全程陪你逛街购物<br>包包、鞋子随你选</p>
                        <div class="coupon-footer">
                            <span>发券人：专属司机</span>
                            <span>有效期至：2024-03-15</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card luxury-purple">
                    <div class="heart-decoration"></div>
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">皇家券</span>
                            <span class="coupon-number">No.009</span>
                        </div>
                        <div class="coupon-icon">👸</div>
                        <h3 class="coupon-title">女王待遇券</h3>
                        <p class="coupon-description">今天你是我的女王<br>所有愿望都将实现</p>
                        <div class="coupon-footer">
                            <span>发券人：忠诚骑士</span>
                            <span>有效期至：2024-02-26</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>奢华系列采用金色和紫色等高贵色彩，搭配华丽的装饰元素。金色代表珍贵和尊贵，紫色象征神秘和优雅。适合特殊场合和重要时刻。</p>
            </div>
        </div>
        
        <!-- 5. 可爱系列 -->
        <div class="design-section">
            <h2 class="section-title">🌸 可爱系列</h2>
            <div class="cards-grid">
                <div class="coupon-card cute-pink">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">萌宠券</span>
                            <span class="coupon-number">No.010</span>
                        </div>
                        <div class="coupon-icon">🐱</div>
                        <h3 class="coupon-title">撒娇特权券</h3>
                        <p class="coupon-description">今天可以无限撒娇<br>所有可爱都被允许</p>
                        <div class="coupon-footer">
                            <span>发券人：宠溺专家</span>
                            <span>有效期至：2024-02-22</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card cute-mint">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">甜品券</span>
                            <span class="coupon-number">No.011</span>
                        </div>
                        <div class="coupon-icon">🍰</div>
                        <h3 class="coupon-title">甜品制作券</h3>
                        <p class="coupon-description">一起制作专属甜品<br>甜蜜回忆加倍</p>
                        <div class="coupon-footer">
                            <span>发券人：甜品师傅</span>
                            <span>有效期至：2024-03-05</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>可爱系列使用柔和的粉色和薄荷绿，搭配萌系图标和童趣文案。色彩饱和度适中，营造温馨可爱的氛围，适合年轻情侣和活泼个性。</p>
            </div>
        </div>
        
        <!-- 6. 3D效果系列 -->
        <div class="design-section">
            <h2 class="section-title">🎭 3D效果系列</h2>
            <div class="cards-grid">
                <div class="coupon-card card-3d">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">VIP券</span>
                            <span class="coupon-number">No.012</span>
                        </div>
                        <div class="coupon-icon">💝</div>
                        <h3 class="coupon-title">惊喜礼物券</h3>
                        <p class="coupon-description">神秘礼物即将揭晓<br>期待值拉满</p>
                        <div class="coupon-footer">
                            <span>发券人：惊喜制造者</span>
                            <span>有效期至：2024-02-24</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card card-3d-blue">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">旅行券</span>
                            <span class="coupon-number">No.013</span>
                        </div>
                        <div class="coupon-icon">✈️</div>
                        <h3 class="coupon-title">周末小旅行券</h3>
                        <p class="coupon-description">说走就走的浪漫之旅<br>两个人的专属时光</p>
                        <div class="coupon-footer">
                            <span>发券人：旅行达人</span>
                            <span>有效期至：2024-03-20</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>3D效果系列通过透视变换和阴影效果营造立体感，让卡片仿佛悬浮在空中。视觉冲击力强，适合重要承诺和特殊礼物，增加仪式感和珍贵感。</p>
            </div>
        </div>
        
        <!-- 设计总结 -->
        <div class="design-section">
            <div class="design-description">
                <h3 style="margin-bottom: 20px;">📝 设计总结与建议</h3>
                
                <h4>🎯 核心设计元素：</h4>
                <ul style="margin: 10px 0 20px 20px; line-height: 1.6;">
                    <li><strong>券编号</strong>：增加收藏感和专属感</li>
                    <li><strong>券类型标签</strong>：快速识别券的用途</li>
                    <li><strong>主题图标</strong>：直观表达券的内容</li>
                    <li><strong>券名称</strong>：简洁有吸引力的标题</li>
                    <li><strong>描述文案</strong>：温馨的承诺说明</li>
                    <li><strong>发券人</strong>：增加人情味和亲密感</li>
                    <li><strong>有效期</strong>：营造紧迫感和珍贵感</li>
                </ul>
                
                <h4>🎨 风格选择建议：</h4>
                <ul style="margin: 10px 0 20px 20px; line-height: 1.6;">
                    <li><strong>经典渐变</strong>：适合日常使用，温馨浪漫</li>
                    <li><strong>玻璃拟态</strong>：现代感强，年轻人喜爱</li>
                    <li><strong>简约风格</strong>：百搭经典，不过时</li>
                    <li><strong>奢华风格</strong>：特殊场合，增加仪式感</li>
                    <li><strong>可爱风格</strong>：活泼有趣，增加亲密感</li>
                    <li><strong>3D效果</strong>：视觉冲击强，科技感足</li>
                </ul>
                
                <h4>💡 实现建议：</h4>
                <ul style="margin: 10px 0; line-height: 1.6;">
                    <li>支持用户自定义选择卡片风格</li>
                    <li>根据券类型自动匹配合适的设计风格</li>
                    <li>特殊节日可推出限定主题卡片</li>
                    <li>添加动画效果增强交互体验</li>
                    <li>考虑深色模式适配</li>
                </ul>
            </div>
        </div>
        
        <!-- 2. 玻璃拟态系列 -->
        <div class="design-section">
            <h2 class="section-title">🔮 玻璃拟态系列</h2>
            <div class="cards-grid">
                <div class="coupon-card glass-card">
                    <div class="pattern-dots"></div>
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">按摩券</span>
                            <span class="coupon-number">No.004</span>
                        </div>
                        <div class="coupon-icon">💆‍♀️</div>
                        <h3 class="coupon-title">肩颈舒缓券</h3>
                        <p class="coupon-description">专业手法按摩肩颈<br>帮你缓解一天的疲劳</p>
                        <div class="coupon-footer">
                            <span>发券人：贴心宝贝</span>
                            <span>有效期至：2024-02-25</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card glass-dark">
                    <div class="pattern-lines"></div>
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">游戏券</span>
                            <span class="coupon-number">No.005</span>
                        </div>
                        <div class="coupon-icon">🎮</div>
                        <h3 class="coupon-title">游戏时光券</h3>
                        <p class="coupon-description">不限时间畅玩游戏<br>今晚你说了算</p>
                        <div class="coupon-footer">
                            <span>发券人：游戏管家</span>
                            <span>有效期至：2024-02-28</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>玻璃拟态设计追求现代科技感，通过半透明效果和模糊背景营造未来感。点状和线性装饰图案增加设计细节，整体风格简约而高级。</p>
            </div>
        </div>
        
        <!-- 3. 简约系列 -->
        <div class="design-section">
            <h2 class="section-title">⚪ 简约系列</h2>
            <div class="cards-grid">
                <div class="coupon-card minimal-white">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type" style="background: #FF6D96; color: white;">家务券</span>
                            <span class="coupon-number">No.006</span>
                        </div>
                        <div class="coupon-icon">🧹</div>
                        <h3 class="coupon-title">家务免做券</h3>
                        <p class="coupon-description">本周所有家务全包<br>你只需要美美地休息</p>
                        <div class="coupon-footer">
                            <span>发券人：勤劳小蜜蜂</span>
                            <span>有效期至：2024-03-10</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card minimal-dark">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type" style="background: #FFD700; color: #333;">电影券</span>
                            <span class="coupon-number">No.007</span>
                        </div>
                        <div class="coupon-icon">🎬</div>
                        <h3 class="coupon-title">私人影院券</h3>
                        <p class="coupon-description">在家打造专属影院<br>一起看你想看的电影</p>
                        <div class="coupon-footer">
                            <span>发券人：影院老板</span>
                            <span>有效期至：2024-02-29</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>简约系列去除多余装饰，专注于内容本身。黑白对比突出信息层次，彩色标签作为点缀增加活力。适合追求极简美学的用户。</p>
            </div>
        </div>
        
        <!-- 4. 奢华系列 -->
        <div class="design-section">
            <h2 class="section-title">✨ 奢华系列</h2>
            <div class="cards-grid">
                <div class="coupon-card luxury-gold">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type" style="background: rgba(0,0,0,0.2); color: white;">钻石券</span>
                            <span class="coupon-number">No.008</span>
                        </div>
                        <div class="coupon-icon">💎</div>
                        <h3 class="coupon-title">购物陪伴券</h3>
                        <p class="coupon-description">全程陪你逛街购物<br>包包、鞋子随你选</p>
                        <div class="coupon-footer">
                            <span>发券人：专属司机</span>
                            <span>有效期至：2024-03-15</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card luxury-purple">
                    <div class="heart-decoration"></div>
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">皇家券</span>
                            <span class="coupon-number">No.009</span>
                        </div>
                        <div class="coupon-icon">👸</div>
                        <h3 class="coupon-title">女王待遇券</h3>
                        <p class="coupon-description">今天你是我的女王<br>所有愿望都将实现</p>
                        <div class="coupon-footer">
                            <span>发券人：忠诚骑士</span>
                            <span>有效期至：2024-02-26</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>奢华系列采用金色和紫色等高贵色彩，搭配华丽的装饰元素。金色代表珍贵和尊贵，紫色象征神秘和优雅。适合特殊场合和重要时刻。</p>
            </div>
        </div>
        
        <!-- 5. 可爱系列 -->
        <div class="design-section">
            <h2 class="section-title">🌸 可爱系列</h2>
            <div class="cards-grid">
                <div class="coupon-card cute-pink">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">萌宠券</span>
                            <span class="coupon-number">No.010</span>
                        </div>
                        <div class="coupon-icon">🐱</div>
                        <h3 class="coupon-title">撒娇特权券</h3>
                        <p class="coupon-description">今天可以无限撒娇<br>所有可爱都被允许</p>
                        <div class="coupon-footer">
                            <span>发券人：宠溺专家</span>
                            <span>有效期至：2024-02-22</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card cute-mint">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">甜品券</span>
                            <span class="coupon-number">No.011</span>
                        </div>
                        <div class="coupon-icon">🍰</div>
                        <h3 class="coupon-title">甜品制作券</h3>
                        <p class="coupon-description">一起制作专属甜品<br>甜蜜回忆加倍</p>
                        <div class="coupon-footer">
                            <span>发券人：甜品师傅</span>
                            <span>有效期至：2024-03-05</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>可爱系列使用柔和的粉色和薄荷绿，搭配萌系图标和童趣文案。色彩饱和度适中，营造温馨可爱的氛围，适合年轻情侣和活泼个性。</p>
            </div>
        </div>
        
        <!-- 6. 3D效果系列 -->
        <div class="design-section">
            <h2 class="section-title">🎭 3D效果系列</h2>
            <div class="cards-grid">
                <div class="coupon-card card-3d">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">VIP券</span>
                            <span class="coupon-number">No.012</span>
                        </div>
                        <div class="coupon-icon">💝</div>
                        <h3 class="coupon-title">惊喜礼物券</h3>
                        <p class="coupon-description">神秘礼物即将揭晓<br>期待值拉满</p>
                        <div class="coupon-footer">
                            <span>发券人：惊喜制造者</span>
                            <span>有效期至：2024-02-24</span>
                        </div>
                    </div>
                </div>
                
                <div class="coupon-card card-3d-blue">
                    <div class="coupon-content">
                        <div class="coupon-header">
                            <span class="coupon-type">旅行券</span>
                            <span class="coupon-number">No.013</span>
                        </div>
                        <div class="coupon-icon">✈️</div>
                        <h3 class="coupon-title">周末小旅行券</h3>
                        <p class="coupon-description">说走就走的浪漫之旅<br>两个人的专属时光</p>
                        <div class="coupon-footer">
                            <span>发券人：旅行达人</span>
                            <span>有效期至：2024-03-20</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="design-description">
                <h4>💡 设计理念：</h4>
                <p>3D效果系列通过透视变换和阴影效果营造立体感，让卡片仿佛悬浮在空中。视觉冲击力强，适合重要承诺和特殊礼物，增加仪式感和珍贵感。</p>
            </div>
        </div>
        
        <!-- 设计总结 -->
        <div class="design-section">
            <div class="design-description">
                <h3 style="margin-bottom: 20px;">📝 设计总结与建议</h3>
                
                <h4>🎯 核心设计元素：</h4>
                <ul style="margin: 10px 0 20px 20px; line-height: 1.6;">
                    <li><strong>券编号</strong>：增加收藏感和专属感</li>
                    <li><strong>券类型标签</strong>：快速识别券的用途</li>
                    <li><strong>主题图标</strong>：直观表达券的内容</li>
                    <li><strong>券名称</strong>：简洁有吸引力的标题</li>
                    <li><strong>描述文案</strong>：温馨的承诺说明</li>
                    <li><strong>发券人</strong>：增加人情味和亲密感</li>
                    <li><strong>有效期</strong>：营造紧迫感和珍贵感</li>
                </ul>
                
                <h4>🎨 风格选择建议：</h4>
                <ul style="margin: 10px 0 20px 20px; line-height: 1.6;">
                    <li><strong>经典渐变</strong>：适合日常使用，温馨浪漫</li>
                    <li><strong>玻璃拟态</strong>：现代感强，年轻人喜爱</li>
                    <li><strong>简约风格</strong>：百搭经典，不过时</li>
                    <li><strong>奢华风格</strong>：特殊场合，增加仪式感</li>
                    <li><strong>可爱风格</strong>：活泼有趣，增加亲密感</li>
                    <li><strong>3D效果</strong>：视觉冲击强，科技感足</li>
                </ul>
                
                <h4>💡 实现建议：</h4>
                <ul style="margin: 10px 0; line-height: 1.6;">
                    <li>支持用户自定义选择卡片风格</li>
                    <li>根据券类型自动匹配合适的设计风格</li>
                    <li>特殊节日可推出限定主题卡片</li>
                    <li>添加动画效果增强交互体验</li>
                    <li>考虑深色模式适配</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // 卡片交互效果
        document.querySelectorAll('.coupon-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.animationPlayState = 'paused';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.animationPlayState = 'running';
            });
            
            // 点击效果
            card.addEventListener('click', function() {
                this.style.transform += ' scale(0.95)';
                setTimeout(() => {
                    this.style.transform = this.style.transform.replace(' scale(0.95)', '');
                }, 150);
            });
        });
        
        // 页面滚动动画
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // 观察所有卡片
        document.querySelectorAll('.coupon-card').forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
            observer.observe(card);
        });
    </script>
</body>
</html> 