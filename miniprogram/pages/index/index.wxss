/* pages/index/index.wxss */
@import "../../app.wxss";

/* 页面容器 - 新增背景图片支持 */
.page-container {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  background: #f5f5f5; /* 添加默认背景色，防止透明 */
}

/* 背景图片层 - 使用image标签方式 */
.background-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0;
  pointer-events: none;
}

.background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(0px); /* 轻微模糊，减少干扰 */
  opacity: 1; /* 调整透明度 */
}

/* 背景遮罩层 - 确保内容可读性 */
.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    /* rgba(255, 255, 255, 0.65) 0%,    减少透明度，让背景更明显 */
    /* rgba(255, 255, 255, 0.70) 30%,   中间适度遮罩 */
    /* rgba(255, 255, 255, 0.75) 70%,   底部更不透明，确保内容清晰 */
    /* rgba(255, 255, 255, 0.80) 100% */
  );
  z-index: 2;
}

/* 内容容器调整 */
.container {
  position: relative;
  z-index: 3; /* 确保内容在背景之上 */
  min-height: 100vh;
  padding-bottom: 40rpx;
  background: transparent !important; /* 强制透明背景 */
}

/* 卡片样式增强 - 提高可读性 */
.card {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 182, 193, 0.2);
  box-shadow: 
    0 8rpx 32rpx rgba(255, 105, 180, 0.1),
    0 2rpx 8rpx rgba(255, 105, 180, 0.05);
}

/* 头部区域样式优化 */
.header-section {
  background: rgba(255, 255, 255, 0.92) !important;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 182, 193, 0.3);
  margin: 20rpx 20rpx 0 20rpx;
}

/* 筛选标签样式增强 */
.filter-tabs {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15rpx);
  margin: 20rpx;
  border-radius: 25rpx;
  padding: 8rpx;
  border: 1rpx solid rgba(255, 182, 193, 0.2);
}

.filter-tabs .tab {
  background: transparent;
  color: #666;
  transition: all 0.3s ease;
}

.filter-tabs .tab.active {
  background: rgba(255, 105, 180, 0.9);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.3);
}

/* 券卡片容器优化 */
.coupons-scroll-view {
  background: transparent;
}

.coupons-grid {
  padding: 0 20rpx;
}

/* 空状态样式增强 */
.empty-state {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(15rpx);
  margin: 40rpx 20rpx;
}

/* 绑定提示区域优化 */
.bind-prompt .prompt-card {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 182, 193, 0.3);
}

/* 按钮样式增强 */
.primary-button {
  background: linear-gradient(135deg, #ff69b4, #ffb6c1) !important;
  box-shadow: 
    0 8rpx 24rpx rgba(255, 105, 180, 0.3),
    0 2rpx 8rpx rgba(255, 105, 180, 0.2);
  border: none;
  backdrop-filter: blur(10rpx);
}

.primary-button:active {
  transform: scale(0.98);
  box-shadow: 
    0 4rpx 16rpx rgba(255, 105, 180, 0.4),
    0 1rpx 4rpx rgba(255, 105, 180, 0.3);
}

/* 模板券样式优化 */
.template-coupon-wrapper {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid rgba(255, 182, 193, 0.2);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-coupon-wrapper:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(255, 105, 180, 0.2);
  border-color: rgba(255, 105, 180, 0.4);
}

.template-coupon-wrapper:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .background-overlay {
    background: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.70) 0%,
      rgba(255, 255, 255, 0.75) 50%,
      rgba(255, 255, 255, 0.80) 100%
    );
  }
  
  .background-image {
    opacity: 1; /* 小屏幕上更透明一些 */
  }
}

/* 原有样式保持 */
.app-info-top {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid #ffb6c1;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.3);
}

/* 新登录界面样式 */
.login-prompt {
  padding: 40rpx 32rpx;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 180, 0.15);
  width: 100%;
  max-width: 600rpx;
}

.features-section {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.feature-icon {
  font-size: 40rpx;
  line-height: 1;
}

.feature-text {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.feature-desc {
  font-size: 20rpx;
  color: #999;
}

.login-title {
  text-align: center;
  margin-bottom: 32rpx;
}

.title-main {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.title-sub {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.login-btn {
  width: 100%;
  background: linear-gradient(135deg, #ff69b4, #ffb6c1);
  border: none;
  border-radius: 50rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.3);
  margin-bottom: 24rpx;
}

.login-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.4);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.btn-icon {
  font-size: 32rpx;
}

.btn-text {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

.login-tip {
  text-align: center;
}

.login-tip text {
  display: block;
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
}

/* 绑定提示样式 */
.bind-prompt {
  padding: 40rpx 32rpx;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Header Section */
.header-section {
  /* display: flex; */
  align-items: center;
  padding: 40rpx 30rpx;
  gap: 20rpx;
  position: relative;
}

.app-logo {
  width: 90rpx;
  height: 90rpx;
  border-radius: 20rpx;
  flex-shrink: 0;
  display: inline-block;
}

.app-greeting {
  flex-grow: 1;
}

.greeting-main {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.greeting-sub {
  display: block;
  font-size: 26rpx;
  color: #666;
}

.create-coupon-btn-header {
  margin-top: 80rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  border-radius: 50rpx;
  background-color: #ff69b4;
  color: white;
  font-weight: bold;
  border-color: #ff69b4;
}
.btn-icon {
  width: 32rpx;
  height: 32rpx;
}

/* Prompt Cards */
.prompt-card {
  padding: 60rpx 40rpx;
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}
.prompt-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
}
.prompt-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.prompt-message {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  max-width: 80%;
}

.bind-btn {
  margin-top: 20rpx;
  width: 70%;
  padding: 22rpx;
}

/* Filter Tabs */
.filter-tabs {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  margin-bottom: 30rpx;
  padding: 20rpx 0;
}
.tab {
  font-size: 28rpx;
  color: #888;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}
.tab.active {
  font-weight: bold;
  color: #ff69b4;
  background-color: rgba(255, 105, 180, 0.1);
}

/* Coupons Scroll View & Grid */
.coupons-scroll-view {
  height: calc(100vh - 500rpx); /* Adjust based on header/tabbar/prompts */
  padding-bottom: 50rpx;
}
.coupons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280rpx, 1fr));
  gap: 25rpx;
}
.coupon-card-wrapper {
  perspective: 1000px;
}

.coupon-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(255, 182, 193, 0.3); /* Pinkish shadow */
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-height: 280rpx;
  justify-content: space-between;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.coupon-card.interactive-card:hover {
  /* For web, or use :active for touch */
  transform: translateY(-5rpx) rotateX(5deg);
  box-shadow: 0 12rpx 35rpx rgba(255, 105, 180, 0.4);
}
.coupon-icon-placeholder {
  font-size: 60rpx;
  margin-bottom: 15rpx;
  line-height: 1;
}
.coupon-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #444;
  margin-bottom: 10rpx;
  line-height: 1.3;
}
.coupon-sender {
  font-size: 22rpx;
  color: #777;
  margin-bottom: 10rpx;
}
.coupon-status-badge {
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 15rpx;
  color: #fff;
  background-color: #ff69b4; /* Default */
}
.coupon-status-badge.received {
  background-color: #ffa500;
} /* Orange */
.coupon-status-badge.pending_redeem {
  background-color: #4caf50;
} /* Green */

.template-card {
  border: 2rpx dashed #ffb6c1;
}
.coupon-action-chip {
  font-size: 20rpx;
  color: #ff69b4;
  background-color: rgba(255, 105, 180, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  margin-top: 15rpx;
}

/* Empty State & Loading */
.loading-placeholder,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
  color: #888;
  min-height: 200rpx;
}
.empty-icon {
  width: 100rpx;
  height: 100rpx;
  opacity: 0.6;
  margin-bottom: 20rpx;
}
.empty-text {
  font-size: 26rpx;
  line-height: 1.5;
}
.bg-pink-100 {
  background: linear-gradient(135deg, #fff1f2, #ffe4e6);
}
.bg-blue-100 {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
}
.bg-green-100 {
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
}
.bg-gray-100 {
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
}

.coupon-list-container {
  padding: 0 20rpx; /* 控制左右边距 */
  box-sizing: border-box;
}

/* 首页样式 */

/* 券卡片网格布局 */
.coupons-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 0 24rpx;
}

/* 首页券卡片自定义样式 */
.index-coupon-card {
  margin-bottom: 16rpx;
  transform: scale(1);
  transition: transform 0.2s ease;
}

.index-coupon-card:active {
  transform: scale(0.98);
}

/* 模板券卡片包装 */
.template-coupon-wrapper {
  position: relative;
  margin-bottom: 16rpx;
}

/* 模板券卡片自定义样式 */
.template-coupon-card {
  position: relative;
  border: 2rpx dashed rgba(255, 105, 180, 0.3);
  background: linear-gradient(135deg, 
    rgba(255, 182, 193, 0.1) 0%, 
    rgba(255, 192, 203, 0.1) 50%, 
    rgba(240, 248, 255, 0.1) 100%);
}

.template-coupon-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    transparent 25%, 
    rgba(255, 105, 180, 0.05) 25%, 
    rgba(255, 105, 180, 0.05) 50%, 
    transparent 50%, 
    transparent 75%, 
    rgba(255, 105, 180, 0.05) 75%);
  background-size: 20rpx 20rpx;
  border-radius: inherit;
  pointer-events: none;
}

/* 模板操作按钮区域 */
.template-actions {
  margin-top: 12rpx;
  padding: 0 12rpx;
}

/* 模版按钮样式已移除 - 改为点击整个卡片 */

/* 模板标识 */
.template-badge {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  background: rgba(255, 105, 180, 0.9);
  color: white;
  font-size: 20rpx;
  font-weight: 500;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  z-index: 5;
  box-shadow: 0 2rpx 6rpx rgba(255, 105, 180, 0.3);
}

/* 券滚动视图 */
.coupons-scroll-view {
  height: calc(100vh - 300rpx);
  padding-bottom: 40rpx;
}

/* 空状态样式优化 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  margin: 40rpx 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 筛选标签样式优化 */
.filter-tabs {
  display: flex;
  justify-content: center;
  margin: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.filter-tabs .tab {
  flex: 1;
  text-align: center;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  position: relative;
}

.filter-tabs .tab.active {
  color: white;
  background: linear-gradient(135deg, #FF69B4 0%, #FF1493 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.4);
  transform: translateY(-2rpx);
}

.filter-tabs .tab:not(.active):active {
  background: rgba(255, 105, 180, 0.1);
  transform: scale(0.98);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .coupons-grid {
    padding: 0 16rpx;
    gap: 12rpx;
  }
  
  /* 模版按钮样式已移除 */
  
  .filter-tabs .tab {
    font-size: 26rpx;
    padding: 14rpx 20rpx;
  }
}

/* 加载状态 */
.loading-indicator {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 24rpx;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-coupon-wrapper,
.index-coupon-card {
  animation: fadeInUp 0.4s ease forwards;
}

.template-coupon-wrapper:nth-child(even) {
  animation-delay: 0.1s;
}

.template-coupon-wrapper:nth-child(odd) {
  animation-delay: 0.05s;
}

/* 情侣券缺省状态专用样式 */
.couple-empty-icon {
  width: 160rpx !important;
  height: 160rpx !important;
  opacity: 0.8 !important;
  margin-bottom: 30rpx !important;
  animation: gentle-pulse 3s ease-in-out infinite;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF69B4;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  opacity: 0.8;
}

/* 温柔的脉冲动画 */
@keyframes gentle-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}
