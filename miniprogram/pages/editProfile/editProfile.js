const app = getApp();

Page({
  data: {
    userInfo: null,
    tempAvatar: '',
    tempNickname: '',
    tempSignature: '', // 临时个性签名
    editingNickname: '', // 正在编辑的昵称
    editingSignature: '', // 正在编辑的个性签名
    isEditingNickname: false, // 是否正在编辑昵称
    isEditingSignature: false, // 是否正在编辑个性签名
    isModified: false,
    isLoading: false,
    isNicknameValid: true // 昵称是否通过验证
  },

  onLoad: function() {
    console.log('编辑页面加载，用户信息:', app.globalData.userInfo);
    
    this.initData();
  },

  /**
   * 初始化数据
   */
  initData: function() {
    const userInfo = app.globalData.userInfo;
    if (!userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }

    this.setData({
      userInfo: userInfo,
      tempAvatar: userInfo.avatarUrl || '',
      tempNickname: userInfo.nickName || '',
      tempSignature: userInfo.signature || ''
    });
  },

  /**
   * 编辑头像 - 卡片点击事件 (修复：不再显示弹窗，通过WXML中的button直接调用)
   */
  editAvatar: function() {
    // 不再显示弹窗，头像选择通过WXML中的button open-type="chooseAvatar"直接调用
    // 这个方法保留用于其他可能的头像相关逻辑
    console.log('头像编辑区域被点击，实际头像选择通过button触发');
  },

  /**
   * 选择头像
   */
  onChooseAvatar: function(e) {
    console.log('选择头像:', e.detail);
    const { avatarUrl } = e.detail;
    
    if (avatarUrl) {
      this.setData({
        tempAvatar: avatarUrl,
        isModified: true
      });
      
      wx.showToast({
        title: '头像已更新',
        icon: 'success',
        duration: 1500
      });
    }
  },

  /**
   * 开始编辑昵称 - 极简化
   */
  startEditNickname: function() {
    console.log('开始编辑昵称');
    
    this.setData({
      isEditingNickname: true,
      editingNickname: this.data.tempNickname || '',
      isNicknameValid: true
    });
  },

  /**
   * 点击编辑区域外部 - 退出编辑模式
   */
  onClickOutside: function() {
    if (this.data.isEditingNickname) {
      console.log('点击外部区域，退出昵称编辑模式');
      this.exitNicknameEditMode();
    }
    if (this.data.isEditingSignature) {
      console.log('点击外部区域，退出个性签名编辑模式');
      this.exitSignatureEditMode();
    }
  },

  /**
   * 退出昵称编辑模式
   */
  exitNicknameEditMode: function() {
    const nickname = (this.data.editingNickname || '').trim();
    
    // 如果有有效昵称，则更新临时昵称
    if (nickname) {
      this.setData({
        tempNickname: nickname,
        isEditingNickname: false,
        editingNickname: '',
        isModified: true
      });
      
      console.log('昵称已更新:', nickname);
    } else {
      // 如果没有输入内容，恢复原来的昵称
    this.setData({
        isEditingNickname: false,
        editingNickname: ''
    });
    }
  },

  /**
   * 退出个性签名编辑模式
   */
  exitSignatureEditMode: function() {
    const signature = (this.data.editingSignature || '').trim();
    
    // 如果有有效个性签名，则更新临时个性签名
    if (signature) {
    this.setData({
        tempSignature: signature,
        isEditingSignature: false,
        editingSignature: '',
        isModified: true
      });
      
      console.log('个性签名已更新:', signature);
    } else {
      // 如果没有输入内容，恢复原来的个性签名
    this.setData({
        isEditingSignature: false,
        editingSignature: ''
    });
    }
  },

  /**
   * 防止点击编辑区域时触发外部点击事件
   */
  preventClose: function(e) {
    // 这个函数通过catchtap在WXML中阻止事件冒泡
    // 无需在JS中编写额外代码
    console.log('点击编辑区域内部，阻止冒泡');
  },

  /**
   * 昵称输入事件
   */
  onNicknameInput: function(e) {
    const inputValue = e.detail.value;
    console.log('昵称输入:', inputValue);

    this.setData({
      editingNickname: inputValue,
      isNicknameValid: true,
      isModified: true // 标记为已修改
    });
  },

  /**
   * 昵称审核事件 - 最终修复版
   * @description 此事件主要用于检查微信返回的昵称是否合规。
   *              昵称的实际内容是通过 onNicknameInput 事件获取的。
   */
  onNicknameReview: function(e) {
    console.log('昵称审核事件:', e.detail);
    const { pass } = e.detail;
    
        this.setData({
      isNicknameValid: pass,
    });

    if (!pass) {
        wx.showToast({
          title: '昵称包含敏感内容，请修改',
          icon: 'none',
          duration: 2000
        });
      }
    // 不再显示获取成功或失败的提示，避免干扰
  },

  /**
   * 开始编辑个性签名
   */
  startEditSignature: function() {
    console.log('开始编辑个性签名');
    
    this.setData({
      isEditingSignature: true,
      editingSignature: this.data.tempSignature || ''
    });
  },

  /**
   * 个性签名输入事件
   */
  onSignatureInput: function(e) {
    const inputValue = e.detail.value;
    console.log('个性签名输入:', inputValue);

    this.setData({
      editingSignature: inputValue,
      isModified: true // 标记为已修改
    });
  },

  /**
   * 保存资料 - 优化验证逻辑，在保存时进行最终校验
   */
  saveProfile: function() {
    if (!this.data.isModified) {
      wx.showToast({
        title: '没有修改内容',
        icon: 'none'
      });
      return;
    }

    const { tempAvatar, tempNickname, tempSignature } = this.data;
    
    // 验证昵称
    const nickname = (tempNickname || '').trim();
    if (!nickname) {
      wx.showToast({
        title: '请设置昵称',
        icon: 'none'
      });
      return;
    }

    if (nickname.length > 20) {
      wx.showToast({
        title: '昵称不能超过20个字符',
        icon: 'none'
      });
      return;
    }

    // 如果昵称包含敏感内容，再次提醒用户
    if (!this.data.isNicknameValid) {
      wx.showModal({
        title: '昵称确认',
        content: '当前昵称可能包含敏感内容，确定要保存吗？',
        confirmText: '确定保存',
        cancelText: '重新修改',
        success: (res) => {
          if (res.confirm) {
            this.doSaveProfile(tempAvatar, nickname, tempSignature);
          }
        }
      });
      return;
    }

    // 正常保存流程
    this.doSaveProfile(tempAvatar, nickname, tempSignature);
  },

  /**
   * 执行保存操作 - 新增方法，分离保存逻辑
   */
  doSaveProfile: function(avatarUrl, nickname, signature) {
    this.setData({ isLoading: true });

    // 调用云函数更新用户资料
    wx.cloud.callFunction({
      name: 'updateUserProfile',
      data: {
        avatarUrl: avatarUrl,
        nickName: nickname,
        signature: signature || '', // 添加个性签名
        isAutoGenerated: false,
        isProfileComplete: true
      },
      success: (res) => {
        console.log('更新用户资料成功:', res);
        
        if (res.result && res.result.success) {
          // 更新全局用户信息和本地缓存
          const updatedUserInfo = {
            ...app.globalData.userInfo,
            avatarUrl: avatarUrl,
            nickName: nickname,
            signature: signature || '',
            isAutoGenerated: false,
            isProfileComplete: true
          };
          
          app.globalData.userInfo = updatedUserInfo;
          // 确保本地缓存也得到更新
          wx.setStorageSync('userInfo', updatedUserInfo);
          
          wx.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 1500
          });
          
          // 延迟返回，让用户看到成功提示
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          throw new Error(res.result?.message || '更新失败');
        }
      },
      fail: (err) => {
        console.error('更新用户资料失败:', err);
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  /**
   * 阻止事件冒泡的空函数
   * @description 在WXML中与catchtap配合使用，阻止点击事件向上传递。
   */
  preventBubble: function() {
    // 无需代码，catchtap已完成所有工作
    console.log('阻止事件冒泡');
  },
}); 