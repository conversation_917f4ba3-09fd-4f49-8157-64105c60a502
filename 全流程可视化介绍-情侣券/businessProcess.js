// Content for the Business Process section

function renderBusinessProcess() {
    return `
        <div class="mb-6">
            <p class="mb-6">情侣信誉券小程序 MVP 版本的核心业务流程图展示了用户在使用过程中的主要交互和数据流动。以下是主要的业务流程：</p>
            
            <div class="tabs mb-6">
                <div class="tab active" data-tab="partner-binding">伴侣绑定流程</div>
                <div class="tab" data-tab="voucher-creation">发券流程</div>
                <div class="tab" data-tab="voucher-receiving">收券与查券流程</div>
                <div class="tab" data-tab="voucher-using">核销券流程</div>
            </div>
            
            <div class="tab-content active" id="partner-binding">
                <h3 class="text-xl font-bold text-pink-800 mb-4">伴侣绑定流程</h3>
                <p class="mb-4">该流程描述了用户如何在小程序中与伴侣建立绑定关系，是使用信誉券功能的前提。</p>
                
                <div class="svg-container">
                    <svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
                      <style>
                        .start-end { fill: #FFC107; stroke: #FF9800; stroke-width: 2; }
                        .process { fill: #2196F3; stroke: #1976D2; stroke-width: 2; }
                        .decision { fill: #4CAF50; stroke: #388E3C; stroke-width: 2; }
                        .arrow { stroke: #333; stroke-width: 2; marker-end: url(#arrowhead-black); }
                        .text { font-family: sans-serif; font-size: 12px; fill: #333; text-anchor: middle; }
                        #arrowhead-black { fill: #333; }
                      </style>
                      <defs>
                        <marker id="arrowhead-black" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                          <polygon points="0 0, 10 3.5, 0 7"></polygon>
                        </marker>
                      </defs>

                      <!-- Nodes -->
                      <ellipse cx="300" cy="50" rx="60" ry="30" class="start-end"></ellipse>
                      <text x="300" y="55" class="text">开始</text>

                      <rect x="220" y="120" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="145" class="text" fill="#FFF">用户A发起绑定邀请</text>

                      <rect x="220" y="200" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="225" class="text" fill="#FFF">生成邀请链接/码</text>

                      <rect x="220" y="280" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="305" class="text" fill="#FFF">用户A分享邀请</text>

                      <rect x="450" y="280" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="530" y="305" class="text" fill="#FFF">用户B点击/输入邀请</text>

                      <rect x="450" y="360" width="160" height="50" rx="10" ry="10" class="process"></rect>
                        <text x="530" y="385" class="text" fill="#FFF">系统身份验证</text>


                      <rect x="220" y="360" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="385" class="text" fill="#FFF">用户B确认绑定</text>

                        <rect x="220" y="440" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="465" class="text" fill="#FFF">系统建立绑定关系</text>


                      <ellipse cx="300" cy="540" rx="60" ry="30" class="start-end"></ellipse>
                      <text x="300" y="545" class="text">结束</text>

                      <!-- Arrows -->
                      <path d="M300 80 L 300 120" class="arrow"></path>
                      <path d="M300 170 L 300 200" class="arrow"></path>
                      <path d="M300 250 L 300 280" class="arrow"></path>
                       <path d="M380 305 L 450 305" class="arrow"></path>
                       <path d="M530 330 L 530 360" class="arrow"></path>
                        <path d="M450 385 L 380 385" class="arrow"></path>
                      <path d="M300 410 L 300 440" class="arrow"></path>
                      <path d="M300 490 L 300 540" class="arrow"></path>

                    </svg>
                </div>
                
                <div class="mt-6 bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">流程说明：</h4>
                    <ol class="list-decimal pl-5 text-blue-800">
                        <li>用户A在"伴侣管理"页面点击"发起绑定"</li>
                        <li>系统生成唯一的邀请链接或码</li>
                        <li>用户A通过微信分享功能将邀请发送给伴侣（用户B）</li>
                        <li>用户B收到并点击邀请链接，跳转至小程序</li>
                        <li>系统验证邀请链接有效性和用户B身份</li>
                        <li>用户B确认接受绑定请求</li>
                        <li>系统更新双方用户记录，建立伴侣绑定关系</li>
                        <li>完成绑定，双方可以互相发送信誉券</li>
                    </ol>
                </div>
            </div>
            
            <div class="tab-content" id="voucher-creation">
                <h3 class="text-xl font-bold text-pink-800 mb-4">发券流程</h3>
                <p class="mb-4">该流程描述了信誉券的创建和发送过程，是小程序的核心功能之一。</p>
                
                <div class="svg-container">
                    <svg width="600" height="500" xmlns="http://www.w3.org/2000/svg">
                      <style>
                        .start-end { fill: #FFC107; stroke: #FF9800; stroke-width: 2; }
                        .process { fill: #2196F3; stroke: #1976D2; stroke-width: 2; }
                        .arrow { stroke: #333; stroke-width: 2; marker-end: url(#arrowhead-black); }
                        .text { font-family: sans-serif; font-size: 12px; fill: #333; text-anchor: middle; }
                        #arrowhead-black { fill: #333; }
                      </style>
                      <defs>
                        <marker id="arrowhead-black" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                          <polygon points="0 0, 10 3.5, 0 7"></polygon>
                        </marker>
                      </defs>

                      <!-- Nodes -->
                      <ellipse cx="300" cy="50" rx="60" ry="30" class="start-end"></ellipse>
                      <text x="300" y="55" class="text">开始 (用户在发券页)</text>

                      <rect x="220" y="120" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="145" class="text" fill="#FFF">填写券信息 (名称, 类型, 描述, 有效期)</text>

                      <rect x="220" y="200" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="225" class="text" fill="#FFF">选择接收人 (已绑定伴侣)</text>

                      <rect x="220" y="280" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="305" class="text" fill="#FFF">填写发送留言 (可选)</text>

                      <rect x="220" y="360" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="385" class="text" fill="#FFF">确认发送</text>

                      <rect x="220" y="440" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="465" class="text" fill="#FFF">系统创建/更新券记录 (status: pending, recipient_id)</text>

                      <rect x="220" y="520" width="160" height="50" rx="10" ry="10" class="process"></rect>
                       <text x="300" y="545" class="text" fill="#FFF">系统通知接收人 (如通过服务通知)</text>


                      <ellipse cx="300" cy="600" rx="60" ry="30" class="start-end"></ellipse>
                      <text x="300" y="605" class="text">结束</text>

                      <!-- Arrows -->
                      <path d="M300 80 L 300 120" class="arrow"></path>
                      <path d="M300 170 L 300 200" class="arrow"></path>
                      <path d="M300 250 L 300 280" class="arrow"></path>
                      <path d="M300 330 L 300 360" class="arrow"></path>
                      <path d="M300 410 L 300 440" class="arrow"></path>
                      <path d="M300 490 L 300 520" class="arrow"></path>
                      <path d="M300 570 L 300 600" class="arrow"></path>

                    </svg>
                </div>
                
                <div class="mt-6 bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">流程说明：</h4>
                    <ol class="list-decimal pl-5 text-blue-800">
                        <li>用户点击底部导航栏的"发券"按钮，进入发券页面</li>
                        <li>用户填写券的名称、选择或自定义类型、填写描述(可选)、设置有效期</li>
                        <li>选择接收人（默认为已绑定的唯一伴侣）</li>
                        <li>填写发送留言(可选)</li>
                        <li>点击"确认发送"按钮</li>
                        <li>系统创建券记录，状态设为"待使用"，并关联接收人ID</li>
                        <li>系统尝试向接收人发送服务通知（如功能可用）</li>
                        <li>发券完成，用户被引导回到首页或查看"我发出的券"列表</li>
                    </ol>
                </div>
            </div>
            
            <div class="tab-content" id="voucher-receiving">
                <h3 class="text-xl font-bold text-pink-800 mb-4">收券与查券流程</h3>
                <p class="mb-4">该流程描述了用户如何接收、查看和管理收到的信誉券。</p>
                
                <div class="svg-container">
                    <svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
                      <style>
                        .start-end { fill: #FFC107; stroke: #FF9800; stroke-width: 2; }
                        .process { fill: #2196F3; stroke: #1976D2; stroke-width: 2; }
                        .decision { fill: #4CAF50; stroke: #388E3C; stroke-width: 2; }
                        .arrow { stroke: #333; stroke-width: 2; marker-end: url(#arrowhead-black); }
                        .text { font-family: sans-serif; font-size: 12px; fill: #333; text-anchor: middle; }
                        #arrowhead-black { fill: #333; }
                      </style>
                      <defs>
                        <marker id="arrowhead-black" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                          <polygon points="0 0, 10 3.5, 0 7"></polygon>
                        </marker>
                      </defs>

                      <!-- Nodes -->
                      <ellipse cx="300" cy="50" rx="60" ry="30" class="start-end"></ellipse>
                      <text x="300" y="55" class="text">开始</text>

                      <rect x="220" y="120" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="145" class="text" fill="#FFF">用户打开小程序 / 收到服务通知</text>

                      <rect x="220" y="200" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="225" class="text" fill="#FFF">进入"我收到的券"列表</text>

                      <rect x="220" y="280" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="305" class="text" fill="#FFF">浏览券列表 (状态: 待使用)</text>

                      <rect x="100" y="360" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="180" y="385" class="text" fill="#FFF">点击列表项</text>

                       <rect x="220" y="440" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="465" class="text" fill="#FFF">进入券详情页</text>
                      <text x="300" y="485" class="text" fill="#FFF">(查看名称, 描述, 有效期, 发行人等)</text>

                      <ellipse cx="450" cy="385" rx="60" ry="30" class="start-end"></ellipse>
                      <text x="450" y="390" class="text">结束 (退出)</text>

                      <ellipse cx="300" cy="540" rx="60" ry="30" class="start-end"></ellipse>
                      <text x="300" y="545" class="text">结束 (查看详情)</text>

                      <!-- Arrows -->
                      <path d="M300 80 L 300 120" class="arrow"></path>
                      <path d="M300 170 L 300 200" class="arrow"></path>
                      <path d="M300 250 L 300 280" class="arrow"></path>
                       <path d="M300 330 L 300 360" class="arrow"></path>
                       <path d="M300 330 L 450 360" class="arrow"></path>
                        <path d="M180 410 L 180 440 C 180 440, 300 440, 300 440" class="arrow"></path>
                        <path d="M300 490 L 300 540" class="arrow"></path>

                    </svg>
                </div>
                
                <div class="mt-6 bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">流程说明：</h4>
                    <ol class="list-decimal pl-5 text-blue-800">
                        <li>用户收到新券通知（如推送或在小程序内提醒）</li>
                        <li>用户打开小程序，默认进入首页</li>
                        <li>用户在首页或通过Tab切换查看"我收到的券"列表</li>
                        <li>浏览券列表，券按状态分类展示（重点突出"待使用"状态的券）</li>
                        <li>用户可以：
                            <ul class="list-disc pl-5 mt-1">
                                <li>继续浏览其他券或退出小程序</li>
                                <li>点击某张券查看详情</li>
                            </ul>
                        </li>
                        <li>在券详情页，用户可查看完整的券信息，并根据券状态执行相应操作（如核销）</li>
                    </ol>
                </div>
            </div>
            
            <div class="tab-content" id="voucher-using">
                <h3 class="text-xl font-bold text-pink-800 mb-4">核销券流程</h3>
                <p class="mb-4">该流程描述了用户如何使用和核销收到的信誉券。在MVP版本中，采用接收方单方面核销的简化流程。</p>
                
                <div class="svg-container">
                    <svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
                      <style>
                        .start-end { fill: #FFC107; stroke: #FF9800; stroke-width: 2; }
                        .process { fill: #2196F3; stroke: #1976D2; stroke-width: 2; }
                        .arrow { stroke: #333; stroke-width: 2; marker-end: url(#arrowhead-black); }
                        .text { font-family: sans-serif; font-size: 12px; fill: #333; text-anchor: middle; }
                        #arrowhead-black { fill: #333; }
                      </style>
                      <defs>
                        <marker id="arrowhead-black" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                          <polygon points="0 0, 10 3.5, 0 7"></polygon>
                        </marker>
                      </defs>

                      <!-- Nodes -->
                      <ellipse cx="300" cy="50" rx="60" ry="30" class="start-end"></ellipse>
                      <text x="300" y="55" class="text">开始 (接收方在券详情页)</text>

                      <rect x="220" y="120" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="145" class="text" fill="#FFF">找到待使用券</text>

                      <rect x="220" y="200" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="225" class="text" fill="#FFF">点击"使用此券/核销"按钮</text>

                      <rect x="220" y="280" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="305" class="text" fill="#FFF">系统更新券状态为"已使用"</text>
                      <text x="300" y="325" class="text" fill="#FFF">(更新 status='used', used_time=now)</text>


                      <rect x="220" y="360" width="160" height="50" rx="10" ry="10" class="process"></rect>
                      <text x="300" y="385" class="text" fill="#FFF">双方列表/详情页显示状态变更</text>

                      <ellipse cx="300" cy="440" rx="60" ry="30" class="start-end"></ellipse>
                      <text x="300" y="445" class="text">结束</text>

                      <!-- Arrows -->
                      <path d="M300 80 L 300 120" class="arrow"></path>
                      <path d="M300 170 L 300 200" class="arrow"></path>
                      <path d="M300 250 L 300 280" class="arrow"></path>
                       <path d="M300 335 L 300 360" class="arrow"></path>
                        <path d="M300 410 L 300 440" class="arrow"></path>

                    </svg>
                </div>
                
                <div class="mt-6 bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">流程说明：</h4>
                    <ol class="list-decimal pl-5 text-blue-800">
                        <li>接收方用户在"我收到的券"列表中，找到并点击想要使用的券</li>
                        <li>进入券详情页，查看券的详细信息</li>
                        <li>当满足使用条件（如：在伴侣面前、券仍在有效期内）时，点击"使用此券/核销"按钮</li>
                        <li>系统更新券状态为"已使用"，并记录核销时间</li>
                        <li>双方的券列表和详情页同步更新状态（发行方可在"我发出的券"中看到状态变更）</li>
                        <li>核销完成，券移至历史记录可供回顾</li>
                    </ol>
                    <p class="mt-3 text-xs text-blue-700 italic">注：MVP版本简化为接收方单方确认核销，未来版本可考虑增加发行方确认或双方互动确认等更复杂机制。</p>
                </div>
            </div>
            
            <h3 class="text-xl font-bold text-pink-800 mt-8 mb-4">产品功能结构思维导图</h3>
            
            <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-200">
                <pre class="text-sm overflow-x-auto" style="font-family: monospace;">
情侣信誉券应用 (MVP)
├── 用户账户管理
│   ├── 微信一键授权登录
│   ├── 获取/展示微信昵称、头像
│   └── 伴侣绑定机制
│       ├── 发起绑定邀请
│       ├── 生成邀请链接/码
│       └── 接受绑定确认
├── 信誉券创建
│   ├── 填写券名称 (必填)
│   ├── 选择/自定义券类型
│   ├── 填写券描述 (选填)
│   ├── 设置有效期 (预设/指定日期)
│   └── (MVP可选) 券面简单自定义
├── 信誉券发送
│   ├── 从创建页发送
│   ├── 从待发送列表选择发送
│   ├── 选择接收人 (已绑定伴侣)
│   └── 填写发送前留言 (选填)
├── 信誉券接收与提醒
│   ├── "我收到的券"列表新券提示
│   └── (如条件允许) 微信服务通知提醒
├── 信誉券查看与管理
│   ├── 券列表 (Tab: 我发出的 / 我收到的)
│   │   ├── 展示券状态 (待发送, 待使用, 已使用, 已过期)
│   │   └── 列表项关键信息展示
│   └── 券详情
│       ├── 展示所有券信息
│       └── 根据状态显示操作按钮
├── 信誉券核销
│   ├── 接收方发起核销
│   ├── 点击"使用此券/核销"按钮
│   └── 系统单方面更新券状态为"已使用"
├── 历史记录
│   ├── 查看所有非"待发送/待使用"状态券
│   ├── 按状态过滤 (已使用 / 已过期)
│   └── 查看历史券详情
└── 设置 (简略)
    └── (MVP阶段可能仅含) 退出登录
                </pre>
            </div>
        </div>
        
        <script>
            // Tab switching functionality
            document.addEventListener('DOMContentLoaded', function() {
                const tabs = document.querySelectorAll('.tab');
                
                tabs.forEach(tab => {
                    tab.addEventListener('click', function() {
                        // Remove active class from all tabs
                        tabs.forEach(t => t.classList.remove('active'));
                        
                        // Add active class to clicked tab
                        this.classList.add('active');
                        
                        // Hide all tab contents
                        const tabContents = document.querySelectorAll('.tab-content');
                        tabContents.forEach(content => {
                            content.classList.remove('active');
                        });
                        
                        // Show the content for the clicked tab
                        const tabId = this.getAttribute('data-tab');
                        document.getElementById(tabId).classList.add('active');
                    });
                });
            });
        </script>
    `;
}
