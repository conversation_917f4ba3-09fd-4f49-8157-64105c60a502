/**
 * 空状态提示组件 JS
 * 提供友好的空状态展示和用户引导
 */

Component({
  /**
   * 组件属性列表
   */
  properties: {
    // 是否显示空状态
    visible: {
      type: Boolean,
      value: true
    },
    
    // 图标 (emoji 或字符)
    icon: {
      type: String,
      value: ''
    },
    
    // 图标图片URL
    iconImage: {
      type: String,
      value: ''
    },
    
    // 主标题
    title: {
      type: String,
      value: '暂无内容'
    },
    
    // 描述文本
    description: {
      type: String,
      value: ''
    },
    
    // 主要操作按钮文本
    actionText: {
      type: String,
      value: ''
    },
    
    // 次要操作按钮文本
    secondaryActionText: {
      type: String,
      value: ''
    },
    
    // 操作按钮类型 (primary, success, warning, error, info)
    actionType: {
      type: String,
      value: 'primary'
    },
    
    // 主要操作按钮加载状态
    actionLoading: {
      type: Boolean,
      value: false
    },
    
    // 主要操作按钮禁用状态
    actionDisabled: {
      type: Boolean,
      value: false
    },
    
    // 次要操作按钮加载状态
    secondaryActionLoading: {
      type: Boolean,
      value: false
    },
    
    // 次要操作按钮禁用状态
    secondaryActionDisabled: {
      type: Boolean,
      value: false
    },
    
    // 尺寸 (small, normal, large, compact, fullscreen)
    size: {
      type: String,
      value: 'normal'
    },
    
    // 是否显示操作区域
    showActions: {
      type: Boolean,
      value: true
    },
    
    // 是否显示内容插槽
    showContentSlot: {
      type: Boolean,
      value: false
    },
    
    // 是否显示操作插槽
    showActionsSlot: {
      type: Boolean,
      value: false
    },
    
    // 自定义样式类名
    customClass: {
      type: String,
      value: ''
    },
    
    // 组件ID，用于识别
    emptyId: {
      type: String,
      value: ''
    },
    
    // 额外数据
    extraData: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件数据
   */
  data: {
    // 尺寸样式类
    sizeClass: '',
    
    // 图标加载状态
    iconLoading: false,
    iconLoadError: false,
    
    // 默认空状态配置
    defaultConfigs: {
      noData: {
        icon: '📭',
        title: '暂无数据',
        description: '这里还没有任何内容',
        actionText: '刷新'
      },
      noNetwork: {
        icon: '📶',
        title: '网络连接失败',
        description: '请检查网络连接后重试',
        actionText: '重新连接'
      },
      noResult: {
        icon: '🔍',
        title: '没有找到结果',
        description: '试试其他关键词或条件',
        actionText: '重新搜索'
      },
      error: {
        icon: '⚠️',
        title: '出现了问题',
        description: '页面加载失败，请稍后重试',
        actionText: '重试'
      },
      coming: {
        icon: '🚀',
        title: '敬请期待',
        description: '功能开发中，即将上线',
        actionText: '返回首页'
      }
    }
  },

  /**
   * 组件方法列表
   */
  methods: {
    /**
     * 主要操作按钮点击事件
     */
    onActionTap(e) {
      if (this.properties.actionDisabled || this.properties.actionLoading) {
        return;
      }
      
      e.stopPropagation();
      
      // 触发操作事件
      this.triggerEvent('action', {
        type: 'primary',
        actionType: this.properties.actionType,
        emptyId: this.properties.emptyId,
        extraData: this.properties.extraData,
        timeStamp: Date.now()
      });
      
      // 添加点击反馈
      this.addActionFeedback('primary');
    },

    /**
     * 次要操作按钮点击事件
     */
    onSecondaryActionTap(e) {
      if (this.properties.secondaryActionDisabled || this.properties.secondaryActionLoading) {
        return;
      }
      
      e.stopPropagation();
      
      // 触发操作事件
      this.triggerEvent('secondaryAction', {
        type: 'secondary',
        emptyId: this.properties.emptyId,
        extraData: this.properties.extraData,
        timeStamp: Date.now()
      });
      
      // 添加点击反馈
      this.addActionFeedback('secondary');
    },

    /**
     * 图标加载完成
     */
    onIconLoad(e) {
      this.setData({
        iconLoading: false,
        iconLoadError: false
      });
      
      console.log('[EmptyState] Icon loaded successfully');
    },

    /**
     * 图标加载失败
     */
    onIconError(e) {
      this.setData({
        iconLoading: false,
        iconLoadError: true
      });
      
      console.error('[EmptyState] Icon load failed:', e.detail);
      
      // 触发图标加载失败事件
      this.triggerEvent('iconError', {
        emptyId: this.properties.emptyId,
        iconImage: this.properties.iconImage,
        error: e.detail
      });
    },

    /**
     * 添加操作反馈效果
     */
    addActionFeedback(type) {
      // 使用微信小程序动画API添加反馈
      if (typeof wx !== 'undefined' && wx.createAnimation) {
        const animation = wx.createAnimation({
          duration: 200,
          timingFunction: 'ease-out'
        });
        
        animation.scale(0.95).step();
        animation.scale(1).step({ delay: 50 });
        
        const selector = type === 'primary' ? '.primary-action' : '.secondary-action';
        this.animate(selector, animation.export());
      }
      
      // 触发反馈事件
      this.triggerEvent('feedback', {
        type: type,
        emptyId: this.properties.emptyId,
        action: 'tap'
      });
    },

    /**
     * 更新尺寸样式类
     */
    updateSizeClass() {
      const { size } = this.properties;
      this.setData({
        sizeClass: size !== 'normal' ? size : ''
      });
    },

    /**
     * 应用预设配置
     */
    applyConfig(configKey) {
      const config = this.data.defaultConfigs[configKey];
      if (!config) {
        console.warn(`[EmptyState] Config '${configKey}' not found`);
        return;
      }
      
      this.setData({
        icon: config.icon,
        title: config.title,
        description: config.description,
        actionText: config.actionText
      });
      
      console.log(`[EmptyState] Applied config: ${configKey}`);
    },

    /**
     * 显示空状态
     */
    show() {
      this.setData({
        visible: true
      });
      
      this.triggerEvent('show', {
        emptyId: this.properties.emptyId,
        timeStamp: Date.now()
      });
    },

    /**
     * 隐藏空状态
     */
    hide() {
      this.setData({
        visible: false
      });
      
      this.triggerEvent('hide', {
        emptyId: this.properties.emptyId,
        timeStamp: Date.now()
      });
    },

    /**
     * 切换显示状态
     */
    toggle() {
      if (this.properties.visible) {
        this.hide();
      } else {
        this.show();
      }
    },

    /**
     * 更新内容
     */
    updateContent(options) {
      const allowedKeys = ['icon', 'iconImage', 'title', 'description', 'actionText', 'secondaryActionText'];
      const updateData = {};
      
      for (const key of allowedKeys) {
        if (options.hasOwnProperty(key)) {
          updateData[key] = options[key];
        }
      }
      
      if (Object.keys(updateData).length > 0) {
        this.setData(updateData);
        console.log('[EmptyState] Content updated:', updateData);
      }
    },

    /**
     * 设置加载状态
     */
    setLoading(primary = false, secondary = false) {
      this.setData({
        actionLoading: primary,
        secondaryActionLoading: secondary
      });
    },

    /**
     * 设置禁用状态
     */
    setDisabled(primary = false, secondary = false) {
      this.setData({
        actionDisabled: primary,
        secondaryActionDisabled: secondary
      });
    },

    /**
     * 获取组件状态
     */
    getState() {
      return {
        visible: this.properties.visible,
        icon: this.properties.icon,
        iconImage: this.properties.iconImage,
        title: this.properties.title,
        description: this.properties.description,
        actionText: this.properties.actionText,
        secondaryActionText: this.properties.secondaryActionText,
        actionLoading: this.properties.actionLoading,
        actionDisabled: this.properties.actionDisabled,
        secondaryActionLoading: this.properties.secondaryActionLoading,
        secondaryActionDisabled: this.properties.secondaryActionDisabled,
        size: this.properties.size,
        extraData: this.properties.extraData
      };
    }
  },

  /**
   * 生命周期函数
   */
  lifetimes: {
    /**
     * 组件实例被创建
     */
    created() {
      console.log('[EmptyState] Component created');
    },

    /**
     * 组件实例进入页面节点树
     */
    attached() {
      console.log('[EmptyState] Component attached');
      
      // 初始化样式类
      this.updateSizeClass();
      
      // 设置图标加载状态
      if (this.properties.iconImage) {
        this.setData({
          iconLoading: true
        });
      }
    },

    /**
     * 组件实例被从页面节点树移除
     */
    detached() {
      console.log('[EmptyState] Component detached');
    }
  },

  /**
   * 组件数据字段监听器
   */
  observers: {
    /**
     * 监听尺寸变化
     */
    'size': function(size) {
      this.updateSizeClass();
    },

    /**
     * 监听图标图片变化
     */
    'iconImage': function(iconImage) {
      if (iconImage) {
        this.setData({
          iconLoading: true,
          iconLoadError: false
        });
      } else {
        this.setData({
          iconLoading: false,
          iconLoadError: false
        });
      }
    },

    /**
     * 监听操作按钮状态
     */
    'actionText, secondaryActionText': function(actionText, secondaryActionText) {
      const hasActions = !!(actionText || secondaryActionText);
      if (hasActions !== this.properties.showActions) {
        console.log(`[EmptyState] Actions visibility changed: ${hasActions}`);
      }
    }
  },

  /**
   * 组件选项
   */
  options: {
    // 启用多插槽支持
    multipleSlots: true,
    // 使用纯数据字段优化性能
    pureDataPattern: /^_/,
    // 样式隔离
    styleIsolation: 'isolated'
  },

  /**
   * 外部样式类
   */
  externalClasses: [
    'custom-class',
    'empty-class',
    'icon-class',
    'title-class',
    'description-class',
    'action-class'
  ]
});
