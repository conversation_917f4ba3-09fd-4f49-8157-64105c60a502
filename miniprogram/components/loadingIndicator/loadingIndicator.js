/**
 * 加载指示器组件 JS
 * 提供多种加载动画效果和控制逻辑
 */

Component({
  /**
   * 组件属性列表
   */
  properties: {
    // 是否显示加载指示器
    visible: {
      type: Boolean,
      value: false,
      observer: 'onVisibleChange'
    },
    
    // 加载文本
    text: {
      type: String,
      value: '加载中...'
    },
    
    // 加载器类型 (spinner, dots, pulse, wave, default)
    type: {
      type: String,
      value: 'default'
    },
    
    // 尺寸 (small, medium, large)
    size: {
      type: String,
      value: 'medium'
    },
    
    // 显示模式 (inline, overlay)
    mode: {
      type: String,
      value: 'inline'
    },
    
    // 主题 (primary, success, warning, error)
    theme: {
      type: String,
      value: 'primary'
    },
    
    // 状态 (loading, success, error)
    state: {
      type: String,
      value: 'loading'
    },
    
    // 自定义样式类名
    customClass: {
      type: String,
      value: ''
    },
    
    // 是否自动隐藏 (毫秒)
    autoHide: {
      type: Number,
      value: 0
    },
    
    // 延迟显示时间 (毫秒)
    delay: {
      type: Number,
      value: 0
    },
    
    // 最小显示时间 (毫秒)
    minDuration: {
      type: Number,
      value: 800
    }
  },

  /**
   * 组件数据
   */
  data: {
    // 实际显示状态
    actualVisible: false,
    
    // 尺寸样式类
    sizeClass: '',
    
    // 旋转器样式
    spinnerStyle: '',
    
    // 定时器ID
    hideTimer: null,
    delayTimer: null,
    
    // 显示开始时间
    showStartTime: 0
  },

  /**
   * 组件方法列表
   */
  methods: {
    /**
     * 显示状态变化监听
     */
    onVisibleChange(newVal, oldVal) {
      if (newVal !== oldVal) {
        if (newVal) {
          this.show();
        } else {
          this.hide();
        }
      }
    },

    /**
     * 显示加载指示器
     */
    show() {
      // 清除之前的定时器
      this.clearTimers();
      
      if (this.properties.delay > 0) {
        // 延迟显示
        this.data.delayTimer = setTimeout(() => {
          this.actualShow();
        }, this.properties.delay);
      } else {
        this.actualShow();
      }
    },

    /**
     * 实际显示逻辑
     */
    actualShow() {
      this.setData({
        actualVisible: true,
        showStartTime: Date.now()
      });
      
      // 更新样式类
      this.updateClasses();
      
      // 设置自动隐藏
      if (this.properties.autoHide > 0) {
        this.data.hideTimer = setTimeout(() => {
          this.hide();
        }, this.properties.autoHide);
      }
      
      // 触发显示事件
      this.triggerEvent('show', {
        type: this.properties.type,
        timeStamp: Date.now()
      });
    },

    /**
     * 隐藏加载指示器
     */
    hide() {
      const now = Date.now();
      const showDuration = now - this.data.showStartTime;
      
      // 检查最小显示时间
      if (this.data.showStartTime > 0 && showDuration < this.properties.minDuration) {
        const remainingTime = this.properties.minDuration - showDuration;
        
        setTimeout(() => {
          this.actualHide();
        }, remainingTime);
      } else {
        this.actualHide();
      }
    },

    /**
     * 实际隐藏逻辑
     */
    actualHide() {
      this.clearTimers();
      
      this.setData({
        actualVisible: false,
        showStartTime: 0
      });
      
      // 触发隐藏事件
      this.triggerEvent('hide', {
        type: this.properties.type,
        timeStamp: Date.now()
      });
    },

    /**
     * 清除所有定时器
     */
    clearTimers() {
      if (this.data.hideTimer) {
        clearTimeout(this.data.hideTimer);
        this.data.hideTimer = null;
      }
      
      if (this.data.delayTimer) {
        clearTimeout(this.data.delayTimer);
        this.data.delayTimer = null;
      }
    },

    /**
     * 更新样式类
     */
    updateClasses() {
      const { size, mode, theme, state, type } = this.properties;
      
      let sizeClass = size;
      if (mode === 'overlay') sizeClass += ' overlay';
      if (theme !== 'primary') sizeClass += ` theme-${theme}`;
      if (state !== 'loading') sizeClass += ` state-${state}`;
      if (mode === 'inline') sizeClass += ' inline';
      
      // 旋转器特殊样式
      let spinnerStyle = '';
      if (type === 'spinner') {
        const colors = {
          primary: '#e91e63',
          success: '#10b981',
          warning: '#f59e0b',
          error: '#ef4444'
        };
        
        const color = colors[theme] || colors.primary;
        spinnerStyle = `border-top-color: ${color}; border-color: ${color}33;`;
      }
      
      this.setData({
        sizeClass,
        spinnerStyle
      });
    },

    /**
     * 切换显示状态
     */
    toggle() {
      if (this.data.actualVisible) {
        this.hide();
      } else {
        this.show();
      }
    },

    /**
     * 更新加载文本
     */
    updateText(text) {
      this.setData({
        text: text || ''
      });
    },

    /**
     * 更新加载状态
     */
    updateState(state) {
      this.setData({
        state: state
      });
      this.updateClasses();
    },

    /**
     * 设置主题
     */
    setTheme(theme) {
      this.setData({
        theme: theme
      });
      this.updateClasses();
    },

    /**
     * 获取当前状态
     */
    getState() {
      return {
        visible: this.data.actualVisible,
        text: this.properties.text,
        type: this.properties.type,
        size: this.properties.size,
        mode: this.properties.mode,
        theme: this.properties.theme,
        state: this.properties.state
      };
    }
  },

  /**
   * 生命周期函数
   */
  lifetimes: {
    /**
     * 组件实例被创建
     */
    created() {
      console.log('[LoadingIndicator] Component created');
    },

    /**
     * 组件实例进入页面节点树
     */
    attached() {
      console.log('[LoadingIndicator] Component attached');
      
      // 初始化样式
      this.updateClasses();
      
      // 如果初始状态为显示，则显示
      if (this.properties.visible) {
        this.show();
      }
    },

    /**
     * 组件实例被从页面节点树移除
     */
    detached() {
      console.log('[LoadingIndicator] Component detached');
      
      // 清理定时器
      this.clearTimers();
    }
  },

  /**
   * 组件数据字段监听器
   */
  observers: {
    /**
     * 监听样式相关属性变化
     */
    'size, mode, theme, state, type': function(size, mode, theme, state, type) {
      this.updateClasses();
    },

    /**
     * 监听文本变化
     */
    'text': function(text) {
      console.log(`[LoadingIndicator] Text changed: ${text}`);
    }
  },

  /**
   * 页面生命周期函数
   */
  pageLifetimes: {
    /**
     * 页面显示
     */
    show() {
      // 页面显示时的处理逻辑
    },

    /**
     * 页面隐藏
     */
    hide() {
      // 页面隐藏时自动隐藏加载指示器
      if (this.data.actualVisible) {
        this.hide();
      }
    }
  },

  /**
   * 组件选项
   */
  options: {
    // 启用多插槽支持
    multipleSlots: true,
    // 使用纯数据字段优化性能
    pureDataPattern: /^_/,
    // 样式隔离
    styleIsolation: 'isolated'
  },

  /**
   * 外部样式类
   */
  externalClasses: [
    'custom-class',
    'loading-class',
    'animation-class',
    'text-class'
  ]
});
