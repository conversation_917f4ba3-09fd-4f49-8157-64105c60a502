<!-- 小程序图标设计 - 5款方案 144x144px -->

<!-- 方案1：双心相扣 - 粉色渐变 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FF69B4"/>
      <stop offset="50%" stop-color="#FFB6C1"/>
      <stop offset="100%" stop-color="#FFC0CB"/>
    </linearGradient>
    <filter id="shadow1">
      <feDropShadow dx="2" dy="2" stdDeviation="4" flood-opacity="0.3"/>
    </filter>
  </defs>
  <!-- 背景圆形 -->
  <circle cx="72" cy="72" r="68" fill="url(#gradient1)" filter="url(#shadow1)"/>
  <!-- 左心形 -->
  <path d="M50 45 C40 35, 25 35, 25 55 C25 70, 50 85, 50 85 C50 85, 60 75, 65 65" 
        fill="#FFFFFF" opacity="0.9"/>
  <!-- 右心形 -->
  <path d="M94 45 C104 35, 119 35, 119 55 C119 70, 94 85, 94 85 C94 85, 84 75, 79 65" 
        fill="#FFFFFF" opacity="0.9"/>
  <!-- 连接环 -->
  <circle cx="72" cy="65" r="8" fill="none" stroke="#FFFFFF" stroke-width="3" opacity="0.8"/>
</svg>

<!-- 方案2：情侣剪影 - 渐变背景 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FF8A80"/>
      <stop offset="50%" stop-color="#FFAB91"/>
      <stop offset="100%" stop-color="#FFCC9A"/>
    </linearGradient>
    <filter id="shadow2">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.2"/>
    </filter>
  </defs>
  <!-- 背景 -->
  <rect width="144" height="144" rx="32" fill="url(#gradient2)" filter="url(#shadow2)"/>
  <!-- 男生剪影 -->
  <ellipse cx="50" cy="45" rx="12" ry="15" fill="#FFFFFF" opacity="0.9"/>
  <rect x="38" y="58" width="24" height="35" rx="12" fill="#FFFFFF" opacity="0.9"/>
  <!-- 女生剪影 -->
  <ellipse cx="94" cy="45" rx="12" ry="15" fill="#FFFFFF" opacity="0.9"/>
  <rect x="82" y="58" width="24" height="35" rx="12" fill="#FFFFFF" opacity="0.9"/>
  <!-- 牵手 -->
  <line x1="62" y1="75" x2="82" y2="75" stroke="#FFFFFF" stroke-width="4" stroke-linecap="round" opacity="0.9"/>
  <!-- 装饰心形 -->
  <path d="M72 100 C68 96, 60 96, 60 104 C60 110, 72 120, 72 120 C72 120, 84 110, 84 104 C84 96, 76 96, 72 100" 
        fill="#FFFFFF" opacity="0.7"/>
</svg>

<!-- 方案3：卡片设计 - 简约风格 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <!-- <stop offset="0%" stop-color="#F8BBD9"/> -->
      <!-- <stop offset="100%" stop-color="#E1BEE7"/> -->
      <stop offset="0%" stop-color="#FF69B4"/>
      <stop offset="50%" stop-color="#FFB6C1"/>
      <stop offset="100%" stop-color="#FFC0CB"/>
    </linearGradient>
    <filter id="shadow3">
      <feDropShadow dx="3" dy="3" stdDeviation="5" flood-opacity="0.15"/>
    </filter>
  </defs>
  <!-- 背景 -->
  <circle cx="72" cy="72" r="70" fill="url(#gradient3)" filter="url(#shadow3)"/>
  <!-- 卡片主体 -->
  <rect x="32" y="45" width="80" height="54" rx="8" fill="#FFFFFF" filter="url(#shadow3)"/>
  <!-- 卡片装饰线 -->
  <line x1="45" y1="60" x2="99" y2="60" stroke="#FFB6C1" stroke-width="2" stroke-linecap="round"/>
  <line x1="45" y1="72" x2="85" y2="72" stroke="#FFB6C1" stroke-width="2" stroke-linecap="round"/>
  <line x1="45" y1="84" x2="75" y2="84" stroke="#FFB6C1" stroke-width="2" stroke-linecap="round"/>
  <!-- 右上角心形 -->
  <path d="M95 52 C93 50, 90 50, 90 53 C90 56, 95 60, 95 60 C95 60, 100 56, 100 53 C100 50, 97 50, 95 52" 
        fill="#FF69B4"/>
</svg>

<!-- 方案4：约定书风格 - 文艺范 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFCDD2"/>
      <stop offset="50%" stop-color="#F8BBD9"/>
      <stop offset="100%" stop-color="#E1BEE7"/>
    </linearGradient>
    <filter id="shadow4">
      <feDropShadow dx="2" dy="3" stdDeviation="4" flood-opacity="0.2"/>
    </filter>
  </defs>
  <!-- 背景 -->
  <rect width="144" height="144" rx="28" fill="url(#gradient4)" filter="url(#shadow4)"/>
  <!-- 纸张效果 -->
  <rect x="25" y="30" width="94" height="84" rx="4" fill="#FFFFFF" filter="url(#shadow4)"/>
  <!-- 标题装饰 -->
  <rect x="35" y="45" width="74" height="3" rx="1.5" fill="#FF69B4"/>
  <!-- 内容线条 -->
  <line x1="35" y1="60" x2="109" y2="60" stroke="#FFB6C1" stroke-width="1" opacity="0.6"/>
  <line x1="35" y1="70" x2="95" y2="70" stroke="#FFB6C1" stroke-width="1" opacity="0.6"/>
  <line x1="35" y1="80" x2="105" y2="80" stroke="#FFB6C1" stroke-width="1" opacity="0.6"/>
  <line x1="35" y1="90" x2="85" y2="90" stroke="#FFB6C1" stroke-width="1" opacity="0.6"/>
  <!-- 签名心形 -->
  <path d="M72 100 C69 97, 64 97, 64 102 C64 106, 72 113, 72 113 C72 113, 80 106, 80 102 C80 97, 75 97, 72 100" 
        fill="#FF69B4"/>
  <!-- 装饰花朵 -->
  <circle cx="100" cy="50" r="3" fill="#FFB6C1" opacity="0.7"/>
  <circle cx="105" cy="45" r="2" fill="#FF69B4" opacity="0.7"/>
  <circle cx="95" cy="48" r="2" fill="#FF69B4" opacity="0.7"/>
</svg>

<!-- 方案5：心愿盒子 - 立体感 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FF8A80"/>
      <stop offset="100%" stop-color="#FFAB91"/>
    </linearGradient>
    <linearGradient id="boxGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFFFFF"/>
      <stop offset="100%" stop-color="#F5F5F5"/>
    </linearGradient>
    <filter id="shadow5">
      <feDropShadow dx="3" dy="4" stdDeviation="6" flood-opacity="0.25"/>
    </filter>
  </defs>
  <!-- 背景 -->
  <circle cx="72" cy="72" r="68" fill="url(#gradient5)" filter="url(#shadow5)"/>
  <!-- 盒子底部 -->
  <rect x="40" y="75" width="64" height="35" rx="4" fill="url(#boxGradient)" filter="url(#shadow5)"/>
  <!-- 盒子盖子 -->
  <rect x="40" y="65" width="64" height="15" rx="4" fill="#FFFFFF" filter="url(#shadow5)"/>
  <!-- 蝴蝶结 -->
  <path d="M68 65 L76 65 M72 50 L72 70" stroke="#FF69B4" stroke-width="3" stroke-linecap="round"/>
  <ellipse cx="68" cy="58" rx="6" ry="4" fill="#FF69B4" opacity="0.8"/>
  <ellipse cx="76" cy="58" rx="6" ry="4" fill="#FF69B4" opacity="0.8"/>
  <circle cx="72" cy="58" r="3" fill="#FF1744"/>
  <!-- 装饰星星 -->
  <path d="M55 90 L57 94 L61 94 L58 97 L59 101 L55 99 L51 101 L52 97 L49 94 L53 94 Z" 
        fill="#FFB6C1" opacity="0.7"/>
  <path d="M85 85 L86 87 L88 87 L87 89 L87 91 L85 90 L83 91 L83 89 L82 87 L84 87 Z" 
        fill="#FF69B4" opacity="0.7"/>
</svg> 