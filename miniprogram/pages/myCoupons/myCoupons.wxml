<!-- pages/myCoupons/myCoupons.wxml -->
<view class="page-container" style="background: {{themeGradient}};">
  
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">我的券</text>
  </view>

  <!-- 主Tab导航 -->
  <view class="main-tabs-container {{tabAnimationClass}}">
    <view class="main-tabs">
      <view 
        wx:for="{{mainTabs}}" 
        wx:key="key"
        class="main-tab {{activeMainTab === item.key ? 'active' : ''}}"
        bindtap="switchMainTab"
        data-tab="{{item.key}}"
      >
        <view class="tab-content">
          <text class="tab-label">{{item.label}}</text>
          <text class="tab-count">{{item.key === 'received' ? (couponStats.received.total || 0) : (couponStats.sent.total || 0)}}</text>
        </view>
      </view>
    </view>
    <view class="tab-indicator" style="transform: translateX({{activeMainTab === 'received' ? '0' : '100%'}});"></view>
  </view>

  <!-- 子Tab导航（仅对收到的券显示） -->
  <view class="sub-tabs-container" wx:if="{{activeMainTab === 'received'}}">
    <scroll-view scroll-x class="sub-tabs-scroll">
      <view class="sub-tabs">
        <view 
          wx:for="{{subTabs}}" 
          wx:key="key"
          class="sub-tab {{activeSubTab === item.key ? 'active' : ''}}"
          bindtap="switchSubTab"
          data-tab="{{item.key}}"
        >
          <text class="sub-tab-label">{{item.label}}</text>
          <view class="sub-tab-count">{{couponStats.received[item.key] || 0}}</view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 内容区域 -->
  <view class="content-container {{contentAnimationClass}}">
    
    <!-- 加载状态 -->
    <view wx:if="{{isLoading}}" class="loading-container">
      <view class="loading-animation">
        <view class="loading-heart">💖</view>
        <view class="loading-dots">
          <view class="dot dot1"></view>
          <view class="dot dot2"></view>
          <view class="dot dot3"></view>
        </view>
      </view>
      <text class="loading-text">正在加载券券们...</text>
    </view>
    
    <!-- 空状态 -->
    <view wx:elif="{{filteredCoupons.length === 0}}" class="empty-container">
      <view class="empty-animation">
        <view class="empty-icon">
          <text wx:if="{{activeMainTab === 'received' && activeSubTab === 'pending'}}">💝</text>
          <text wx:elif="{{activeMainTab === 'received' && activeSubTab === 'redeemed'}}">✨</text>
          <text wx:elif="{{activeMainTab === 'received' && activeSubTab === 'expired'}}">🕰️</text>
          <text wx:elif="{{activeMainTab === 'sent'}}">💌</text>
          <text wx:else>💫</text>
        </view>
        <view class="empty-rings">
          <view class="ring ring1"></view>
          <view class="ring ring2"></view>
          <view class="ring ring3"></view>
        </view>
      </view>
      <text class="empty-title">
        <text wx:if="{{activeMainTab === 'received' && activeSubTab === 'pending'}}">暂无待兑现的券</text>
        <text wx:elif="{{activeMainTab === 'received' && activeSubTab === 'redeemed'}}">还没有兑现过券</text>
        <text wx:elif="{{activeMainTab === 'received' && activeSubTab === 'expired'}}">没有失效的券</text>
        <text wx:elif="{{activeMainTab === 'sent'}}">还没有发送券</text>
        <text wx:else>暂无数据</text>
      </text>
      <text class="empty-subtitle">
        <text wx:if="{{activeMainTab === 'received' && activeSubTab === 'pending'}}">等待TA为你创建信誉券</text>
        <text wx:elif="{{activeMainTab === 'received' && activeSubTab === 'redeemed'}}">快去兑现你的信誉券吧！</text>
        <text wx:elif="{{activeMainTab === 'received' && activeSubTab === 'expired'}}">真棒！没有信誉券过期</text>
        <text wx:elif="{{activeMainTab === 'sent'}}">去首页为TA创建第一张信誉券吧</text>
        <text wx:else>换个分类看看吧</text>
      </text>
    </view>
    
    <!-- 券列表 -->
    <scroll-view wx:else scroll-y class="coupons-scroll">
      <view class="coupons-list">
        <view 
          wx:for="{{filteredCoupons}}" 
          wx:key="_id"
          class="coupon-item-wrapper"
          bindtap="onCouponTap"
          data-id="{{item._id}}"
        >
          <view class="coupon-card">
            <!-- 券卡片主体 -->
            <view class="coupon-main">
              <view class="coupon-icon-container">
                <text class="coupon-icon">{{item.icon || '💝'}}</text>
                <view class="icon-glow"></view>
              </view>
              
              <view class="coupon-content">
                <view class="coupon-title">{{item.title || item.name}}</view>
                <view class="coupon-description">{{item.description}}</view>
                
                <!-- 我收到的券信息 -->
                <view wx:if="{{activeMainTab === 'received'}}" class="coupon-meta">
                  <view class="coupon-type">
                    <text class="type-text">
                      <text wx:if="{{item.couponType === 'promise'}}">承诺券</text>
                      <text wx:elif="{{item.couponType === 'task'}}">任务券</text>
                      <text wx:elif="{{item.couponType === 'reward'}}">奖励券</text>
                      <text wx:elif="{{item.couponType === 'apology'}}">道歉券</text>
                      <text wx:elif="{{item.couponType === 'special'}}">特别券</text>
                      <text wx:else>券券</text>
                    </text>
                  </view>
                  
                  <view class="coupon-status">
                    <text class="status-text">{{item.statusText}}</text>
                  </view>
                </view>
                
                <!-- 我发送的券信息 -->
                <view wx:else class="coupon-meta-sent">
                  <view class="coupon-type">
                    <text class="type-text">
                      <text wx:if="{{item.couponType === 'promise'}}">承诺券</text>
                      <text wx:elif="{{item.couponType === 'task'}}">任务券</text>
                      <text wx:elif="{{item.couponType === 'reward'}}">奖励券</text>
                      <text wx:elif="{{item.couponType === 'apology'}}">道歉券</text>
                      <text wx:elif="{{item.couponType === 'special'}}">特别券</text>
                      <text wx:else>券券</text>
                    </text>
                  </view>
                  
                  <view class="sent-time">
                    <text class="sent-time-text">{{item.sentTimeText}}</text>
                  </view>
                </view>
                
                <!-- 过期时间（仅对我收到的券显示） -->
                <view wx:if="{{activeMainTab === 'received' && item.expiryText}}" class="coupon-expiry">
                  <text class="expiry-text">{{item.expiryText}}</text>
                </view>
              </view>
            </view>
            
            <!-- 券装饰元素 -->
            <view class="coupon-decorations">
              <view class="decoration-heart heart1">💕</view>
              <view class="decoration-heart heart2">💖</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 底部安全区域 -->
      <view class="bottom-safe-area"></view>
    </scroll-view>
    
  </view>
</view>