<!--
  加载指示器组件 WXML
  一个简单的居中加载动画组件，支持不同尺寸和样式
-->
<view class="loading-indicator {{sizeClass}} {{customClass}}" wx:if="{{visible}}">
  
  <!-- 加载动画区域 -->
  <view class="loading-animation">
    <!-- 旋转圆环加载器 -->
    <view class="loading-spinner {{spinnerType}}" wx:if="{{type === 'spinner'}}" style="{{spinnerStyle}}">
      <view class="spinner-ring"></view>
    </view>
    
    <!-- 点状加载器 -->
    <view class="loading-dots" wx:elif="{{type === 'dots'}}">
      <view class="dot dot-1"></view>
      <view class="dot dot-2"></view>
      <view class="dot dot-3"></view>
    </view>
    
    <!-- 脉冲加载器 -->
    <view class="loading-pulse" wx:elif="{{type === 'pulse'}}">
      <view class="pulse-circle"></view>
    </view>
    
    <!-- 波纹加载器 -->
    <view class="loading-wave" wx:elif="{{type === 'wave'}}">
      <view class="wave-bar bar-1"></view>
      <view class="wave-bar bar-2"></view>
      <view class="wave-bar bar-3"></view>
      <view class="wave-bar bar-4"></view>
      <view class="wave-bar bar-5"></view>
    </view>
    
    <!-- 默认旋转加载器 -->
    <view class="loading-default" wx:else>
      <view class="default-ring"></view>
    </view>
  </view>
  
  <!-- 加载文本 -->
  <view class="loading-text" wx:if="{{text}}">
    <text class="text-content">{{text}}</text>
  </view>
  
  <!-- 自定义内容插槽 -->
  <slot name="content"></slot>
  
</view>

<!-- 组件样式 -->
<style>
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  text-align: center;
  position: relative;
}

.loading-indicator.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4rpx);
  z-index: 999;
}

/* 动画容器 */
.loading-animation {
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 旋转圆环样式 */
.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  position: relative;
}

.loading-spinner.small { width: 48rpx; height: 48rpx; }
.loading-spinner.medium { width: 64rpx; height: 64rpx; }
.loading-spinner.large { width: 88rpx; height: 88rpx; }

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 6rpx solid rgba(233, 30, 99, 0.2);
  border-top-color: #e91e63;
  border-radius: 50%;
  animation: spinner-rotate 1s linear infinite;
}

@keyframes spinner-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 点状加载器 */
.loading-dots {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #e91e63;
  animation: dots-bounce 1.4s ease-in-out infinite both;
}

.dot-1 { animation-delay: -0.32s; }
.dot-2 { animation-delay: -0.16s; }

@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 脉冲加载器 */
.loading-pulse {
  width: 64rpx;
  height: 64rpx;
  position: relative;
}

.pulse-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #e91e63;
  animation: pulse-scale 1s ease-in-out infinite;
}

@keyframes pulse-scale {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* 波纹加载器 */
.loading-wave {
  display: flex;
  gap: 4rpx;
  align-items: center;
  height: 64rpx;
}

.wave-bar {
  width: 6rpx;
  background: #e91e63;
  border-radius: 3rpx;
  animation: wave-stretch 1.2s ease-in-out infinite;
}

.bar-1 { animation-delay: -1.2s; }
.bar-2 { animation-delay: -1.1s; }
.bar-3 { animation-delay: -1.0s; }
.bar-4 { animation-delay: -0.9s; }
.bar-5 { animation-delay: -0.8s; }

@keyframes wave-stretch {
  0%, 40%, 100% {
    height: 12rpx;
    opacity: 0.5;
  }
  20% {
    height: 64rpx;
    opacity: 1;
  }
}

/* 默认加载器 */
.loading-default {
  width: 64rpx;
  height: 64rpx;
  position: relative;
}

.default-ring {
  width: 100%;
  height: 100%;
  border: 4rpx solid #f3f4f6;
  border-top: 4rpx solid #e91e63;
  border-radius: 50%;
  animation: default-spin 1s linear infinite;
}

@keyframes default-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载文本 */
.loading-text {
  margin-top: 16rpx;
}

.text-content {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 500;
  line-height: 1.4;
}

/* 尺寸变体 */
.loading-indicator.small .loading-animation {
  margin-bottom: 16rpx;
}

.loading-indicator.small .text-content {
  font-size: 24rpx;
}

.loading-indicator.large .loading-animation {
  margin-bottom: 32rpx;
}

.loading-indicator.large .text-content {
  font-size: 32rpx;
}

/* 响应式适配 */
@media screen and (max-width: 480rpx) {
  .loading-indicator {
    padding: 24rpx;
  }
  
  .loading-spinner {
    width: 48rpx;
    height: 48rpx;
  }
  
  .loading-pulse {
    width: 48rpx;
    height: 48rpx;
  }
  
  .text-content {
    font-size: 26rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .loading-indicator.overlay {
    background: rgba(31, 41, 55, 0.9);
  }
  
  .spinner-ring {
    border-color: rgba(233, 30, 99, 0.3);
    border-top-color: #f48fb1;
  }
  
  .default-ring {
    border-color: #374151;
    border-top-color: #f48fb1;
  }
  
  .dot,
  .pulse-circle,
  .wave-bar {
    background: #f48fb1;
  }
  
  .text-content {
    color: #d1d5db;
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .spinner-ring,
  .dot,
  .pulse-circle,
  .wave-bar,
  .default-ring {
    animation: none;
  }
  
  .loading-indicator::after {
    content: '加载中...';
    font-size: 24rpx;
    color: #6b7280;
    margin-top: 16rpx;
  }
}
</style>
