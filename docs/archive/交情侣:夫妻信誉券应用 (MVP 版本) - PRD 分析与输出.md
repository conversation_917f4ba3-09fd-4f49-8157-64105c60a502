## 交情侣/夫妻信誉券应用 (MVP 版本) - PRD 分析与输出

### 1. 用户旅程图 (User Journey Map)

为了清晰地展现用户从初次接触到深度使用“情侣信誉券”小程序的完整体验流程，我们绘制如下用户旅程图：

```svg
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <style>
    .stage { fill: #E0F7FA; stroke: #00ACC1; stroke-width: 2; }
    .arrow { stroke: #00ACC1; stroke-width: 2; marker-end: url(#arrowhead); }
    .text { font-family: sans-serif; font-size: 12px; fill: #333; }
    .header { font-weight: bold; font-size: 14px; fill: #00796B; }
    .pain-point { fill: #FFEBEE; stroke: #E57373; stroke-width: 1; }
    .opportunity { fill: #E8F5E9; stroke: #81C784; stroke-width: 1; }
    .feeling { font-style: italic; fill: #555; }
    #arrowhead { fill: #00ACC1; }
  </style>
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7"></polygon>
    </marker>
  </defs>

  <!-- Stages -->
  <rect x="50" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
  <text x="110" y="80" text-anchor="middle" class="header">发现/访问</text>

  <rect x="200" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
  <text x="260" y="80" text-anchor="middle" class="header">注册/登录</text>

  <rect x="350" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
  <text x="410" y="80" text-anchor="middle" class="header">理解价值</text>

  <rect x="500" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
  <text x="560" y="80" text-anchor="middle" class="header">伴侣绑定</text>

  <rect x="650" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
  <text x="710" y="80" text-anchor="middle" class="header">核心体验</text>

  <rect x="800" y="50" width="120" height="700" rx="10" ry="10" class="stage"></rect>
  <text x="860" y="80" text-anchor="middle" class="header">核销/历史</text>

  <!-- Rows -->
  <text x="10" y="150" class="header">阶段</text>
  <text x="10" y="250" class="header">行为</text>
  <text x="10" y="350" class="header">接触点</text>
  <text x="10" y="450" class="header">想法</text>
  <text x="10" y="550" class="header">情绪</text>
  <text x="10" y="650" class="header">痛点/机会</text>

  <!-- Content -->
  <!-- Stage 1: 发现/访问 -->
  <text x="110" y="150" text-anchor="middle" class="text">发现/访问</text>
  <text x="110" y="250" text-anchor="middle" class="text">了解产品</text>
  <text x="110" y="350" text-anchor="middle" class="text">亲友推荐、社交媒体、应用商店</text>
  <text x="110" y="450" text-anchor="middle" class="text">“这是什么？有趣吗？”</text>
  <text x="110" y="550" text-anchor="middle" class="feeling">好奇</text>
  <rect x="60" y="640" width="100" height="50" rx="5" ry="5" class="opportunity"></rect>
  <text x="110" y="665" text-anchor="middle" class="text">营销触达，引导体验</text>

  <!-- Stage 2: 注册/登录 -->
  <text x="260" y="150" text-anchor="middle" class="text">注册/登录</text>
  <text x="260" y="250" text-anchor="middle" class="text">微信授权</text>
  <text x="260" y="350" text-anchor="middle" class="text">小程序授权页面</text>
  <text x="260" y="450" text-anchor="middle" class="text">“希望快速方便”</text>
  <text x="260" y="550" text-anchor="middle" class="feeling">期待顺利</text>
  <rect x="210" y="640" width="100" height="50" rx="5" ry="5" class="pain-point"></rect>
  <text x="260" y="665" text-anchor="middle" class="text">授权流程卡顿</text>

  <!-- Stage 3: 理解价值 -->
  <text x="410" y="150" text-anchor="middle" class="text">理解价值</text>
  <text x="410" y="250" text-anchor="middle" class="text">浏览产品介绍/指引</text>
  <text x="410" y="350" text-anchor="middle" class="text">首页、新手指引页</text>
  <text x="410" y="450" text-anchor="middle" class="text">“这个券怎么用？有什么好处？”</text>
  <text x="410" y="550" text-anchor="middle" class="feeling">求知</text>
  <rect x="360" y="640" width="100" height="50" rx="5" ry="5" class="opportunity"></rect>
  <text x="410" y="665" text-anchor="middle" class="text">清晰价值主张与指引</text>

  <!-- Stage 4: 伴侣绑定 -->
  <text x="560" y="150" text-anchor="middle" class="text">伴侣绑定</text>
  <text x="560" y="250" text-anchor="middle" class="text">发起/接受邀请，分享链接/码</text>
  <text x="560" y="350" text-anchor="middle" class="text">邀请页面、微信分享</text>
  <text x="560" y="450" text-anchor="middle" class="text">“怎么邀请？对方会收到吗？”</text>
  <text x="560" y="550" text-anchor="middle" class="feeling">忐忑、期待回应</text>
  <rect x="510" y="640" width="100" height="50" rx="5" ry="5" class="pain-point"></rect>
  <text x="560" y="665" text-anchor="middle" class="text">邀请流程复杂</text>
   <rect x="510" y="700" width="100" height="50" rx="5" ry="5" class="opportunity"></rect>
  <text x="560" y="725" text-anchor="middle" class="text">清晰指引，分享便捷</text>

  <!-- Stage 5: 核心体验 (发券/收券/查券) -->
  <text x="710" y="150" text-anchor="middle" class="text">核心体验</text>
  <text x="710" y="250" text-anchor="middle" class="text">创建券、填写信息、选择伴侣、发送；接收通知、查看列表/详情</text>
  <text x="710" y="350" text-anchor="middle" class="text">发券页、券列表、券详情</text>
  <text x="710" y="450" text-anchor="middle" class="text">“我要发什么券？对方收到会开心吗？我有什么券？”</text>
  <text x="710" y="550" text-anchor="middle" class="feeling">创意、期待、满足</text>
  <rect x="660" y="640" width="100" height="50" rx="5" ry="5" class="pain-point"></rect>
  <text x="710" y="665" text-anchor="middle" class="text">发券内容填写繁琐</text>
   <rect x="660" y="700" width="100" height="50" rx="5" ry="5" class="opportunity"></rect>
  <text x="710" y="725" text-anchor="middle" class="text">预设模板、快速发送</text>

  <!-- Stage 6: 核销/历史 -->
  <text x="860" y="150" text-anchor="middle" class="text">核销/历史</text>
  <text x="860" y="250" text-anchor="middle" class="text">在合适时机使用券，发起核销；查看已使用/已过期券</text>
  <text x="860" y="350" text-anchor="middle" class="text">券详情、历史记录页</text>
  <text x="860" y="450" text-anchor="middle" class="text">“现在是时候使用了！这次核销顺利吗？我们发过多少券了？”</text>
  <text x="860" y="550" text-anchor="middle" class="feeling">兴奋、满足、回忆</text>
  <rect x="810" y="640" width="100" height="50" rx="5" ry="5" class="pain-point"></rect>
  <text x="860" y="665" text-anchor="middle" class="text">核销流程不顺畅</text>
   <rect x="810" y="700" width="100" height="50" rx="5" ry="5" class="opportunity"></rect>
  <text x="860" y="725" text-anchor="middle" class="text">单方面快速核销</text>

  <!-- Arrows -->
  <path d="M170 400 L 200 400" class="arrow"></path>
  <path d="M320 400 L 350 400" class="arrow"></path>
  <path d="M470 400 L 500 400" class="arrow"></path>
  <path d="M620 400 L 650 400" class="arrow"></path>
  <path d="M770 400 L 800 400" class="arrow"></path>

</svg>
```

**用户旅程描述:**

1.  **发现/访问:** 用户可能通过朋友推荐、社交媒体推广或无意中在微信内看到小程序分享而发现。他们带着对新奇事物的**好奇心**，希望了解这是一个什么应用，能否为他们的关系带来一些新意。这是一个重要的**营销触达点**，清晰的产品定位和吸引人的slogan能有效抓住用户。
2.  **注册/登录:** 用户打开小程序后，通常会遇到微信授权登录。用户期望这一步**快速便捷**，不希望填写过多信息。如果授权流程出现卡顿或用户对授权内容有疑虑，容易产生**痛点**，导致流失。**机会点**在于利用微信一键授权的便利性。
3.  **理解价值:** 登录后，用户可能需要引导了解产品是做什么的，信誉券是什么，能带来什么好处。他们此时带着**求知欲**，如果首页或新手指引能清晰传达**核心价值**（承诺具象化、互动趣味化），并展示信誉券的样板，将抓住用户兴趣。
4.  **伴侣绑定:** 理解价值后，用户需要与伴侣建立关联才能使用核心功能。他们会进入伴侣绑定流程，通过生成和分享邀请链接/码完成。用户此时可能有些**忐忑**，担心邀请不成功或伴侣不感兴趣，同时也**期待**对方接受邀请。**痛点**在于邀请流程是否**复杂**、分享是否方便。**机会点**在于设计**清晰的绑定指引**和**便捷的分享方式**。
5.  **核心体验 (发券/收券/查券):** 完成绑定后，用户进入核心使用阶段。他们开始**创意**地思考要给伴侣发什么券，**期待**对方收到券的反应。同时，用户也会查看自己收到的券，好奇伴侣给了自己什么惊喜。这个阶段的情绪是**积极、有趣、期待**。主要的**痛点**可能在于券内容填写**是否繁琐**，列表展示是否清晰，新券通知是否及时。**机会点**在于提供**预设券模板**、**简洁的创建流程**、**及时的新券提醒**以及**清晰的券状态展示**。
6.  **核销/历史:** 当收到券的伴侣在合适时机想兑现承诺时，他们会找到相应的券并发起核销。此时用户带着**兴奋和期待**享受承诺兑现的甜蜜瞬间。核销成功后，券状态变为“已使用”，并进入历史记录。用户可以随时回顾发出的或收到的历史券，勾起甜蜜的**回忆**，感受关系的沉淀。**痛点**在于核销流程是否**顺畅**，特别是如果涉及双方确认（MVP建议单方面核销简化）。**机会点**在于设计**便捷的单方面核销**流程，以及**易于回顾的历史记录**功能。

贯穿整个旅程，用户的**情绪曲线**大致呈**U型反转**：初期可能略带犹豫和不确定，绑定成功并开始使用核心功能后，情绪迅速上升，享受发券、收券的乐趣；核销时刻达到情绪高峰（兑现承诺的甜蜜）；后续回顾历史时，情绪趋于平稳，感受到的是关系的积累与温暖。如果在任何阶段遇到痛点（如流程卡顿、功能复杂、通知不及时），情绪会下降。

### 2. 核心业务流程图 (Core Business Process Flows)

根据PRD的描述，我们梳理以下核心业务流程：

#### 2.1. 伴侣绑定流程

绘制为简化流程图：

```svg
<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <style>
    .start-end { fill: #FFC107; stroke: #FF9800; stroke-width: 2; }
    .process { fill: #2196F3; stroke: #1976D2; stroke-width: 2; }
    .decision { fill: #4CAF50; stroke: #388E3C; stroke-width: 2; }
    .arrow { stroke: #333; stroke-width: 2; marker-end: url(#arrowhead-black); }
    .text { font-family: sans-serif; font-size: 12px; fill: #333; text-anchor: middle; }
    #arrowhead-black { fill: #333; }
  </style>
  <defs>
    <marker id="arrowhead-black" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7"></polygon>
    </marker>
  </defs>

  <!-- Nodes -->
  <ellipse cx="300" cy="50" rx="60" ry="30" class="start-end"></ellipse>
  <text x="300" y="55" class="text">开始</text>

  <rect x="220" y="120" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="145" class="text" fill="#FFF">用户A发起绑定邀请</text>

  <rect x="220" y="200" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="225" class="text" fill="#FFF">生成邀请链接/码</text>

  <rect x="220" y="280" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="305" class="text" fill="#FFF">用户A分享邀请</text>

  <rect x="450" y="280" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="530" y="305" class="text" fill="#FFF">用户B点击/输入邀请</text>

  <rect x="450" y="360" width="160" height="50" rx="10" ry="10" class="process"></rect>
    <text x="530" y="385" class="text" fill="#FFF">系统身份验证</text>


  <rect x="220" y="360" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="385" class="text" fill="#FFF">用户B确认绑定</text>

    <rect x="220" y="440" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="465" class="text" fill="#FFF">系统建立绑定关系</text>


  <ellipse cx="300" cy="540" rx="60" ry="30" class="start-end"></ellipse>
  <text x="300" y="545" class="text">结束</text>

  <!-- Arrows -->
  <path d="M300 80 L 300 120" class="arrow"></path>
  <path d="M300 170 L 300 200" class="arrow"></path>
  <path d="M300 250 L 300 280" class="arrow"></path>
   <path d="M380 305 L 450 305" class="arrow"></path>
   <path d="M530 330 L 530 360" class="arrow"></path>
    <path d="M450 385 L 380 385" class="arrow"></path>
  <path d="M300 410 L 300 440" class="arrow"></path>
  <path d="M300 490 L 300 540" class="arrow"></path>

</svg>
```

*   **开始:** 用户A决定绑定伴侣。
*   **用户A发起绑定邀请:** 用户A在应用内选择发起伴侣绑定功能。
*   **生成邀请链接/码:** 系统生成一个唯一的邀请链接或邀请码。
*   **用户A分享邀请:** 用户A将邀请链接/码通过微信等渠道分享给伴侣B。
*   **用户B点击/输入邀请:** 伴侣B接收到邀请，在微信内点击链接或在小程序内输入邀请码。
*   **系统身份验证:** 系统验证邀请的有效性，并确认用户B的身份。
*   **用户B确认绑定:** 用户B在小程序内看到绑定请求，确认与对应的用户A进行绑定。
*   **系统建立绑定关系:** 系统更新用户A和用户B在`users` collection中的`partner_id`字段，使彼此关联。
*   **结束:** 伴侣绑定成功。

#### 2.2. 发券流程

```svg
<svg width="600" height="500" xmlns="http://www.w3.org/2000/svg">
  <style>
    .start-end { fill: #FFC107; stroke: #FF9800; stroke-width: 2; }
    .process { fill: #2196F3; stroke: #1976D2; stroke-width: 2; }
    .arrow { stroke: #333; stroke-width: 2; marker-end: url(#arrowhead-black); }
    .text { font-family: sans-serif; font-size: 12px; fill: #333; text-anchor: middle; }
    #arrowhead-black { fill: #333; }
  </style>
  <defs>
    <marker id="arrowhead-black" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7"></polygon>
    </marker>
  </defs>

  <!-- Nodes -->
  <ellipse cx="300" cy="50" rx="60" ry="30" class="start-end"></ellipse>
  <text x="300" y="55" class="text">开始 (用户在发券页)</text>

  <rect x="220" y="120" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="145" class="text" fill="#FFF">填写券信息 (名称, 类型, 描述, 有效期)</text>

  <rect x="220" y="200" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="225" class="text" fill="#FFF">选择接收人 (已绑定伴侣)</text>

  <rect x="220" y="280" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="305" class="text" fill="#FFF">填写发送留言 (可选)</text>

  <rect x="220" y="360" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="385" class="text" fill="#FFF">确认发送</text>

  <rect x="220" y="440" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="465" class="text" fill="#FFF">系统创建/更新券记录 (status: pending, recipient_id)</text>

  <rect x="220" y="520" width="160" height="50" rx="10" ry="10" class="process"></rect>
   <text x="300" y="545" class="text" fill="#FFF">系统通知接收人 (如通过服务通知)</text>


  <ellipse cx="300" cy="600" rx="60" ry="30" class="start-end"></ellipse>
  <text x="300" y="605" class="text">结束</text>

  <!-- Arrows -->
  <path d="M300 80 L 300 120" class="arrow"></path>
  <path d="M300 170 L 300 200" class="arrow"></path>
  <path d="M300 250 L 300 280" class="arrow"></path>
  <path d="M300 330 L 300 360" class="arrow"></path>
  <path d="M300 410 L 300 440" class="arrow"></path>
  <path d="M300 490 L 300 520" class="arrow"></path>
  <path d="M300 570 L 300 600" class="arrow"></path>

</svg>
```

*   **开始:** 用户在创建券页面或“我发出的”待发送列表发起发送操作。
*   **填写券信息:** 用户输入券的名称、类型、描述，并设置有效期。
*   **选择接收人:** 用户从已绑定的伴侣列表中选择唯一的接收人。
*   **填写发送留言:** 用户可选择性地输入附带的留言。
*   **确认发送:** 用户点击发送按钮。
*   **系统创建/更新券记录:**
    *   如果是在创建后直接发送，则在`vouchers` collection中创建一条新记录，设置`name`, `type`, `description`, `expiration_date`, `issuer_id` 为当前用户ID, `recipient_id` 为所选伴侣ID, `status` 为 "pending", `creation_time` 为当前时间，填写 `issue_message`。
    *   如果是从待发送列表选择已创建的券发送，则更新该券记录的`recipient_id`, `status`为 "pending", 并填写 `issue_message`。
*   **系统通知接收人:**（如条件允许）系统通过微信服务通知等方式提醒接收方收到了新券。
*   **结束:** 券发送成功，可在“我发出的”列表中看到该券状态为“待使用”。

#### 2.3. 收券与查券流程

```svg
<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <style>
    .start-end { fill: #FFC107; stroke: #FF9800; stroke-width: 2; }
    .process { fill: #2196F3; stroke: #1976D2; stroke-width: 2; }
    .decision { fill: #4CAF50; stroke: #388E3C; stroke-width: 2; }
    .arrow { stroke: #333; stroke-width: 2; marker-end: url(#arrowhead-black); }
    .text { font-family: sans-serif; font-size: 12px; fill: #333; text-anchor: middle; }
    #arrowhead-black { fill: #333; }
  </style>
  <defs>
    <marker id="arrowhead-black" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7"></polygon>
    </marker>
  </defs>

  <!-- Nodes -->
  <ellipse cx="300" cy="50" rx="60" ry="30" class="start-end"></ellipse>
  <text x="300" y="55" class="text">开始</text>

  <rect x="220" y="120" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="145" class="text" fill="#FFF">用户打开小程序 / 收到服务通知</text>

  <rect x="220" y="200" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="225" class="text" fill="#FFF">进入“我收到的券”列表</text>

  <rect x="220" y="280" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="305" class="text" fill="#FFF">浏览券列表 (状态: 待使用)</text>

  <rect x="100" y="360" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="180" y="385" class="text" fill="#FFF">点击列表项</text>

   <rect x="220" y="440" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="465" class="text" fill="#FFF">进入券详情页</text>
  <text x="300" y="485" class="text" fill="#FFF">(查看名称, 描述, 有效期, 发行人等)</text>

  <ellipse cx="450" cy="385" rx="60" ry="30" class="start-end"></ellipse>
  <text x="450" y="390" class="text">结束 (退出)</text>

  <ellipse cx="300" cy="540" rx="60" ry="30" class="start-end"></ellipse>
  <text x="300" y="545" class="text">结束 (查看详情)</text>

  <!-- Arrows -->
  <path d="M300 80 L 300 120" class="arrow"></path>
  <path d="M300 170 L 300 200" class="arrow"></path>
  <path d="M300 250 L 300 280" class="arrow"></path>
   <path d="M300 330 L 300 360" class="arrow"></path>
   <path d="M300 330 L 450 360" class="arrow"></path>
    <path d="M180 410 L 180 440 C 180 440, 300 440, 300 440" class="arrow"></path>
    <path d="M300 490 L 300 540" class="arrow"></path>

</svg>
```

*   **开始:** 用户打开小程序（可能因为收到通知）或决定主动查看收到的券。
*   **用户打开小程序/收到服务通知:** 用户进入应用。如果有新券通知，可以引导至相关页面。
*   **进入“我收到的券”列表:** 用户导航到展示自己作为接收人的券列表页面。
*   **浏览券列表:** 用户查看状态为“待使用”的券列表，列表项简洁展示关键信息（如名称、发行人、状态）。新收到的券可能有视觉提示。
*   **点击列表项:** 用户选择一个列表项，希望查看更多细节。
*   **进入券详情页:** 页面展示该券的完整信息，包括名称、类型、描述、有效期、发行人、创建时间、发送留言等。
*   **结束 (查看详情):** 用户完成券信息的查阅。
*   **结束 (退出):** 用户浏览列表后退出当前页面。

用户也可以通过“我发出的”列表查看自己发出的券（待发送、待使用、已使用、已过期）。流程类似，只是列表展示的券集合和可执行的操作（如待发送券的发送操作）不同。

#### 2.4. 核销券流程 (MVP 简化版，接收方单方面核销)

```svg
<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <style>
    .start-end { fill: #FFC107; stroke: #FF9800; stroke-width: 2; }
    .process { fill: #2196F3; stroke: #1976D2; stroke-width: 2; }
    .arrow { stroke: #333; stroke-width: 2; marker-end: url(#arrowhead-black); }
    .text { font-family: sans-serif; font-size: 12px; fill: #333; text-anchor: middle; }
    #arrowhead-black { fill: #333; }
  </style>
  <defs>
    <marker id="arrowhead-black" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7"></polygon>
    </marker>
  </defs>

  <!-- Nodes -->
  <ellipse cx="300" cy="50" rx="60" ry="30" class="start-end"></ellipse>
  <text x="300" y="55" class="text">开始 (接收方在券详情页)</text>

  <rect x="220" y="120" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="145" class="text" fill="#FFF">找到待使用券</text>

  <rect x="220" y="200" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="225" class="text" fill="#FFF">点击“使用此券/核销”按钮</text>

  <rect x="220" y="280" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="305" class="text" fill="#FFF">系统更新券状态为“已使用”</text>
  <text x="300" y="325" class="text" fill="#FFF">(更新 status='used', used_time=now)</text>


  <rect x="220" y="360" width="160" height="50" rx="10" ry="10" class="process"></rect>
  <text x="300" y="385" class="text" fill="#FFF">双方列表/详情页显示状态变更</text>

  <ellipse cx="300" cy="440" rx="60" ry="30" class="start-end"></ellipse>
  <text x="300" y="445" class="text">结束</text>

  <!-- Arrows -->
  <path d="M300 80 L 300 120" class="arrow"></path>
  <path d="M300 170 L 300 200" class="arrow"></path>
  <path d="M300 250 L 300 280" class="arrow"></path>
   <path d="M300 335 L 300 360" class="arrow"></path>
    <path d="M300 410 L 300 440" class="arrow"></path>

</svg>
```

*   **开始:** 接收方在“我收到的券”列表或详情页找到状态为“待使用”的券。
*   **找到待使用券:** 接收方确认要使用某张券。
*   **点击“使用此券/核销”按钮:** 接收方在券详情页点击核销按钮。
*   **系统更新券状态为“已使用”:** 系统根据券ID，在`vouchers` collection中将该券记录的`status`字段更新为 "used"，并记录当前的`used_time`。
*   **双方列表/详情页显示状态变更:** 发行人（在“我发出的”列表）和接收人（在“我收到的”列表）刷新页面后，该券的状态均显示为“已使用”，核销按钮消失。
*   **结束:** 券核销完成。

### 3. 产品功能结构思维导图 (Product Functional Structure Mind Map)

以下思维导图展示了MVP版本的主要功能模块及其层级结构：

```
情侣信誉券应用 (MVP)
├── 用户账户管理
│   ├── 微信一键授权登录
│   ├── 获取/展示微信昵称、头像
│   └── 伴侣绑定机制
│       ├── 发起绑定邀请
│       ├── 生成邀请链接/码
│       └── 接受绑定确认
├── 信誉券创建
│   ├── 填写券名称 (必填)
│   ├── 选择/自定义券类型
│   ├── 填写券描述 (选填)
│   ├── 设置有效期 (预设/指定日期)
│   └── (MVP可选) 券面简单自定义
├── 信誉券发送
│   ├── 从创建页发送
│   ├── 从待发送列表选择发送
│   ├── 选择接收人 (已绑定伴侣)
│   └── 填写发送前留言 (选填)
├── 信誉券接收与提醒
│   ├── “我收到的券”列表新券提示
│   └── (如条件允许) 微信服务通知提醒
├── 信誉券查看与管理
│   ├── 券列表 (Tab: 我发出的 / 我收到的)
│   │   ├── 展示券状态 (待发送, 待使用, 已使用, 已过期)
│   │   └── 列表项关键信息展示
│   └── 券详情
│       ├── 展示所有券信息
│       └── 根据状态显示操作按钮
├── 信誉券核销
│   ├── 接收方发起核销
│   ├── 点击“使用此券/核销”按钮
│   └── 系统单方面更新券状态为“已使用”
├── 历史记录
│   ├── 查看所有非“待发送/待使用”状态券
│   ├── 按状态过滤 (已使用 / 已过期)
│   └── 查看历史券详情
└── 设置 (简略)
    └── (MVP阶段可能仅含) 退出登录
```

### 4. 关键交互时序图 (Sequence Diagrams for Key Interactions)

根据PRD，MVP阶段的核销流程是接收方单方面操作，不涉及发行方确认，因此流程较为简单，与业务流程图中的核销部分逻辑相同。伴侣绑定流程相对复杂，涉及用户A、用户B和系统，适合绘制时序图。

#### 4.1. 伴侣绑定时序图

该图描述了用户A邀请用户B进行伴侣绑定的消息交互顺序：

```latex
\documentclass[12pt]{article}
\usepackage{tikz}
\usepackage{pgf}
\usepackage{tikz-sequence}

\begin{document}

\begin{sequence}{5cm}
  % Participants
  \participant{User A}
  \participant{User B}
  \participant{MiniProgram System}

  % Messages
  \message{User A}{发起绑定}{MiniProgram System}
    \postcall{MiniProgram System}{生成唯一邀请链接/码}
  \message{MiniProgram System}{返回邀请链接/码}{User A}
  \message{User A}{分享邀请链接/码 (通过微信等)}{User B}
  \message{User B}{访问邀请链接/码}{MiniProgram System}
    \postcall{MiniProgram System}{验证邀请有效性, 识别User B}
  \message{MiniProgram System}{显示绑定确认页}{User B}
  \message{User B}{确认绑定}{MiniProgram System}
  \message{MiniProgram System}{更新User A & User B records \\ (Set partner\_id)}{MiniProgram System}
    \postcall{MiniProgram System}{绑定成功}
  \message{MiniProgram System}{通知User A绑定成功}{User A}


\end{sequence}

\end{document}
```

**时序描述:**

1.  **User A** 在小程序中发起绑定操作。
2.  小程序**System** 接收到请求，生成一个唯一的邀请链接或邀请码。
3.  **System** 将生成的邀请链接/码返回给 **User A**。
4.  **User A** 通过微信等外部渠道将邀请链接/码分享给 **User B**。
5.  **User B** 在微信内点击链接或在小程序内输入邀请码，访问 **System**。
6.  **System** 验证邀请的有效性，并根据User B的微信身份识别User B。
7.  **System** 向 **User B** 展示与 User A 绑定关系的确认页面。
8.  **User B** 在确认页面选择确认绑定，将确认消息发送给 **System**。
9.  **System** 接收到 User B 的确认，更新数据库中 User A 和 User B 的用户记录，将彼此的 `partner_id` 设为对方的 `_id`。完成绑定关系建立。
10. **System** 通知 **User A** 绑定关系已成功建立。

此图清晰地展示了伴侣绑定过程中，两位用户与小程序后端系统之间的信息传递顺序和交互点。