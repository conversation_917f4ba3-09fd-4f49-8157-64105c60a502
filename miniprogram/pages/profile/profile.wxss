/* profile.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 50%, #F0F8FF 100%);
  padding: 30rpx;
  box-sizing: border-box;
  padding-bottom: 150rpx;
}

/* 用户信息区域 */
.user-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.user-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid #FFB6C1;
}

.user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-nickname {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.user-signature {
  font-size: 24rpx;
  color: #666;
  font-style: italic;
  opacity: 0.8;
}

.profile-status {
  margin-top: 8rpx;
}

.auto-profile-hint {
  font-size: 20rpx;
  color: #FF69B4;
  background: rgba(255, 105, 180, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  border: 1rpx solid rgba(255, 105, 180, 0.2);
}

.edit-button {
  width: 60rpx;
  height: 60rpx;
  background: #FF69B4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon {
  font-size: 24rpx;
  color: white;
}

/* 完善资料提示 */
.profile-complete-hint {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 165, 0, 0.1));
  border-radius: 16rpx;
  padding: 24rpx;
  margin-top: 20rpx;
  border: 1rpx solid rgba(255, 165, 0, 0.3);
}

.hint-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.hint-icon {
  font-size: 28rpx;
}

.hint-text {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.complete-profile-btn {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(255, 165, 0, 0.3);
  min-width: 140rpx;
}

.complete-profile-btn:active {
  transform: scale(0.95);
}

/* 情侣关系状态 */
.relationship-status {
  background: rgba(255, 105, 180, 0.05);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(255, 105, 180, 0.2);
}

.couple-card {
  text-align: center;
}

.couple-avatars {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.couple-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 2rpx solid #FFB6C1;
}

.heart-connector {
  font-size: 30rpx;
  animation: heartbeat 2s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.couple-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.couple-names {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.together-days {
  font-size: 24rpx;
  color: #666;
}

.unbind-hint {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
}

.hint-icon {
  font-size: 40rpx;
  opacity: 0.6;
}

.hint-text {
  font-size: 26rpx;
  color: #666;
}

.bind-btn {
  background: linear-gradient(45deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.3);
}

/* 快捷入口 */
.quick-actions {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  gap: 20rpx;
  position: relative;
  transition: all 0.3s ease;
}

.item-hover {
  background: #e9ecef;
  transform: scale(0.98);
}

.action-icon {
  font-size: 32rpx;
  width: 50rpx;
  text-align: center;
}

.action-title {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.action-badge {
  background: #FF4444;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.action-arrow {
  font-size: 20rpx;
  color: #FF69B4;
  font-weight: bold;
}

/* 功能菜单 */
.menu-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.menu-group {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.group-title {
  padding: 20rpx 30rpx 16rpx;
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  background: rgba(248, 249, 250, 0.8);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  gap: 20rpx;
  position: relative;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 28rpx;
  width: 40rpx;
  text-align: center;
}

.menu-title {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-badge {
  background: #FF4444;
  color: white;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.menu-arrow {
  font-size: 20rpx;
  color: #ccc;
  font-weight: bold;
}

/* 数据统计 */
.stats-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.stats-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx 16rpx;
  background: rgba(255, 105, 180, 0.05);
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 105, 180, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100rpx;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: rgba(255, 105, 180, 0.08);
  transform: translateY(-2rpx);
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #FF69B4;
  margin-bottom: 8rpx;
  line-height: 1;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
  line-height: 1.2;
  text-align: center;
}

/* 底部操作 */
.bottom-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.danger-btn, .secondary-btn {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.danger-btn {
  background: linear-gradient(45deg, #FF6B6B, #FF8E8E);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  border: 1rpx solid #ddd;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80vw;
  max-width: 500rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.modal-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.modal-body {
  padding: 30rpx;
}

.warning-text {
  font-size: 26rpx;
  color: #FF6B6B;
  line-height: 1.5;
  margin-bottom: 24rpx;
  display: block;
}

.confirm-input {
  width: 100%;
  height: 80rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}

.modal-actions {
  display: flex;
  border-top: 1rpx solid #e9ecef;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border-right: 1rpx solid #e9ecef;
}

.confirm-btn.danger {
  background: #FF6B6B;
  color: white;
}

.confirm-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF69B4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 20rpx;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 骨架屏样式 */
.skeleton-container {
  padding: 30rpx;
  animation: skeleton-loading 1.5s infinite ease-in-out;
}

@keyframes skeleton-loading {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.skeleton-user-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
}

.skeleton-user-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.skeleton-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
}

.skeleton-user-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.skeleton-text {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 8rpx;
}

.skeleton-nickname {
  height: 32rpx;
  width: 200rpx;
}

.skeleton-id {
  height: 24rpx;
  width: 150rpx;
}

.skeleton-edit-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
}

.skeleton-relationship {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
  padding: 24rpx;
  background: rgba(255, 105, 180, 0.05);
  border-radius: 16rpx;
}

.skeleton-relationship-text {
  height: 28rpx;
  width: 250rpx;
}

.skeleton-btn {
  height: 60rpx;
  width: 120rpx;
  border-radius: 30rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
}

.skeleton-actions {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.skeleton-action-item {
  height: 72rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 16rpx;
}

.skeleton-stats {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.skeleton-stats::before {
  content: '';
  display: block;
  height: 32rpx;
  width: 120rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 8rpx;
  margin-bottom: 24rpx;
}

.skeleton-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.skeleton-stat-item {
  height: 120rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: 16rpx;
}

@keyframes skeleton-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 新的解绑弹窗样式 */
.modal-overlay-new {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.unbind-modal {
  background: white;
  border-radius: 32rpx;
  padding: 0;
  margin: 30rpx;
  max-width: 600rpx;
  width: calc(100vw - 60rpx);
  max-height: 85vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  animation: slideUp 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  display: flex;
  flex-direction: column;
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-decoration {
  text-align: center;
  padding: 30rpx 30rpx 15rpx;
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 100%);
  border-radius: 32rpx 32rpx 0 0;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.modal-decoration::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

.broken-heart {
  font-size: 70rpx;
  animation: heartBreak 2s ease-in-out infinite;
  position: relative;
  z-index: 1;
}

@keyframes heartBreak {
  0%, 100% { 
    transform: scale(1) rotate(0deg);
    filter: hue-rotate(0deg);
  }
  25% { 
    transform: scale(1.1) rotate(-5deg);
    filter: hue-rotate(10deg);
  }
  75% { 
    transform: scale(1.1) rotate(5deg);
    filter: hue-rotate(-10deg);
  }
}

.decoration-line {
  height: 4rpx;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  margin: 20rpx auto 0;
  width: 60%;
  border-radius: 2rpx;
}

.modal-title-section {
  text-align: center;
  padding: 20rpx 30rpx 15rpx;
  flex-shrink: 0;
}

.modal-main-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.modal-subtitle {
  font-size: 24rpx;
  color: #666;
  font-style: italic;
  opacity: 0.8;
  display: block;
}

.warning-content {
  padding: 15rpx 30rpx;
  flex: 1;
  min-height: 0;
}

.warning-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  animation: slideInLeft 0.6s ease-out;
  animation-fill-mode: both;
}

.warning-item:nth-child(1) { animation-delay: 0.1s; }
.warning-item:nth-child(2) { animation-delay: 0.2s; }
.warning-item:nth-child(3) { animation-delay: 0.3s; }
.warning-item:nth-child(4) { animation-delay: 0.4s; }

.warning-item:last-child {
  border-bottom: none;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-30rpx);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.warning-icon {
  font-size: 28rpx;
  width: 36rpx;
  text-align: center;
  flex-shrink: 0;
}

.warning-text {
  font-size: 24rpx;
  color: #555;
  flex: 1;
  line-height: 1.3;
}

.retention-text {
  text-align: center;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, rgba(255, 182, 193, 0.1), rgba(255, 192, 203, 0.1));
  margin: 15rpx 30rpx;
  border-radius: 16rpx;
  border: 1rpx solid rgba(255, 182, 193, 0.3);
  animation: pulse 2s ease-in-out infinite;
  flex-shrink: 0;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.retention-main {
  font-size: 26rpx;
  font-weight: 600;
  color: #FF69B4;
  display: block;
  margin-bottom: 6rpx;
}

.retention-sub {
  font-size: 22rpx;
  color: #666;
  display: block;
  line-height: 1.3;
}

.confirm-section {
  padding: 15rpx 30rpx;
  flex-shrink: 0;
}

.confirm-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
  text-align: center;
}

.confirm-input-new {
  width: 100%;
  height: 70rpx;
  border: 2rpx solid #ddd;
  border-radius: 16rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  text-align: center;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.confirm-input-new:focus {
  border-color: #FF69B4;
  box-shadow: 0 0 0 4rpx rgba(255, 105, 180, 0.1);
}

.modal-buttons {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  gap: 16rpx;
  flex-shrink: 0;
}

.stay-btn, .unbind-btn {
  flex: 1;
  height: 70rpx;
  border-radius: 18rpx;
  border: none;
  font-size: 26rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stay-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(255, 105, 180, 0.3);
}

.stay-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.3);
}

.unbind-btn {
  background: #f5f5f5;
  color: #999;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.unbind-btn.active {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(255, 107, 107, 0.3);
}

.unbind-btn.active:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.btn-text {
  position: relative;
  z-index: 1;
}

/* 按钮波纹效果 */
.stay-btn::before, .unbind-btn.active::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.stay-btn:active::before, .unbind-btn.active:active::before {
  width: 200%;
  height: 200%;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .unbind-modal {
    margin: 20rpx;
    width: calc(100vw - 40rpx);
    max-height: 90vh;
  }
  
  .modal-decoration {
    padding: 25rpx 25rpx 12rpx;
  }
  
  .broken-heart {
    font-size: 60rpx;
  }
  
  .modal-title-section {
    padding: 15rpx 25rpx 12rpx;
  }
  
  .modal-main-title {
    font-size: 28rpx;
  }
  
  .modal-subtitle {
    font-size: 22rpx;
  }
  
  .warning-content {
    padding: 12rpx 25rpx;
  }
  
  .warning-item {
    padding: 10rpx 0;
  }
  
  .warning-text {
    font-size: 22rpx;
  }
  
  .retention-text {
    padding: 16rpx 25rpx;
    margin: 12rpx 25rpx;
  }
  
  .retention-main {
    font-size: 24rpx;
  }
  
  .retention-sub {
    font-size: 20rpx;
  }
  
  .confirm-section {
    padding: 12rpx 25rpx;
  }
  
  .confirm-input-new {
    height: 60rpx;
    font-size: 24rpx;
  }
  
  .modal-buttons {
    padding: 16rpx 25rpx 25rpx;
    gap: 12rpx;
  }
  
  .stay-btn, .unbind-btn {
    height: 60rpx;
    font-size: 24rpx;
  }
}

