// pages/couponDetail/couponDetail.js
const app = getApp();

Page({
  data: {
    couponId: null,
    coupon: null,
    isLoading: true,
    userInfo: null,
    isCreator: false,
    isRecipient: false,
    showRedeemModal: false,
    redemptionNote: '',
    themeGradient: app.globalData.gradientBackground,
    canBeRedeemed: false,
    canCreatorConfirmRedemption: false,
    canBeCancelled: false,
    dynamicStatusText: '',
    formattedTimes: {
      createTime: '',
      sendTime: '',
      receiveTime: '',
      redeemTime: '',
      expiryDate: ''
    }
  },

  onLoad: function (options) {
    this.setData({ 
      couponId: options.id,
      userInfo: app.globalData.userInfo,
      themeGradient: app.globalData.gradientBackground,
    });
    if (this.data.couponId) {
      this.loadCouponDetails();
    } else {
      wx.showToast({ title: '无效的券ID', icon: 'none', duration: 2000 });
      setTimeout(() => wx.navigateBack(), 2000);
    }
  },

  loadCouponDetails: function () {
    this.setData({ isLoading: true });
    wx.cloud.callFunction({
      name: 'getCouponDetail',
      data: { couponId: this.data.couponId },
      success: res => {
        if (res.result && res.result.success) {
          const coupon = res.result.data;
          const userInfo = this.data.userInfo;
          console.log('=== 券详情加载成功 ===');
          console.log('券信息:', {
            _id: coupon._id,
            name: coupon.name,
            status: coupon.status,
            creatorId: coupon.creatorId,
            recipientId: coupon.recipientId,
            redeemApplyTime: coupon.redeemApplyTime,
            redeemTime: coupon.redeemTime
          });
          console.log('用户信息:', {
            _openid: userInfo?._openid,
            nickName: userInfo?.nickName
          });

          const isCreator = userInfo && coupon.creatorId === userInfo._openid;
          const isRecipient = userInfo && coupon.recipientId === userInfo._openid;

          console.log('权限判断:', {
            isCreator: isCreator,
            isRecipient: isRecipient
          });

          const dynamicStatusText = this.calculateDynamicStatusText(coupon);
          console.log('计算的状态文本:', dynamicStatusText);

          this.setData({
            coupon: coupon,
            isCreator: isCreator,
            isRecipient: isRecipient,
            canBeRedeemed: this.calculateCanBeRedeemed(coupon, isRecipient),
            canCreatorConfirmRedemption: this.calculateCanCreatorConfirmRedemption(coupon, isCreator),
            canBeCancelled: this.calculateCanBeCancelled(coupon, isCreator),
            dynamicStatusText: dynamicStatusText,
            formattedTimes: this.calculateFormattedTimes(coupon)
          });

          console.log('页面数据更新完成');
        } else {
          console.error('获取券详情失败:', res.result);
          wx.showToast({ title: res.result.message || '加载失败', icon: 'none' });
        }
      },
      fail: err => {
        console.error('网络请求失败:', err);
        wx.showToast({ title: '网络错误', icon: 'none' });
      },
      complete: () => this.setData({ isLoading: false })
    });
  },
  
  calculateCanBeRedeemed: function(coupon, isRecipient) {
    if (!coupon || !isRecipient) return false;
    const now = new Date().getTime();
    return coupon.status === 'received' && (!coupon.expiryDate || new Date(coupon.expiryDate).getTime() > now);
  },

  calculateCanCreatorConfirmRedemption: function(coupon, isCreator) {
    return coupon && isCreator && coupon.status === 'pending_redeem';
  },

  calculateCanBeCancelled: function(coupon, isCreator) {
    // 移除取消券功能：券发出后就代表已经送给对方，不可取消
    return false;
  },

  calculateDynamicStatusText: function(coupon) {
    if (!coupon) {
      console.log('状态计算：券信息为空');
      return "加载中...";
    }

    console.log('=== 计算券状态文本 ===');
    console.log('券状态:', coupon.status);
    console.log('过期时间:', coupon.expiryDate);

    const now = new Date().getTime();
    const isExpired = coupon.expiryDate && new Date(coupon.expiryDate).getTime() <= now;

    console.log('当前时间:', new Date(now).toISOString());
    console.log('是否过期:', isExpired);

    // 检查是否过期（但不是已兑现或已取消的券）
    if (coupon.status !== 'redeemed' && coupon.status !== 'cancelled' && isExpired) {
        console.log('状态结果: 已过期失效');
        return "已过期失效";
    }

    let statusText = '';
    switch (coupon.status) {
      case 'draft':
        statusText = '草稿状态 (未发送)';
        break;
      case 'received':
        statusText = '待兑现';
        break;
      case 'pending_redeem':
        statusText = '申请兑现中';
        break;
      case 'redeemed':
        statusText = '已兑现';
        break;
      case 'expired':
        statusText = '已过期';
        break;
      case 'cancelled':
        statusText = '已被发起者作废';
        break;
      default:
        statusText = '状态未知';
        break;
    }

    console.log('状态结果:', statusText);
    return statusText;
  },

  calculateFormattedTimes: function(coupon) {
    const now = new Date().getTime();
    const isExpired = coupon.expiryDate && new Date(coupon.expiryDate).getTime() <= now;

    // 构建时间轴数据
    const timeline = {
      sendTime: this.formatDateTime(coupon.sendTime),
      requestTime: coupon.redeemApplyTime ? this.formatDateTime(coupon.redeemApplyTime) : null,
      finalTime: null,
      finalTimeLabel: '',
      expiryDate: coupon.expiryDate ? this.formatDateTime(coupon.expiryDate) : '长期有效'
    };

    // 确定最终时间显示
    if (coupon.status === 'redeemed' && coupon.redeemTime) {
      timeline.finalTime = this.formatDateTime(coupon.redeemTime);
      timeline.finalTimeLabel = '兑现时间';
    } else if (isExpired && coupon.status !== 'redeemed') {
      timeline.finalTime = this.formatDateTime(coupon.expiryDate);
      timeline.finalTimeLabel = '过期时间';
    }

    return timeline;
  },

  formatDateTime: function (timestamp) {
    if (!timestamp) return 'N/A';
    const date = new Date(timestamp);
    return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}.${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  },

  // 移除confirmReceive方法：新流程中券发送后直接为已接收状态

  requestRedeem: function () {
    if (!this.data.canBeRedeemed) return;
    
    wx.showModal({
      title: '申请兑现',
      content: `确定要向 ${this.data.coupon.creatorNickname} 申请兑现这张【${this.data.coupon.name}】吗?`,
      confirmText: "申请兑现",
      cancelText: "再想想",
      success: res => {
        if (res.confirm) {
          this.performAction('redeem');
        }
      }
    });
  },

  confirmRedemption: function() {
    if (!this.data.canCreatorConfirmRedemption) return;
    this.setData({ showRedeemModal: true });
  },

  onRedemptionNoteInput: function(e) {
    this.setData({ redemptionNote: e.detail.value});
  },

  handleConfirmRedemption: function() {
    this.performAction('approve', { note: this.data.redemptionNote });
    this.setData({ showRedeemModal: false, redemptionNote: '' });
  },
  
  closeRedeemModal: function() {
    this.setData({ showRedeemModal: false, redemptionNote: '' });
  },

  // 移除cancelCoupon方法：新流程中券发出后就代表已经送给对方，不可取消

  /**
   * 更新通知状态
   */
  updateNotificationStatus: function(actionType, newCouponStatus) {
    let notificationAction = '';
    
    // 映射券操作到通知操作
    switch (actionType) {
      case 'redeem':
        notificationAction = 'apply_redeem';
        break;
      case 'approve':
        notificationAction = 'approve_redeem';
        break;
      case 'reject':
        notificationAction = 'reject_redeem';
        break;
      default:
        return; // 不需要更新通知的操作
    }
    
    // 调用通知状态更新云函数
    wx.cloud.callFunction({
      name: 'updateNotificationStatus',
      data: {
        couponId: this.data.couponId,
        newCouponStatus: newCouponStatus,
        action: notificationAction
      },
      success: res => {
        console.log('通知状态更新成功:', res.result);
      },
      fail: err => {
        console.error('通知状态更新失败:', err);
      }
    });
  },

  performAction: function (actionType, additionalData = {}) {
    wx.showLoading({ title: '处理中...' });
    wx.cloud.callFunction({
      name: 'updateCouponStatus',
      data: {
        couponId: this.data.couponId,
        action: actionType,
        ...additionalData
      },
      success: res => {
        wx.hideLoading();
        if (res.result && res.result.success) {
          // 更新通知状态
          this.updateNotificationStatus(actionType, res.result.data.newStatus);
          wx.showToast({ title: res.result.message || '操作成功', icon: 'success' });
          this.loadCouponDetails();

          // 操作成功后检查未读消息数量，确保徽标实时更新
          if (app.globalData.userInfo) {
            setTimeout(() => {
              app.checkUnreadMessages();
            }, 500);
          }
        } else {
          wx.showToast({ title: res.result.message || '操作失败', icon: 'none' });
        }
      },
      fail: err => {
        wx.hideLoading();
        wx.showToast({ title: '网络请求失败', icon: 'none' });
        console.error(`Action ${actionType} failed:`, err);
      }
    });
  },

  onPullDownRefresh: function () {
    this.loadCouponDetails();
    wx.stopPullDownRefresh();
  },

  onShareAppMessage: function () {
    return {
      title: `来自${this.data.coupon.creatorNickname}的信任券：${this.data.coupon.name}`,
      path: `/pages/couponDetail/couponDetail?id=${this.data.couponId}`
    };
  }
});
