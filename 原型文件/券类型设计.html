<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情侣信誉券灵感宝库</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Chosen Palette: Warm Harmony -->
    <!-- Application Structure Plan: 本应用采用单页仪表盘式布局。顶部为标题和简介，核心区域分为两部分：左侧是券卡分类的数据可视化图表（甜甜圈图），直观展示各类券卡的数量分布；右侧是主内容区，包含分类筛选按钮和券卡展示网格。用户可通过点击按钮快速筛选浏览特定类别的券卡。这种结构将报告的核心数据（券卡列表）转化为一个易于导航和探索的交互式工具，旨在优化用户发现和选择创意的体验，而非简单复现报告的线性结构。 -->
    <!-- Visualization & Content Choices: 1. **券卡分类概览**: 报告中10个分类及其数量 -> 目标: 宏观展示内容分布 -> 呈现方式: 甜甜圈图 -> 交互: 鼠标悬浮提示类别和数量 -> 理由: 将报告的结构性信息可视化，提供一个有趣的数据概览，增加应用的专业感。 -> 库: Chart.js (Canvas)。 2. **券卡列表**: 报告中100+条券卡详情 -> 目标: 探索和发现具体创意 -> 呈现方式: 响应式卡片网格 -> 交互: 点击分类按钮进行筛选 -> 理由: 卡片是展示独立信息的最佳形式，筛选功能是浏览大型数据集的核心交互，直观高效。 -> 库: Vanilla JS + Tailwind CSS。 -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #FFFBF5;
            color: #4A4A4A;
        }
        .active-filter {
            background-color: #E57373 !important;
            color: white !important;
            font-weight: 500;
            transform: scale(1.05);
        }
        .filter-btn {
            transition: all 0.2s ease-in-out;
        }
        .card {
            transition: all 0.3s ease-in-out;
            border-top-width: 4px;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
            max-width: 400px;
            margin: auto;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
    </style>
</head>
<body class="antialiased">

    <div class="container mx-auto px-4 py-8 md:py-12">
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-2">💌 情侣信誉券灵感宝库</h1>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                欢迎来到情侣信誉券的世界！这里汇集了超过100个创意点子，旨在为你们的生活增添甜蜜、趣味和浪漫。点击下方分类，探索属于你们的专属惊喜吧！
            </p>
        </header>

        <main>
            <section id="dashboard" class="mb-12">
                 <h2 class="text-2xl font-bold text-center text-gray-700 mb-6">券卡类型分布</h2>
                 <p class="text-center text-gray-500 mb-6 max-w-2xl mx-auto">本创意库涵盖10大核心类型，从贴心服务到浪漫体验，全方位覆盖情侣生活的方方面面。下图展示了各类券卡的数量分布，帮助您快速了解内容构成。</p>
                <div class="chart-container">
                    <canvas id="categoryChart"></canvas>
                </div>
            </section>
            
            <section id="explorer">
                <h2 class="text-2xl font-bold text-center text-gray-700 mb-6">创意券卡浏览器</h2>
                <p class="text-center text-gray-500 mb-8 max-w-2xl mx-auto">这里是所有创意券卡的集合。您可以点击下方的分类按钮进行筛选，快速找到最符合当下心情和场景的券卡。每一张卡片都可能开启一段美好的回忆。</p>
                <div id="filters" class="flex flex-wrap justify-center gap-2 md:gap-3 mb-10">
                </div>

                <div id="voucher-grid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                </div>
            </section>
        </main>
    </div>

    <footer class="text-center py-6 mt-12 bg-white/50">
        <p class="text-gray-500">灵感源自生活，用创意点亮爱情。</p>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const VOUCHERS = [
                { category: '服务券', name: '暖心早餐投喂券', icon: '🥐', description: '凭此券可兑换一个不用早起的清晨，和我亲手准备的爱心早餐。', color: '温馨粉' },
                { category: '服务券', name: '全能免“做”金牌', icon: '✨', description: '今日家务我全包！持此金牌，你可以心安理得地“葛优躺”一整天。', color: '清新绿' },
                { category: '服务券', name: '御用按摩大师券', icon: '💆‍♀️', description: '你的御用按摩大师随时待命，提供至少15分钟的专属放松服务。', color: '疗愈紫' },
                { category: '服务券', name: '跑腿超人召唤券', icon: '🏃‍♂️', description: '无论取快递还是买奶茶，一声令下，跑腿超人使命必达！', color: '活力橙' },
                { category: '服务券', name: '专属司机接送券', icon: '🚗', description: '凭此券可享受专车接送服务一次，风雨无阻，安全到家。', color: '沉稳蓝' },
                { category: '服务券', name: '“今天你洗碗”券', icon: '🧼', description: '不用争，不用抢，这张券决定了今晚洗碗的光荣任务属于我。', color: '洁净白' },
                { category: '服务券', name: '爱心夜宵外卖券', icon: '🍜', description: '深夜饥饿时，为你点一份热气腾腾的夜宵，温暖你的胃和心。', color: '暖心黄' },
                { category: '服务券', name: '“万能修理工”券', icon: '🛠️', description: '家里任何需要修理的小东西，交给我来搞定，保证修好！', color: '专业灰' },
                { category: '服务券', name: '“帮你选”决策券', icon: '🤔', description: '选择困难症发作时，帮你做决定，从穿搭到晚餐，我来选！', color: '理性蓝' },
                { category: '服务券', name: '“听我吐槽”陪伴券', icon: '💬', description: '当你需要倾诉时，我将放下一切，做你最耐心的听众。', color: '温柔米' },
                { category: '体验券', name: '浪漫约会策划券', icon: '💌', description: '你只需点头，剩下的交给我。兑换一次由我精心策划的神秘约会。', color: '浪漫紫' },
                { category: '体验券', name: '私人影院包场券', icon: '🎬', description: '我的客厅为你包场，你想看的电影，就是唯一上映的电影。', color: '经典黑金' },
                { category: '体验券', name: '“世界那么大”旅行券', icon: '✈️', description: '抛开所有烦恼，我们即刻出发！兑换一次说走就走的短途旅行。', color: '天空蓝' },
                { category: '体验券', name: '城市漫步探索券', icon: '🗺️', description: '选一个没去过的街区，手牵手压马路，发现城市里隐藏的惊喜。', color: '复古棕' },
                { category: '体验券', name: '密室逃脱挑战券', icon: '🔑', description: '一起开动脑筋，挑战一次惊险刺激的密室逃脱，见证我们的默契。', color: '神秘黑' },
                { category: '体验券', name: 'DIY手作体验券', icon: '🎨', description: '无论是陶艺、烘焙还是油画，一起创造一件属于我们的作品。', color: '创意彩虹' },
                { category: '体验券', name: 'Livehouse狂欢券', icon: '🎤', description: '沉浸在音乐和人潮中，忘掉所有烦恼，一起尽情呐喊摇摆！', color: '摇滚红' },
                { category: '体验券', name: '星空露营体验券', icon: '⛺', description: '远离城市喧嚣，在星空下枕着草地入眠，分享彼此的秘密。', color: '深邃星空蓝' },
                { category: '体验券', name: '主题乐园畅玩券', icon: '🎢', description: '像孩子一样，在主题乐园里疯玩一整天，所有项目我陪你。', color: '梦幻粉蓝' },
                { category: '体验券', name: '“重返校园”一日券', icon: '🏫', description: '穿上校服，重游校园，在图书馆自习，在操场漫步，重温青涩时光。', color: '青春绿' },
                { category: '特权券', name: '“我说了算”霸气券', icon: '👑', description: '在任意一项非原则性问题上，你拥有绝对最终决定权。有效期1次。', color: '霸气金' },
                { category: '特权券', name: '“瞬间和好”抱抱券', icon: '🫂', description: '吵架时出示此券，我将立刻停止争论，给你一个超大的拥抱。', color: '和解粉' },
                { category: '特权券', name: '愿望实现卡', icon: '🎁', description: '说出你一个能力范围内的愿望，我都会尽全力为你实现。', color: '梦想蓝' },
                { category: '特权券', name: '“今天听你的”服从券', icon: '🙋‍♀️', description: '从早到晚，你的所有指令我都会无条件服从，当一天你的小跟班。', color: '乖巧白' },
                { category: '特权券', name: '免死金牌', icon: '🛡️', description: '在你犯了小错误惹我生气时使用，可免除一次“惩罚”。', color: '尊贵银' },
                { category: '特权券', name: '“游戏优先”特权券', icon: '🎮', description: '我在忙时，你可以使用此券，要求我暂停一切，陪你玩一局游戏。', color: '电竞绿' },
                { category: '特权券', name: '“最后一块”零食券', icon: '🍰', description: '凭此券可获得袋子里/盘子里最后一块零食的专享权。', color: '甜蜜橙' },
                { category: '特权券', name: '“遥控器归你”券', icon: '📺', description: '今晚看什么电视/比赛，由你全权决定，我绝无怨言。', color: '宝石蓝' },
                { category: '特权券', name: '“再睡五分钟”赖床券', icon: '😴', description: '早上被叫醒时使用，可以理直气壮地再多睡五分钟。', color: '慵懒黄' },
                { category: '特权券', name: '“无理由”原谅券', icon: '🙏', description: '无论发生什么，只要出示此券，我都会选择无条件原谅你一次。', color: '纯净白' },
                { category: '美食券', name: '“火锅之夜”召唤券', icon: '🍲', description: '天冷了，心动了。此券一出，必须安排一顿热气腾腾的火锅。', color: '火热红' },
                { category: '美食券', name: '“奶茶自由”畅饮券', icon: '🥤', description: '凭此券可兑换任意一杯你心仪的奶茶，全糖去冰，由我买单！', color: '奶茶棕' },
                { category: '美食券', name: '“深夜食堂”点餐券', icon: '🍳', description: '我化身食堂老板，为你烹饪一道你指定的爱心夜宵。', color: '深夜蓝' },
                { category: '美食券', name: '“一起吃遍”美食清单券', icon: '📝', description: '兑换一次机会，共同完成我们美食清单上的一个打卡目标。', color: '探索黄' },
                { category: '美食券', name: '“甜品放纵”品尝券', icon: '🍩', description: '忘记卡路里！带你去吃一顿能让心情变好的高热量甜品。', color: '甜蜜粉' },
                { category: '美食券', name: '“烧烤撸串”江湖券', icon: '🍢', description: '江湖路远，一起撸串。兑换一次充满烟火气的烧烤大餐。', color: '炭火橙' },
                { category: '美食券', name: '“为你下厨”指定菜券', icon: '👨‍🍳', description: '你点菜，我来做。无论多难，我都会为你挑战一次。', color: '温暖米' },
                { category: '美食券', name: '“冰淇淋”消暑券', icon: '🍦', description: '夏日炎炎，用一个冰淇淋为你带来一丝清凉和甜蜜。', color: '清凉蓝' },
                { category: '美食券', name: '“剥虾/蟹”服务券', icon: '🦐', description: '你只管吃，我负责剥。享受一次饭来张口的服务。', color: '尊享金' },
                { category: '美食券', name: '“新店尝鲜”探险券', icon: '✨', description: '发现一家新开的餐厅？我们一起去当第一批“小白鼠”！', color: '好奇绿' },
                { category: '娱乐券', name: '“KTV麦霸”欢唱券', icon: '🎤', description: '包间已定，就等你开嗓。今晚你就是全场唯一的麦霸！', color: '炫彩紫' },
                { category: '娱乐券', name: '“桌游世界”畅玩券', icon: '🎲', description: '选一款你最爱的桌游，我们大战三百回合，输的人接受惩罚。', color: '策略蓝' },
                { category: '娱乐券', name: '“抓娃娃大神”附体券', icon: '🧸', description: '我将化身抓娃娃大神，为你抓一个你最想要的娃娃为止。', color: '可爱粉' },
                { category: '娱乐券', name: '“电玩城”金币兑换券', icon: '🪙', description: '兑换50枚游戏币，在电玩城里尽情挥洒汗水和青春。', color: '像素风' },
                { category: '娱乐券', name: '“陪你追剧”马拉松券', icon: '🍿', description: '你选剧，我陪伴。陪你刷完一整季你最爱的剧集。', color: '舒适橙' },
                { category: '娱乐券', name: '“双人成行”游戏券', icon: '🎮', description: '无论什么游戏，只要你想玩，我一定奉陪到底，做你的最佳队友。', color: '合作绿' },
                { category: '娱乐券', name: '“剧本杀”沉浸券', icon: '🎭', description: '换上戏服，一起进入另一个世界，体验一段不同的人生。', color: '悬疑黑' },
                { category: '娱乐券', name: '“运动搭子”陪伴券', icon: '🏸', description: '羽毛球、跑步、游泳…你想做的运动，我都是你的最佳搭档。', color: '活力黄' },
                { category: '娱乐券', name: '“盲盒”开启惊喜券', icon: '❓', description: '我为你准备一个神秘盲盒，开启属于你的小惊喜。', color: '神秘紫' },
                { category: '娱乐券', name: '“棋逢对手”对弈券', icon: '♟️', description: '象棋、围棋、五子棋…选一个，我们来一场智力的较量。', color: '黑白经典' },
                { category: '亲密券', name: '“晚安之吻”兑换券', icon: '🌙', description: '无论多晚，凭此券可兑换一个温柔的晚安吻。', color: '静谧蓝' },
                { category: '亲密券', name: '“深情拥抱”续时券', icon: '🤗', description: '兑换一次超过30秒的深情拥抱，感受彼此的心跳。', color: '温暖红' },
                { category: '亲密券', name: '“十指紧扣”牵手券', icon: '🤝', description: '散步时使用，要求对方全程十指紧扣，不许松开。', color: '亲密粉' },
                { category: '亲密券', name: '“情话”专属定制券', icon: '💬', description: '我将为你写下/说出一段专属情话，只给你一个人听。', color: '浪漫紫' },
                { category: '亲密券', name: '“烛光晚餐”预约券', icon: '🕯️', description: '在家或餐厅，享受一次只有我们两个人的浪漫烛光晚餐。', color: '酒红色' },
                { category: '亲密券', name: '“泡泡浴”共享券', icon: '🛀', description: '一起享受一个充满泡泡和香气的放松时刻。', color: '梦幻白' },
                { category: '亲密券', name: '“背后拥抱”突袭券', icon: '🥰', description: '在任何我想你的时候，从背后给你一个突然的温暖拥抱。', color: '惊喜黄' },
                { category: '亲密券', name: '“专属昵称”命名券', icon: '💕', description: '你可以为我起一个只有你能叫的专属亲密昵称，有效期一天。', color: '甜蜜橙' },
                { category: '亲密券', name: '“心跳挑战”对视券', icon: '👀', description: '对视一分钟，不许笑，不许躲闪，看谁先心跳加速。', color: '激情红' },
                { category: '亲密券', name: '“一日情侣”体验券', icon: '💖', description: '像第一天认识那样，重新体验一次心动的感觉。', color: '初恋粉' },
                { category: '成长券', name: '“专注时刻”守护券', icon: '🤫', description: '当你需要专注学习或工作时，我保证不打扰，并为你做好后勤。', color: '专注蓝' },
                { category: '成长券', name: '“新技能”学习陪伴券', icon: '📚', description: '陪你一起学习一项新技能，无论是乐器、语言还是编程。', color: '成长绿' },
                { category: '成长券', name: '“深度交流”畅谈券', icon: '🗣️', description: '放下手机，进行一次至少1小时的深度交流，分享最近的思考和感悟。', color: '智慧紫' },
                { category: '成长券', name: '“鼓励师”变身券', icon: '💪', description: '在你感到气馁时，我将化身头号粉丝，给你最真诚的鼓励和赞美。', color: '力量黄' },
                { category: '成长券', name: '“一起读书”共享券', icon: '📖', description: '挑选一本书，我们一起阅读，然后分享各自的读后感。', color: '书香棕' },
                { category: '成长券', name: '“目标制定”规划券', icon: '🎯', description: '坐下来，一起规划我们下个阶段的共同目标和个人目标。', color: '未来蓝' },
                { category: '成长券', name: '“坏习惯”监督改正券', icon: '✅', description: '你可以指定我的一个坏习惯，我承诺努力改正，并接受你的监督。', color: '决心白' },
                { category: '成长券', name: '“兴趣培养”支持券', icon: '🎨', description: '我会全力支持你去尝试一个新的兴趣爱好，并成为你的第一个观众。', color: '探索绿' },
                { category: '成长券', name: '“复盘会议”召开券', icon: '📈', description: '针对某件事，召开一次“复盘会议”，总结经验，共同进步。', color: '理性灰' },
                { category: '成长券', name: '“为你骄傲”庆祝券', icon: '🎉', description: '当你取得任何小成就时，为你举办一个迷你的庆祝仪式。', color: '荣耀金' },
                { category: '解压券', name: '“情绪垃圾桶”使用券', icon: '🗑️', description: '我是你的专属情绪垃圾桶，所有负能量和烦恼都可以倒给我。', color: '释放蓝' },
                { category: '解压券', name: '“发呆放空”陪伴券', icon: '☁️', description: '什么都不用想，什么都不用做，我陪你一起安静地发呆。', color: '天空白' },
                { category: '解压券', name: '“大哭一场”许可券', icon: '😭', description: '在我怀里，你可以卸下所有坚强，痛痛快快地大哭一场。', color: '治愈绿' },
                { category: '解压券', name: '“K歌解压”嘶吼券', icon: '🎶', description: '去KTV，不用在乎跑调，想唱什么就唱什么，尽情嘶吼释放。', color: '摇滚黑' },
                { category: '解压券', name: '“疯狂购物”买单券', icon: '🛍️', description: '在预算内，陪你进行一次解压式购物，我来买单。', color: '愉悦黄' },
                { category: '解压券', name: '“捏脸”解压券', icon: '😊', description: '心情不好的时候，可以捏我的脸，直到你笑出来为止。', color: '可爱粉' },
                { category: '解压券', name: '“沙包”陪练券', icon: '🥊', description: '我会戴好护具，当你的人肉沙包，让你打几拳出出气。', color: '力量红' },
                { category: '解压券', name: '“看喜剧”大笑券', icon: '😂', description: '陪你看一场能让你从头笑到尾的喜剧电影或脱口秀。', color: '开心橙' },
                { category: '解压券', name: '“安静”勿扰券', icon: '🤫', description: '当你需要独处时，我会安静地离开，给你足够的个人空间。', color: '平和灰' },
                { category: '解压券', name: '“撸猫/狗”治愈券', icon: '🐾', description: '带你去宠物店，或者借朋友的宠物，享受被毛茸茸治愈的时光。', color: '温暖棕' },
                { category: '道歉券', name: '“我错了”真诚道歉券', icon: '🙏', description: '我郑重地为我的错误道歉，并承诺会认真反思。', color: '真诚白' },
                { category: '道歉券', name: '“任你处罚”认错券', icon: '🥺', description: '我接受你提出的任何一项合理惩罚，绝无怨言。', color: '愧疚灰' },
                { category: '道歉券', name: '“逗你笑”开心券', icon: '😄', description: '我会用尽浑身解数，讲笑话、做鬼脸，直到把你逗笑为止。', color: '阳光黄' },
                { category: '道歉券', name: '“写检讨”保证券', icon: '✍️', description: '我会手写一份不低于300字的深刻检讨，并向你保证不再犯。', color: '严肃黑' },
                { category: '道歉券', name: '“买礼物”补偿券', icon: '🎁', description: '为你挑选一份礼物作为补偿，希望你能感受到我的歉意。', color: '补偿蓝' },
                { category: '道歉券', name: '“主动和好”台阶券', icon: '🪜', description: '吵架冷战时，我必须在1小时内主动给你台阶下。', color: '温暖橙' },
                { category: '道歉券', name: '“翻旧账”豁免券', icon: '📜', description: '你可以使用此券，对我进行一次“翻旧账”批判大会，我保证洗耳恭听。', color: '坦白米' },
                { category: '道歉券', name: '“学唱情歌”道歉券', icon: '🎵', description: '我会为你学唱一首情歌，用歌声表达我的歉意。', color: '音符黑白' },
                { category: '道歉券', name: '“给你一个拥抱”和解券', icon: '🤗', description: '不说话，先给一个紧紧的拥抱，让拥抱替我道歉。', color: '和解粉' },
                { category: '道歉券', name: '“听候发落”乖巧券', icon: '🙇', description: '在你消气之前，我保证乖乖听话，听候你的发落。', color: '柔和蓝' },
                { category: '角色扮演券', name: '“霸道总裁”体验券', icon: '💼', description: '我将扮演霸道总裁，而你是我独宠的小娇妻/夫。', color: '总裁黑金' },
                { category: '角色扮演券', name: '“一日学长/学妹”券', icon: '🎒', description: '我扮演温柔学长/可爱学妹，与你重温校园的青涩爱恋。', color: '青春蓝白' },
                { category: '角色扮演券', name: '“古代帝王/爱妃”券', icon: '👑', description: '朕/本宫允许你，与我共度一晚，体验宫廷里的爱恨情仇。', color: '皇家紫金' },
                { category: '角色扮演券', name: '“超级英雄/被拯救者”券', icon: '🦸', description: '我是你的专属超级英雄，今天你的所有“危难”都由我来拯救。', color: '英雄红蓝' },
                { category: '角色扮演券', name: '“侦探/助手”探案券', icon: '🕵️', description: '我们将化身侦探与助手，围绕一个谜题，展开一场家庭探案。', color: '侦探棕' },
                { category: '角色扮演券', name: '“服务员/顾客”点餐券', icon: '🍽️', description: '在家吃饭时，我扮演餐厅服务员，为你提供五星级的点餐和上菜服务。', color: '服务白' },
                { category: '角色扮演券', name: '“老师/学生”辅导券', icon: '🧑‍🏫', description: '我扮演严格的老师，为你“辅导”一项你不擅长的技能。', color: '学院绿' },
                { category: '角色扮演券', name: '“王子/公主”童话券', icon: '🏰', description: '今晚，我们是童话里的王子和公主，享受一个梦幻般的夜晚。', color: '童话粉蓝' },
                { category: '角色扮演券', name: '“医生/病人”看诊券', icon: '🩺', description: '我扮演医生，为你进行一次全面的“身体和心理”检查。', color: '医疗白' },
                { category: '角色扮演券', name: '“明星/粉丝”见面券', icon: '✨', description: '我是你的头号粉丝，为你举办一场迷你的粉丝见面会，对你进行彩虹屁攻击。', color: '闪耀银' },
            ];

            const CATEGORIES = ['全部', ...new Set(VOUCHERS.map(v => v.category))];
            
            const COLOR_MAP = {
                '温馨粉': 'border-pink-400 bg-pink-50', '清新绿': 'border-green-400 bg-green-50', '疗愈紫': 'border-purple-400 bg-purple-50',
                '活力橙': 'border-orange-400 bg-orange-50', '沉稳蓝': 'border-blue-400 bg-blue-50', '洁净白': 'border-gray-300 bg-gray-50',
                '暖心黄': 'border-yellow-400 bg-yellow-50', '专业灰': 'border-gray-400 bg-gray-100', '理性蓝': 'border-indigo-400 bg-indigo-50',
                '温柔米': 'border-amber-300 bg-amber-50', '浪漫紫': 'border-fuchsia-500 bg-fuchsia-50', '经典黑金': 'border-yellow-500 bg-gray-800 text-white',
                '天空蓝': 'border-sky-400 bg-sky-50', '复古棕': 'border-amber-700 bg-amber-100', '神秘黑': 'border-gray-600 bg-gray-900 text-white',
                '创意彩虹': 'border-violet-500 bg-violet-50', '摇滚红': 'border-red-600 bg-red-100', '深邃星空蓝': 'border-indigo-700 bg-indigo-900 text-white',
                '梦幻粉蓝': 'border-cyan-300 bg-rose-50', '青春绿': 'border-emerald-400 bg-emerald-50', '霸气金': 'border-yellow-400 bg-yellow-100',
                '和解粉': 'border-rose-300 bg-rose-50', '梦想蓝': 'border-blue-500 bg-blue-100', '乖巧白': 'border-stone-300 bg-stone-50',
                '尊贵银': 'border-slate-400 bg-slate-100', '电竞绿': 'border-lime-500 bg-lime-50', '甜蜜橙': 'border-orange-400 bg-orange-100',
                '宝石蓝': 'border-cyan-500 bg-cyan-50', '慵懒黄': 'border-amber-400 bg-amber-50', '纯净白': 'border-gray-200 bg-white',
                '火热红': 'border-red-500 bg-red-50', '奶茶棕': 'border-orange-800 bg-orange-100', '深夜蓝': 'border-blue-800 bg-blue-900 text-white',
                '探索黄': 'border-yellow-500 bg-yellow-50', '甜蜜粉': 'border-pink-300 bg-pink-50', '炭火橙': 'border-orange-600 bg-orange-100',
                '温暖米': 'border-orange-200 bg-orange-50', '清凉蓝': 'border-cyan-200 bg-cyan-50', '尊享金': 'border-amber-500 bg-amber-100',
                '好奇绿': 'border-teal-400 bg-teal-50', '炫彩紫': 'border-purple-600 bg-purple-100', '策略蓝': 'border-blue-600 bg-blue-100',
                '可爱粉': 'border-fuchsia-300 bg-fuchsia-50', '像素风': 'border-indigo-500 bg-gray-700 text-white', '舒适橙': 'border-red-300 bg-red-50',
                '合作绿': 'border-green-500 bg-green-100', '悬疑黑': 'border-black bg-neutral-800 text-white', '活力黄': 'border-yellow-400 bg-yellow-100',
                '神秘紫': 'border-violet-600 bg-violet-100', '黑白经典': 'border-gray-500 bg-white', '静谧蓝': 'border-blue-300 bg-blue-50',
                '温暖红': 'border-red-400 bg-red-100', '亲密粉': 'border-rose-400 bg-rose-50', '酒红色': 'border-red-800 bg-red-900 text-white',
                '梦幻白': 'border-pink-200 bg-white', '惊喜黄': 'border-amber-300 bg-amber-50', '激情红': 'border-rose-600 bg-rose-100',
                '初恋粉': 'border-pink-300 bg-pink-100', '专注蓝': 'border-sky-600 bg-sky-100', '成长绿': 'border-emerald-500 bg-emerald-100',
                '智慧紫': 'border-indigo-500 bg-indigo-100', '力量黄': 'border-yellow-500 bg-yellow-100', '书香棕': 'border-stone-500 bg-stone-100',
                '未来蓝': 'border-cyan-600 bg-cyan-100', '决心白': 'border-gray-400 bg-white', '探索绿': 'border-lime-600 bg-lime-100',
                '理性灰': 'border-slate-500 bg-slate-100', '荣耀金': 'border-amber-500 bg-amber-200', '释放蓝': 'border-blue-400 bg-blue-50',
                '天空白': 'border-sky-200 bg-sky-50', '治愈绿': 'border-green-300 bg-green-50', '摇滚黑': 'border-neutral-700 bg-neutral-900 text-white',
                '愉悦黄': 'border-yellow-300 bg-yellow-50', '力量红': 'border-rose-500 bg-rose-100', '开心橙': 'border-orange-300 bg-orange-50',
                '平和灰': 'border-gray-300 bg-gray-100', '温暖棕': 'border-amber-600 bg-amber-100', '真诚白': 'border-stone-200 bg-white',
                '愧疚灰': 'border-slate-300 bg-slate-50', '阳光黄': 'border-yellow-200 bg-yellow-50', '严肃黑': 'border-black bg-gray-200',
                '补偿蓝': 'border-indigo-300 bg-indigo-50', '温暖橙': 'border-orange-200 bg-orange-50', '坦白米': 'border-orange-100 bg-white',
                '音符黑白': 'border-gray-800 bg-white', '和解粉': 'border-pink-200 bg-pink-50', '柔和蓝': 'border-sky-300 bg-sky-50',
                '总裁黑金': 'border-yellow-600 bg-black text-white', '青春蓝白': 'border-blue-200 bg-white', '皇家紫金': 'border-amber-500 bg-purple-900 text-white',
                '英雄红蓝': 'border-red-500 bg-blue-600 text-white', '侦探棕': 'border-amber-800 bg-amber-200', '服务白': 'border-gray-200 bg-white',
                '学院绿': 'border-green-700 bg-green-100', '童话粉蓝': 'border-pink-300 bg-sky-100', '医疗白': 'border-blue-300 bg-white',
                '闪耀银': 'border-slate-300 bg-slate-200'
            };

            const filtersContainer = document.getElementById('filters');
            const voucherGrid = document.getElementById('voucher-grid');

            function renderVouchers(filter = '全部') {
                voucherGrid.innerHTML = '';
                const filteredVouchers = filter === '全部' ? VOUCHERS : VOUCHERS.filter(v => v.category === filter);
                
                filteredVouchers.forEach(v => {
                    const colorClass = COLOR_MAP[v.color] || 'border-gray-300 bg-white';
                    const card = `
                        <div class="card bg-white rounded-lg shadow-md p-5 flex flex-col ${colorClass}">
                            <div class="flex items-start justify-between mb-3">
                                <span class="text-4xl">${v.icon}</span>
                                <span class="text-xs font-semibold text-gray-500 bg-gray-200 px-2 py-1 rounded-full">${v.category}</span>
                            </div>
                            <h3 class="text-lg font-bold text-gray-800 mb-2 flex-grow">${v.name}</h3>
                            <p class="text-sm text-gray-600">${v.description}</p>
                        </div>
                    `;
                    voucherGrid.innerHTML += card;
                });
            }

            function setupFilters() {
                CATEGORIES.forEach(cat => {
                    const btn = document.createElement('button');
                    btn.className = 'filter-btn px-4 py-2 text-sm font-medium text-gray-700 bg-white rounded-full shadow-sm hover:bg-gray-100 focus:outline-none';
                    btn.textContent = cat;
                    btn.dataset.category = cat;
                    if (cat === '全部') {
                        btn.classList.add('active-filter');
                    }
                    btn.addEventListener('click', () => {
                        document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active-filter'));
                        btn.classList.add('active-filter');
                        renderVouchers(cat);
                    });
                    filtersContainer.appendChild(btn);
                });
            }

            function createChart() {
                const ctx = document.getElementById('categoryChart').getContext('2d');
                const categoryCounts = CATEGORIES.slice(1).map(cat => VOUCHERS.filter(v => v.category === cat).length);
                
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: CATEGORIES.slice(1),
                        datasets: [{
                            label: '券卡数量',
                            data: categoryCounts,
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)', 'rgba(255, 206, 86, 0.7)',
                                'rgba(75, 192, 192, 0.7)', 'rgba(153, 102, 255, 0.7)', 'rgba(255, 159, 64, 0.7)',
                                'rgba(255, 99, 255, 0.7)', 'rgba(100, 255, 100, 0.7)', 'rgba(201, 203, 207, 0.7)',
                                'rgba(50, 50, 50, 0.7)'
                            ],
                            borderColor: [
                                'rgba(255, 99, 132, 1)', 'rgba(54, 162, 235, 1)', 'rgba(255, 206, 86, 1)',
                                'rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)', 'rgba(255, 159, 64, 1)',
                                'rgba(255, 99, 255, 1)', 'rgba(100, 255, 100, 1)', 'rgba(201, 203, 207, 1)',
                                'rgba(50, 50, 50, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    font: {
                                        family: "'Noto Sans SC', sans-serif"
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        let label = context.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        if (context.parsed !== null) {
                                            label += context.parsed + ' 张';
                                        }
                                        return label;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            setupFilters();
            renderVouchers();
            createChart();
        });
    </script>
</body>
</html>
