/* pages/binding/binding.wxss */
@import "../../app.wxss";

.container {
  padding-bottom: 50rpx;
}

.binding-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 40rpx 30rpx;
}
.header-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.8;
}
.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.header-subtitle {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.tab-navigation {
  display: flex;
  margin-bottom: 30rpx;
  background-color: rgba(255,255,255,0.7);
  border-radius: 35rpx;
  padding: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}
.tab-item {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  font-size: 28rpx;
  color: #888;
  border-radius: 30rpx;
  transition: all 0.3s ease;
  font-weight: 500;
}
.tab-item.active {
  background-color: #FF69B4;
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(255,105,180,0.3);
}

.binding-section {
  padding: 40rpx 30rpx;
  text-align: center;
}
.section-instruction {
  display: block;
  font-size: 28rpx;
  color: #555;
  margin-bottom: 30rpx;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
  align-items: center;
  margin-bottom: 20rpx;
}
.invite-code-input {
  width: 100%;
  max-width: 400rpx;
  padding: 25rpx;
  font-size: 32rpx;
  border: 1rpx solid #ddd;
  border-radius: 16rpx;
  text-align: center;
  letter-spacing: 5rpx; /* For better code readability */
  background-color: #f9f9f9;
}
.invite-code-input:focus {
  border-color: #FFB6C1;
  background-color: #fff;
}

/* 绑定按钮样式已移至全局 primary-button 类 */

.code-display-area {
  padding: 30rpx;
  background-color: rgba(255, 105, 180, 0.05);
  border: 1rpx dashed #FFB6C1;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}
.my-invite-code {
  font-size: 52rpx;
  font-weight: bold;
  color: #FF69B4;
  letter-spacing: 8rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}
.code-actions {
  display: flex;
  gap: 20rpx;
  width: 100%;
  max-width: 450rpx;
}
.code-actions button {
  flex: 1;
  margin: 0 10rpx;
}

/* 按钮样式已统一使用全局 primary-button 类 */

.loading-code {
    flex-direction: row;
    gap: 15rpx;
    color: #888;
    font-size: 28rpx;
}
.loading-spinner-global.small {
    width: 40rpx;
    height: 40rpx;
    border-width: 4rpx;
}


.help-text {
  font-size: 24rpx;
  color: #888;
  line-height: 1.5;
  margin-top: 10rpx;
}

.tips-section {
  padding: 30rpx;
}
.tips-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #FF69B4;
  margin-bottom: 20rpx;
  text-align: center;
}
.tip-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
  padding-left: 30rpx;
  position: relative;
}
.tip-item::before {
  content: '‧';
  position: absolute;
  left: 0;
  color: #FFB6C1;
  font-size: 30rpx;
  line-height: 1;
}
/* Ensure /images/couple-hearts.svg exists */
