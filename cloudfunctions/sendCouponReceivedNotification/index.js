/**
 * 发送券接收确认通知云函数
 * 当接收方确认接收券后，向发送方发送确认通知
 */

const cloud = require('wx-server-sdk');
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event - 云函数调用参数
 * @param {Object} context - 云函数运行上下文
 * @returns {Object} 执行结果
 */
exports.main = async (event, context) => {
  console.log('开始执行发送券接收确认通知云函数:', event);
  
  try {
    const { couponId } = event;
    
    if (!couponId) {
      throw new Error('缺少必要参数: couponId');
    }

    // 获取当前调用者的openid
    const { OPENID } = cloud.getWXContext();
    
    // 获取券详细信息
    const couponDoc = await db.collection('coupons').doc(couponId).get();
    if (!couponDoc.data) {
      throw new Error('券信息不存在');
    }
    
    const coupon = couponDoc.data;
    
    // 权限验证：只有券的接收方可以触发此通知
    if (coupon.recipientId !== OPENID) {
      throw new Error('无权限发送此券的接收确认通知');
    }

    // 验证券状态：应该是已接收状态
    if (coupon.status !== 'received') {
      throw new Error(`券状态不正确，当前状态: ${coupon.status}，应该为 received`);
    }

    // 获取情侣关系验证
    const coupleDoc = await db.collection('couples').doc(coupon.coupleId).get();
    if (!coupleDoc.data) {
      throw new Error('相关情侣关系不存在');
    }

    const couple = coupleDoc.data;
    
    // 验证情侣关系是否激活
    if (couple.status !== 'active') {
      throw new Error('情侣关系状态异常，无法发送通知');
    }

    // 验证用户是否在情侣关系中
    if (couple.user1Id !== OPENID && couple.user2Id !== OPENID) {
      throw new Error('用户不在相关的情侣关系中');
    }

    // 获取发送方用户信息
    const senderDoc = await db.collection('users').where({
      _openid: coupon.creatorId
    }).get();
    
    if (!senderDoc.data || senderDoc.data.length === 0) {
      throw new Error('找不到发送方用户信息');
    }
    
    const sender = senderDoc.data[0];

    // 获取接收方用户信息
    const receiverDoc = await db.collection('users').where({
      _openid: OPENID
    }).get();
    
    if (!receiverDoc.data || receiverDoc.data.length === 0) {
      throw new Error('找不到接收方用户信息');
    }
    
    const receiver = receiverDoc.data[0];

    console.log(`准备发送券接收确认通知: 券名=${coupon.name}, 发送方=${sender.nickname}, 接收方=${receiver.nickname}`);

    // 创建通知记录
    const notification = {
      title: '券接收确认',
      content: `${receiver.nickname} 已确认接收您发送的「${coupon.name}」`,
      type: 'coupon_received',
      recipientId: coupon.creatorId,
      senderId: OPENID,
      couponId: couponId,
      couponName: coupon.name,
      isRead: false,
      createTime: new Date()
    };

    // 插入通知记录
    const notificationResult = await db.collection('notifications').add({
      data: notification
    });

    console.log('券接收确认通知发送成功:', {
      couponId,
      couponName: coupon.name,
      senderId: coupon.creatorId,
      receiverId: OPENID,
      notificationId: notificationResult._id
    });

    // 返回成功结果
    return {
      success: true,
      message: '券接收确认通知发送成功',
      data: {
        couponId,
        couponName: coupon.name,
        senderId: coupon.creatorId,
        receiverId: OPENID,
        receiveTime: coupon.receiveTime,
        notificationId: notificationResult._id,
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('发送券接收确认通知云函数执行失败:', error);

    return {
      success: false,
      message: '云函数执行失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};
