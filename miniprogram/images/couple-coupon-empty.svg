<svg xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 120 120" fill="none">
  <!-- 背景圆形 -->
  <circle cx="60" cy="60" r="55" fill="rgba(255,182,193,0.1)" stroke="rgba(255,105,180,0.2)" stroke-width="2"/>
  
  <!-- 券的轮廓 -->
  <rect x="25" y="40" width="70" height="40" rx="8" fill="rgba(255,105,180,0.05)" stroke="rgba(255,105,180,0.3)" stroke-width="2" stroke-dasharray="4,2"/>
  
  <!-- 券的装饰孔 -->
  <circle cx="25" cy="60" r="4" fill="rgba(255,105,180,0.2)"/>
  <circle cx="95" cy="60" r="4" fill="rgba(255,105,180,0.2)"/>
  
  <!-- 券内的心形图案 -->
  <path d="M50 55C50 52.2386 52.2386 50 55 50C57.7614 50 60 52.2386 60 55C60 60 50 65 50 65S40 60 40 55C40 52.2386 42.2386 50 45 50C47.7614 50 50 52.2386 50 55Z" fill="rgba(255,105,180,0.4)"/>
  <path d="M80 55C80 52.2386 77.7614 50 75 50C72.2386 50 70 52.2386 70 55C70 60 80 65 80 65S90 60 90 55C90 52.2386 87.7614 50 85 50C82.2386 50 80 52.2386 80 55Z" fill="rgba(255,105,180,0.4)"/>
  
  <!-- 连接心形的装饰线 -->
  <path d="M60 60L70 60" stroke="rgba(255,105,180,0.5)" stroke-width="2" stroke-linecap="round"/>
  
  <!-- 券上的文字装饰 -->
  <rect x="35" y="45" width="20" height="2" rx="1" fill="rgba(255,105,180,0.3)"/>
  <rect x="65" y="45" width="20" height="2" rx="1" fill="rgba(255,105,180,0.3)"/>
  <rect x="35" y="73" width="15" height="2" rx="1" fill="rgba(255,105,180,0.3)"/>
  <rect x="70" y="73" width="15" height="2" rx="1" fill="rgba(255,105,180,0.3)"/>
  
  <!-- 飘散的小心形 -->
  <path d="M30 25C30 23.3431 31.3431 22 33 22C34.6569 22 36 23.3431 36 25C36 28 30 32 30 32S24 28 24 25C24 23.3431 25.3431 22 27 22C28.6569 22 30 23.3431 30 25Z" fill="rgba(255,105,180,0.3)"/>
  <path d="M90 30C90 28.3431 91.3431 27 93 27C94.6569 27 96 28.3431 96 30C96 33 90 37 90 37S84 33 84 30C84 28.3431 85.3431 27 87 27C88.6569 27 90 28.3431 90 30Z" fill="rgba(255,105,180,0.3)"/>
  <path d="M85 85C85 83.3431 86.3431 82 88 82C89.6569 82 91 83.3431 91 85C91 88 85 92 85 92S79 88 79 85C79 83.3431 80.3431 82 82 82C83.6569 82 85 83.3431 85 85Z" fill="rgba(255,105,180,0.3)"/>
  <path d="M25 90C25 88.3431 26.3431 87 28 87C29.6569 87 31 88.3431 31 90C31 93 25 97 25 97S19 93 19 90C19 88.3431 20.3431 87 22 87C23.6569 87 25 88.3431 25 90Z" fill="rgba(255,105,180,0.3)"/>
  
  <!-- 中央的等待图标 -->
  <circle cx="60" cy="95" r="8" fill="rgba(255,105,180,0.1)" stroke="rgba(255,105,180,0.4)" stroke-width="1.5"/>
  <path d="M57 95L60 98L63 95" stroke="rgba(255,105,180,0.6)" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M60 92V98" stroke="rgba(255,105,180,0.6)" stroke-width="1.5" stroke-linecap="round"/>
</svg>
