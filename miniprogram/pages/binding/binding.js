// pages/binding/binding.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    coupleInfo: null,
    inviteCode: '', // For entering partner's code
    myInviteCode: '', // Generated for sharing
    isGeneratingCode: false,
    isBinding: false,
    activeTab: 'enterCode', // 'enterCode' or 'myCode'
    themeGradient: app.globalData.gradientBackground,
  },

  onLoad: function (options) {
    console.log('绑定页面加载，传入参数:', options);
    console.log('全局用户信息:', app.globalData.userInfo);
    console.log('全局情侣信息:', app.globalData.coupleInfo);
    
    this.setData({ 
      userInfo: app.globalData.userInfo,
      coupleInfo: app.globalData.coupleInfo,
      themeGradient: app.globalData.gradientBackground,
    });

    if (!this.data.userInfo) {
      wx.showModal({
        title: '请先登录',
        content: '登录后才能进行情侣绑定操作。',
        confirmText: '去登录',
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({ url: '/pages/index/index' });
          }
        }
      });
      return;
    }

    // 检查是否已经绑定
    const isAlreadyBound = app.isCoupleBound();
    console.log('检查绑定状态结果:', isAlreadyBound);
    
    if (isAlreadyBound) {
      console.log('用户已绑定，显示提示并跳转');
      wx.showModal({
        title: '已绑定情侣',
        content: '您已经和伴侣绑定啦！可以前往个人中心查看或管理绑定关系。',
        confirmText: '前往个人中心',
        cancelText: '返回首页',
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({ url: '/pages/profile/profile' });
          } else {
            wx.switchTab({ url: '/pages/index/index' });
          }
        }
      });
    } else {
      console.log('用户未绑定，显示绑定界面');
      // 如果传入了邀请码参数，自动填入
      if (options.code) {
        this.setData({ 
          inviteCode: options.code.toUpperCase(),
          activeTab: 'enterCode'
        });
      }
      // 生成自己的邀请码
      this.generateMyInviteCode();
    }
  },

  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
    if (tab === 'myCode' && !this.data.myInviteCode) {
      this.generateMyInviteCode();
    }
  },

  onInviteCodeInput: function (e) {
    this.setData({ inviteCode: e.detail.value.trim().toUpperCase() });
  },

  generateMyInviteCode: function () {
    if (this.data.myInviteCode) return; // Don't regenerate if already exists and valid
    this.setData({ isGeneratingCode: true });
    wx.cloud.callFunction({
      name: 'generateInviteCode',
      data: {},
      success: res => {
        if (res.result && res.result.success) {
          this.setData({ myInviteCode: res.result.data.inviteCode });
        } else {
          wx.showToast({ title: '生成邀请码失败', icon: 'none' });
          console.error("Failed to generate invite code:", res.result.message);
        }
      },
      fail: err => {
        wx.showToast({ title: '网络错误', icon: 'none' });
        console.error("Error generating invite code:", err);
      },
      complete: () => this.setData({ isGeneratingCode: false })
    });
  },

  handleBindWithCode: function () {
    if (!this.data.inviteCode || this.data.inviteCode.length < 4) { // Basic validation
      wx.showToast({ title: '请输入有效的邀请码', icon: 'none' });
      return;
    }
    this.setData({ isBinding: true });
    wx.cloud.callFunction({
      name: 'bindWithInviteCode',
      data: { inviteCode: this.data.inviteCode },
      success: res => {
        if (res.result && res.result.success) {
          wx.showToast({ title: '绑定成功！', icon: 'success' });
          
          // 更新全局数据和本地存储
          const coupleInfo = res.result.data.coupleInfo;
          const app = getApp();
          app.globalData.coupleInfo = coupleInfo;
          wx.setStorageSync('coupleInfo', coupleInfo);
          this.setData({ coupleInfo: coupleInfo });
          
          console.log('绑定成功，更新全局数据:', coupleInfo);
          
          // 使用增强的页面刷新机制 - 修复问题4
          this.handleBindingSuccess();
          
        } else {
          wx.showToast({ title: res.result.message || '绑定失败', icon: 'none' });
        }
        this.setData({ isBinding: false });
      },
      fail: err => {
        console.error('绑定失败:', err);
        wx.showToast({ title: '绑定请求失败', icon: 'none' });
        this.setData({ isBinding: false });
      }
    });
  },

  /**
   * 绑定成功后的处理逻辑（修复页面刷新问题）
   */
  handleBindingSuccess: function () {
    const app = getApp();
    
    console.log('开始处理绑定成功后的页面刷新');
    
    // 立即通知所有页面刷新
    app.notifyAllPagesRefresh();
    
    // 延迟返回，确保数据更新和页面刷新完成
    setTimeout(() => {
      console.log('绑定成功处理完成，准备返回');
      
      // 返回上一页
      wx.navigateBack({
        success: () => {
          console.log('返回上一页成功');
          
          // 返回后再次确保页面刷新
          setTimeout(() => {
            app.notifyAllPagesRefresh();
          }, 200);
        },
        fail: (err) => {
          console.error('返回上一页失败:', err);
          // 如果返回失败，跳转到首页
          wx.switchTab({ 
            url: '/pages/index/index',
            success: () => {
              setTimeout(() => {
                app.notifyAllPagesRefresh();
              }, 200);
            }
          });
        }
      });
    }, 800); // 增加延迟时间确保数据同步
  },

  copyMyCode: function () {
    if (!this.data.myInviteCode) return;
    wx.setClipboardData({
      data: this.data.myInviteCode,
      success: () => wx.showToast({ title: '邀请码已复制' })
    });
  },

  onShareAppMessage: function() {
    // Sharing the invite code
    if (this.data.myInviteCode) {
      return {
        title: `Hi~ 这是我的情侣信任券邀请码: ${this.data.myInviteCode}，快来绑定我吧！`,
        path: `/pages/binding/binding?code=${this.data.myInviteCode}`, // Optional: prefill code if recipient opens via share
        imageUrl: '/images/share-invite.png' // Generic invite share image
      };
    }
    return {
      title: '情侣信任券 - 和你的另一半绑定吧！',
      path: '/pages/binding/binding',
      imageUrl: '/images/share-invite.png'
    };
  }
});
