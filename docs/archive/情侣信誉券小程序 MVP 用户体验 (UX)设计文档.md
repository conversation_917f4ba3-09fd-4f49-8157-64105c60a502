# 情侣信誉券小程序 MVP 用户体验 (UX) 设计文档

本文档基于已确认的“情侣信誉券”小程序 MVP UI 设计文档，为其 MVP 阶段规划和设计详细的用户体验（UX）流程，以便为后续的开发和测试工作提供明确指导。

## 1. 核心功能用户操作流程图

### 1.1 用户注册/登录流程

用户通过微信授权登录小程序。
```mermaid
graph TD
    A[用户访问小程序] --> B{是否已授权登录?};
    B -- 是 --> F[进入首页];
    B -- 否 --> C[展示微信授权登录页面];
    C -- 用户同意授权 --> D[获取用户信息并调用后台登录接口];
    D -- 登录/注册成功 --> E[后台返回用户ID];
    E --> F[进入首页];
    D -- 登录/注册失败 --> G[提示用户登录失败, 可重试或联系客服];
    C -- 用户拒绝授权 --> H[展示授权失败提示并引导用户再次授权或退出];
```
*   **描述:** 用户首次访问或未登录时，小程序会引导用户进行微信授权登录。授权成功后，获取用户信息并调用后端接口完成注册或登录流程，然后进入首页。若授权失败，提供提示和引导。

### 1.2 情侣绑定流程

用户和其伴侣通过邀请码进行绑定。
```mermaid
graph TD
    A[用户进入伴侣管理页] --> B{是否已绑定伴侣?};
    B -- 是 --> C[展示当前伴侣信息];
    C --> D{用户选择?};
    D -- 解绑 --> E[弹出解绑确认弹窗];
    E -- 用户确认解绑 --> F[调用解绑接口];
    F -- 解绑成功 --> G[提示解绑成功并刷新页面至未绑定状态];
    F -- 解绑失败 --> H[提示解绑失败];
    B -- 否 --> I[展示邀请/绑定区域];
    I --> J{用户选择?};
    J -- 生成我的邀请码 ]--> K[展示生成的邀请码];
    J -- 输入伴侣邀请码 ]--> L[输入框输入邀请码];
    L --> M[点击绑定按钮];
    M --> N[调用绑定接口, 提交伴侣邀请码];
    N -- 绑定成功 --> O[提示绑定成功并刷新页面至已绑定状态];
    N -- 绑定失败 --> P[提示绑定失败, 原因可能是邀请码错误或伴侣已绑定];
```
*   **描述:** 未绑定伴侣的用户可以在伴侣管理页生成自己的邀请码供伴侣使用，或输入伴侣提供的邀请码进行绑定。绑定成功后，双方即可在小程序内互动。已绑定的用户可在该页面管理伴侣信息并解除绑定。

### 1.3 创建信誉券流程

用户为伴侣创建一张信誉券。
```mermaid
graph TD
    A[用户点击底部导航栏“创建”] --> B[进入创建券页面];
    B --> C[填写券名称];
    B --> D[填写券描述 (可选)];
    B --> E[选择所属伴侣];
    B --> F[选择有效期];
    C & D & E & F --> G[点击“创建”按钮];
    G --> H[前端表单校验];
    H -- 校验通过 --> I[调用创建券接口];
    H -- 校验失败 --> J[前端提示校验错误];
    I -- 创建成功 --> K[提示创建成功并跳转至首页/券列表];
    I -- 创建失败 --> L[提示创建失败];
```
*   **描述:** 用户在创建券页面填写券的名称、描述、指定接收伴侣和设定有效期后，点击创建按钮。前端进行初步校验，校验通过后调用后端接口创建信誉券。创建成功会给予提示并跳转。

### 1.4 查看信誉券列表流程

用户在首页查看自己创建或接收的信誉券。
```mermaid
graph TD
    A[用户进入首页/券列表页] --> B[页面加载中];
    B --> C[调用获取信誉券列表接口];
    C -- 接口返回成功 --> D[根据券状态组织数据并渲染列表];
    C -- 接口返回失败 --> E[展示加载失败提示];
    D --> F{用户操作列表项?};
    F -- 下拉刷新 --> G[重新调用获取列表接口];
    F -- 列表项点击 --> H[跳转至券详情页传递券ID];
    D -- 列表为空 --> I[展示空状态设计];
    D -- 列表加载更多 --> J[滚动到底部触发加载下一页数据];
```
*   **描述:** 用户进入首页后，页面加载并调用接口获取信誉券列表数据。列表根据券的状态进行展示。用户可以下拉刷新列表，或点击列表项进入详情页。列表为空时展示空状态。

### 1.5 查看信誉券详情流程

用户查看特定信誉券的详细信息。
```mermaid
graph TD
    A[用户在列表页点击信誉券] --> B[跳转至券详情页, 携带券ID];
    B --> C[页面加载中];
    C --> D[调用获取券详情接口, 传入券ID];
    D -- 接口返回成功 --> E[渲染券详情信息 (名称, 描述, 状态, 创建人, 有效期等)];
    D -- 接口返回失败 --> F[提示加载失败并可能提供返回按钮];
    E --> G{用户操作?};
    G -- 券状态为待核销 & 当前用户为接收人 --> H[展示“立即核销”按钮];
    H --> I[点击“立即核销”按钮];
    I --> J[弹出核销确认弹窗];
    J -- 用户确认核销 --> K[调用核销接口, 传入券ID];
    K -- 核销成功 --> L[提示核销成功并刷新详情页状态为已核销];
    K -- 核销失败 --> M[提示核销失败];
    G -- 其他情况 (已核销/已过期/非接收人) --> N[不展示核销按钮或按钮禁用];
```
*   **描述:** 用户点击列表中的信誉券后进入详情页，根据券ID加载详细信息。如果券状态为“待核销”且当前用户为接收者，会展示核销按钮。用户点击核销按钮需经过确认弹窗，确认后调用核销接口。

### 1.6 使用/核销信誉券流程

用户（接收方）核销一张“待使用”状态的信誉券。该流程嵌套在“查看信誉券详情”流程中。

*   **参见 1.5 查看信誉券详情流程，步骤 H 到 M**

### 1.7 管理个人资料流程 (MVP 简化)

MVP 阶段可能只包含昵称和头像展示，或与微信绑定相关的功能，简化流程。
```mermaid
graph TD
    A[用户进入“我的”页面] --> B[展示用户头像和昵称 (来源于微信)];
    B --> C{其他设置项?};
    C -- 进入伴侣管理 --> D[跳转至伴侣管理页];
    C -- 其他设置 (MVP可能无) --> E[预留功能入口];
```
*   **描述:** MVP阶段的个人资料管理主要展示微信授权获取的头像和昵称，并提供进入伴侣管理页的入口。其他高级设置可能在后续版本中实现。

## 2. 关键交互点的反馈机制

系统在用户进行关键操作时，应提供清晰、及时的视觉和文字反馈，增强用户体验的可控感和流畅性。

*   **成功提示:**
    *   **提示内容:** 简洁明了，如“创建成功”、“绑定成功”、“核销成功”、“已复制邀请码”。
    *   **样式:** ```svg
<svg width="150" height="50" xmlns="http://www.w3.org/2000/svg">
  <rect x="5" y="5" rx="5" ry="5" width="140" height="40" style="fill:#D4EDDA;stroke:#28A745;stroke-width:1;opacity:0.9;" />
  <text x="75" y="30" font-family="Arial" font-size="14" fill="#155724" text-anchor="middle">操作成功！</text>
</svg>
```
      采用非模态的顶部或中部提示条 (Toast)，背景色使用浅绿色 `#D4EDDA`，边框或文字使用绿色 `#28A745`。提示条自动消失（2-3秒）。

*   **错误校验与提示:**
    *   **提示内容:** 具体指出错误原因，如“券名称不能为空”、“有效期选择有误”、“请选择所属伴侣”、“邀请码格式不正确”、“该用户已绑定其他伴侣”。
    *   **样式:**
        *   表单输入错误：对应输入框边框变红 `#DC3545`，输入框下方显示红色错误提示文字 `#DC3545`，字号 12px。
        *   操作失败：```svg
<svg width="150" height="50" xmlns="http://www.w3.org/2000/svg">
  <rect x="5" y="5" rx="5" ry="5" width="140" height="40" style="fill:#F8D7DA;stroke:#DC3545;stroke-width:1;opacity:0.9;" />
  <text x="75" y="30" font-family="Arial" font-size="14" fill="#721C24" text-anchor="middle">操作失败！</text>
</svg>
```
          非模态提示条，背景色浅红色 `#F8D7DA`，边框或文字红色 `#DC3545` 或深红色 `#721C24`。提示条自动消失或可手动关闭。重要错误可能采用模态 Alert 弹窗。

*   **加载状态:**
    *   **页面加载:** 首次进入页面、下拉刷新或关键数据加载时，页面顶部出现线性或圆形加载指示器 (Spinner)，颜色使用主色 `#FF6D96`。列表页面可使用骨架屏 (Skeleton Screen) 提升感知速度。
    *   **数据提交:** 点击按钮触发数据提交（如创建、绑定、核销）时，按钮本身可以显示内置的加载指示器 (Spinner)，同时按钮变为禁用状态。
    *   **样式:** ```svg
<svg width="50" height="50" xmlns="http://www.w3.org/2000/svg">
  <circle cx="25" cy="25" r="20" fill="none" stroke="#FF6D96" stroke-width="4" stroke-dasharray="31.4, 31.4" stroke-linecap="round">
    <animateTransform attributeName="transform" type="rotate" from="0 25 25" to="360 25 25" dur="1s" repeatCount="indefinite"/>
  </circle>
</svg>
```
      圆形 Spinner 采用主色 `#FF6D96`。

*   **空状态设计:**
    *   **券列表为空:** 当用户没有创建或接收任何信誉券时，列表区域不显示空白，而是展示一张带有温馨插图或图标的引导图文。
    *   **样式:**
        *   居中放置图标（如一个大心形图标 `#FF6D96` 或一个空盒子线性图标 `#CCCCCC`）。
        *   下方显示引导文字，如“还没有信誉券哦，给ta创造一份惊喜吧！”字号 14px，中灰色 `#666666`。
        *   可能包含一个醒目的主色按钮，引导用户“立即创建第一张信誉券”。
        *   **示例:** ```svg
<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
  <rect width="200" height="150" fill="#FFFFFF" rx="10" ry="10"/>
  <circle cx="100" cy="50" r="30" fill="#FFE4E1"/>
  <path d="M85 50 Q100 35 115 50 Q100 65 85 50 Z" fill="#FF6D96"/>
  <text x="100" y="100" font-family="Arial" font-size="14" fill="#666666" text-anchor="middle">还没有信誉券哦</text>
  <text x="100" y="120" font-family="Arial" font-size="14" fill="#666666" text-anchor="middle">给ta创造一份惊喜吧！</text>
</svg>
```
    *   **搜索结果为空:** 如果 MVP 阶段包含搜索功能，搜索结果为空时类似列表为空的处理方式，但文案应更具体，如“没有找到相关的信誉券，换个关键词试试？”。

## 3. 异常流程处理

小程序应优雅地处理常见的异常情况，提供友好的用户提示和必要的引导。

*   **网络错误:**
    *   **场景:** 用户无网络连接、网络不稳定导致请求超时或中断。
    *   **处理:**
        *   页面加载时：若首次加载失败，全屏展示“网络错误”页面，带有图标和文字提示，并提供“重试”按钮。
        *   操作请求时：若接口请求失败，通过非模态提示条 (Toast) 提示“网络错误，请稍后重试”，对于关键操作失败，可能需要模态 Alert 提示。
    *   **提示:** ```svg
<svg width="150" height="50" xmlns="http://www.w3.org/2000/svg">
  <rect x="5" y="5" rx="5" ry="5" width="140" height="40" style="fill:#FFF3CD;stroke:#FFC107;stroke-width:1;opacity:0.9;" />
  <text x="75" y="30" font-family="Arial" font-size="14" fill="#856404" text-anchor="middle">网络错误！</text>
</svg>
```
      非模态提示条，背景浅黄色 `#FFF3CD`，边框或文字黄色 `#FFC107` 或深黄色 `#856404`。

*   **权限不足:**
    *   **场景:** 用户未授权微信登录，尝试访问需要用户信息的页面；用户尝试某些受限操作。
    *   **处理:**
        *   强制登录：若进入首页就需要登录，未登录用户会被引导至授权页面（参见 1.1 流程）。
        *   功能权限：若用户未绑定伴侣却尝试创建券（假定只有已绑定用户可以创建），提示“请先绑定伴侣”，并提供跳转到伴侣管理页的入口。
    *   **提示:** 模态 Alert 弹窗：“您需要先授权登录才能使用该功能。是否前往授权？”（对于登录）；非模态提示条或 Alert 弹窗：“您尚未绑定伴侣，请先绑定。”

*   **服务器错误:**
    *   **场景:** 后端接口返回服务器内部错误、数据库错误等非业务逻辑错误。
    *   **处理:**
        *   通过非模态提示条 (Toast) 提示“服务异常，请稍后重试” 或 “操作失败，请联系客服”。
        *   在开发和测试阶段，错误提示可更详细，但向用户展示时应尽量友好、简略。
    *   **提示:** ```svg
<svg width="150" height="50" xmlns="http://www.w3.org/2000/svg">
  <rect x="5" y="5" rx="5" ry="5" width="140" height="40" style="fill:#E2E3E5;stroke:#6C757D;stroke-width:1;opacity:0.9;" />
  <text x="75" y="30" font-family="Arial" font-size="14" fill="#343A40" text-anchor="middle">服务器异常！</text>
</svg>
```
      非模态提示条，颜色偏中性灰色。

*   **其他可预见的异常情况及其应对策略:**
    *   **邀请码失效/不存在:** 用户输入邀请码绑定时，若邀请码不正确、已过期或已被使用，提示“邀请码无效，请确认后重试”。
    *   **伴侣已绑定:** 用户尝试绑定已被其他用户绑定的伴侣时，提示“您尝试绑定的用户已绑定其他伴侣”。
    *   **重复创建:** 防止用户短时间内重复提交创建请求，前端按钮点击后进入加载状态并禁用防止重复提交。
    *   **数据不一致:** 极少数情况下，前后端数据展示不一致，可引导用户刷新页面，必要时提示“数据异常，请尝试刷新页面”。
    *   **未找到券 (例如尝试访问已被删除或不存在的券详情):** 提示“未找到该信誉券”，并引导用户返回列表页。

## 4. 可用性测试初步定义

可用性测试是评估小程序易用性的重要环节。MVP 阶段的测试将聚焦核心功能，以确保基础用户流程顺畅。

*   **测试范围:**
    *   **核心用户流程:**
        *   微信授权登录与首次进入小程序。
        *   未绑定伴侣状态下生成邀请码。
        *   未绑定伴侣状态下输入邀请码并绑定伴侣。
        *   已绑定伴侣状态下查看伴侣信息。
        *   （可选）已绑定伴侣状态下解绑伴侣。
        *   创建一张新的信誉券（包括填写信息、选择伴侣、选择有效期、提交）。
        *   在首页查看自己创建的待使用的信誉券。
        *   在首页查看伴侣创建的待使用的信誉券。
        *   点击列表项进入信誉券详情页。
        *   查看信誉券的详细信息。
        *   （作为接收方）在详情页核销信誉券（包括点击按钮、确认弹窗、核销成功）。
        *   在首页查看已核销或已过期的信誉券。
        *   进入历史记录页面查看历史信誉券。
        *   （若有）在个人中心查看基本信息并跳转至伴侣管理。
    *   **关键交互点:** 
        *   表单输入、选择器的使用体验。
        *   各类提示（成功、错误、加载）的及时性和清晰度。
        *   空状态页面的理解和引导作用。
        *   按钮、列表项等可点击元素的反馈。
    *   **异常处理:**
        *   弱网络环境下的使用体验。
        *   输入错误数据时的提示和修正。

*   **目标用户:**
    *   **年龄段:** MVP 阶段主要用户群体定为 20-35 岁。
    *   **使用习惯:** 熟悉微信及微信小程序的基本操作，习惯使用智能手机进行社交和日常操作。
    *   **与情侣的关系状态:** 处于恋爱关系中的情侣，具有一定的互动意愿和送/收小礼物/承诺的习惯或潜在需求。需要招募真实的情侣用户参与测试。
    *   **用户背景:** 涵盖不同职业、生活方式的情侣，以获取更全面的反馈。

*   **测试场景与任务:**
    *   **场景 1: “爱的初体验”**
        *   **任务:** *小明和小红是一对情侣，他们都首次使用[小程序名称]小程序。请你作为小明，完成以下任务：1. 打开小程序并完成登录。2. 进入伴侣管理页面，生成你的邀请码。3. 将你的邀请码通过微信发送给小红（模拟）。请你作为小红，完成以下任务：1. 打开小程序并完成登录。2. 进入伴侣管理页面，输入小明给你的邀请码，完成与小明的绑定。*
        *   **评估点:** 登录流程的顺畅性，邀请码生成及绑定的易用性，页面状态切换是否清晰。
    *   **场景 2: “制造小惊喜”**
        *   **任务:** *小明想给小红一个小承诺，创建一张“做一天家务”信誉券。请你作为小明，完成以下任务：1. 进入创建信誉券页面。2. 填写券名称“做一天家务”。3. 填写券描述（例如：周六日任选一天，承包所有家务）。4. 选择接收人是小红。5. 设置一个有效期（例如：未来一个月内）。6. 成功创建这张信誉券。7. 回到首页查看你创建的券是否出现在列表中。*
        *   **评估点:** 创建券表单的填写体验，字段的理解难度，选择器的易用性，创建成功的反馈和券列表的展示是否及时准确。
    *   **场景 3: “兑现爱的承诺”**
        *   **任务:** *小红收到了小明创建的“做一天家务”券，现在周六到了，小红想使用这张券。请你作为小红，完成以下任务：1. 打开小程序首页，找到小明给你的“做一天家务”券。2. 点击进入该券的详情页。3. 查看券的详细内容。4. 点击“立即核销”按钮。5. 在弹出的确认框中确认核销。6. 查看核销成功的提示，并确认券的状态在详情页和列表页都已更新为“已核销”。*
        *   **评估点:** 查看券详情的便捷性，核销按钮的易用性，核销二次确认的流程是否合理，核销成功后的状态更新是否及时可见。
    *   **场景 4: “回顾甜蜜足迹”**
        *   **任务:** *小明想看看他和小红过去互相发过哪些券。请你作为小明，完成以下任务：1. 进入历史记录页面。2. 查看历史列表中的信誉券信息（包括已核销和已过期的券）。*
        *   **评估点:** 历史记录列表的组织方式是否清晰，状态展示是否准确，信息是否全面。

通过上述的可用性测试，可以收集用户在实际操作中的反馈，发现流程中的卡点和设计缺陷，为后续的迭代优化提供数据支持。

---
本文档提供了情侣信誉券小程序 MVP 阶段的详细 UX 流程、交互反馈和异常处理设计，以及初步的可用性测试规划。这些内容将作为开发的基准，并在开发和测试过程中持续验证和优化。