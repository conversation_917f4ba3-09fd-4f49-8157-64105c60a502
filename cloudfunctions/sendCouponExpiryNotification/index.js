/**
 * 发送券过期提醒通知云函数
 * 定期检查即将过期或已过期的券，发送提醒通知
 */

const cloud = require('wx-server-sdk');
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 检查日期是否已过期
 * @param {Date|string} date - 要检查的日期
 * @returns {boolean} 是否已过期
 */
function isExpired(date) {
  return new Date(date) < new Date();
}

/**
 * 计算日期剩余天数
 * @param {Date|string} date - 目标日期
 * @returns {number} 剩余天数
 */
function remainingDays(date) {
  const targetDate = new Date(date);
  const now = new Date();
  const diffTime = targetDate.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * 云函数入口函数
 * @param {Object} event - 云函数调用参数 
 * @param {Object} context - 云函数运行上下文
 * @returns {Object} 执行结果
 */
exports.main = async (event, context) => {
  console.log('开始执行发送券过期提醒通知云函数:', event);
  
  try {
    const { 
      daysBefore = 1,        // 提前几天提醒，默认1天
      processExpired = true, // 是否处理已过期券
      couponId = null        // 指定券ID（手动触发时使用）
    } = event;

    const processResults = {
      expiringSoon: {
        total: 0,
        notificationsSent: 0,
        failed: 0,
        details: []
      },
      expired: {
        total: 0,
        statusUpdated: 0,
        notificationsSent: 0,
        failed: 0,
        details: []
      }
    };

    // 情况1: 处理指定的单张券
    if (couponId) {
      console.log(`处理指定券的过期通知: ${couponId}`);
      
      const couponDoc = await db.collection('coupons').doc(couponId).get();
      if (!couponDoc.data) {
        throw new Error('券信息不存在');
      }
      
      const coupon = couponDoc.data;
      const result = await processSingleCouponExpiry(coupon);
      processResults.manual = result;
      
      return {
        success: true,
        message: '指定券过期通知处理完成',
        data: processResults,
        timestamp: new Date().toISOString()
      };
    }

    // 情况2: 批量处理即将过期的券
    console.log(`开始批量检查即将过期的券 (${daysBefore}天内)`);
    
    const now = new Date();
    const targetDate = new Date(now.getTime() + daysBefore * 24 * 60 * 60 * 1000);
    
    const expiringSoonDocs = await db.collection('coupons')
      .where({
        status: 'received',
        expiryDate: db.command.lte(targetDate)
      })
      .get();

    if (expiringSoonDocs.data.length > 0) {
      processResults.expiringSoon.total = expiringSoonDocs.data.length;
      console.log(`找到 ${expiringSoonDocs.data.length} 张即将过期的券`);
      
      for (const coupon of expiringSoonDocs.data) {
        const result = await processExpiringSoonCoupon(coupon);
        processResults.expiringSoon.details.push(result);
        
        if (result.success) {
          processResults.expiringSoon.notificationsSent++;
        } else {
          processResults.expiringSoon.failed++;
        }
      }
    }

    // 情况3: 批量处理已过期的券
    if (processExpired) {
      console.log('开始批量检查已过期的券');
      
      const expiredDocs = await db.collection('coupons')
        .where({
          expiryDate: db.command.lt(now),
          status: db.command.neq('expired')
        })
        .get();

      if (expiredDocs.data.length > 0) {
        processResults.expired.total = expiredDocs.data.length;
        console.log(`找到 ${expiredDocs.data.length} 张已过期的券`);
        
        for (const coupon of expiredDocs.data) {
          const result = await processExpiredCoupon(coupon);
          processResults.expired.details.push(result);
          
          if (result.statusUpdated) {
            processResults.expired.statusUpdated++;
          }
          
          if (result.notificationSent) {
            processResults.expired.notificationsSent++;
          } else if (result.error) {
            processResults.expired.failed++;
          }
        }
      }
    }

    const summary = {
      expiringSoon: `${processResults.expiringSoon.notificationsSent}/${processResults.expiringSoon.total} 成功`,
      expired: `${processResults.expired.statusUpdated}/${processResults.expired.total} 状态更新, ${processResults.expired.notificationsSent} 通知发送`
    };

    console.log('券过期处理完成', summary);

    return {
      success: true,
      message: '券过期提醒处理完成',
      data: processResults,
      summary,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('发送券过期提醒通知云函数执行失败:', error);

    return {
      success: false,
      message: '云函数执行失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 处理指定券的过期通知
 * @param {Object} coupon - 券对象
 * @returns {Object} 处理结果
 */
async function processSingleCouponExpiry(coupon) {
  try {
    const isExpiredNow = isExpired(coupon.expiryDate);
    
    if (isExpiredNow && coupon.status !== 'expired') {
      // 更新已过期券状态
      await db.collection('coupons').doc(coupon._id).update({
        data: {
          status: 'expired',
          expiredTime: new Date()
        }
      });
    }
    
    // 发送过期通知给接收方
    const notification = {
      title: isExpiredNow ? '券已过期' : '券即将过期',
      content: `您的券「${coupon.name}」${isExpiredNow ? '已经过期' : `将在${remainingDays(coupon.expiryDate)}天后过期`}`,
      type: 'coupon_expiry',
      recipientId: coupon.recipientId,
      senderId: 'system',
      couponId: coupon._id,
      couponName: coupon.name,
      isRead: false,
      createTime: new Date()
    };

    await db.collection('notifications').add({
      data: notification
    });

    return {
      success: true,
      couponId: coupon._id,
      isExpired: isExpiredNow,
      notificationSent: true
    };
    
  } catch (error) {
    return {
      success: false,
      couponId: coupon._id,
      error: error.message
    };
  }
}

/**
 * 处理即将过期的券
 * @param {Object} coupon - 券对象
 * @returns {Object} 处理结果
 */
async function processExpiringSoonCoupon(coupon) {
  try {
    const days = remainingDays(coupon.expiryDate);
    
    console.log(`处理即将过期券: ${coupon.name}, 剩余 ${days} 天`, {
      couponId: coupon._id,
      expiryDate: coupon.expiryDate
    });

    const notification = {
      title: '券即将过期',
      content: `您的券「${coupon.name}」将在${days}天后过期，请尽快使用`,
      type: 'coupon_expiry_warning',
      recipientId: coupon.recipientId,
      senderId: 'system',
      couponId: coupon._id,
      couponName: coupon.name,
      isRead: false,
      createTime: new Date()
    };

    await db.collection('notifications').add({
      data: notification
    });

    return {
      success: true,
      couponId: coupon._id,
      couponName: coupon.name,
      remainingDays: days,
      notificationSent: true
    };
    
  } catch (error) {
    console.error(`处理即将过期券失败: ${coupon._id}`, error);
    return {
      success: false,
      couponId: coupon._id,
      couponName: coupon.name,
      error: error.message
    };
  }
}

/**
 * 处理已过期的券
 * @param {Object} coupon - 券对象
 * @returns {Object} 处理结果
 */
async function processExpiredCoupon(coupon) {
  try {
    console.log(`处理已过期券: ${coupon.name}`, {
      couponId: coupon._id,
      expiryDate: coupon.expiryDate,
      currentStatus: coupon.status
    });

    let statusUpdated = false;
    
    // 更新券状态为已过期
    if (coupon.status !== 'expired') {
      await db.collection('coupons').doc(coupon._id).update({
        data: {
          status: 'expired',
          expiredTime: new Date()
        }
      });
      statusUpdated = true;
    }

    // 调用通知状态管理系统处理过期
    try {
      await cloud.callFunction({
        name: 'updateNotificationStatus',
        data: {
          couponId: coupon._id,
          newCouponStatus: 'expired',
          action: 'expire_coupon'
        }
      });
    } catch (error) {
      console.warn('调用通知状态管理失败:', error);
    }

    // 发送过期通知给接收方
    const notification = {
      title: '券已过期',
      content: `您的券「${coupon.name}」已过期，无法再使用`,
      type: 'coupon_expiry',
      recipientId: coupon.recipientId,
      senderId: 'system',
      couponId: coupon._id,
      couponName: coupon.name,
      isRead: false,
      createTime: new Date(),
      notificationStatus: 'active',
      couponStatus: 'expired',
      userRole: 'recipient'
    };

    await db.collection('notifications').add({
      data: notification
    });

    return {
      success: true,
      couponId: coupon._id,
      couponName: coupon.name,
      statusUpdated,
      notificationSent: true
    };
    
  } catch (error) {
    console.error(`处理已过期券失败: ${coupon._id}`, error);
    return {
      success: false,
      couponId: coupon._id,
      couponName: coupon.name,
      error: error.message
    };
  }
}
