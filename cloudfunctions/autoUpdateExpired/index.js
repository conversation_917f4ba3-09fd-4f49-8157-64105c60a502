/**
 * 自动更新过期券状态云函数
 * 定期检查并更新已过期但状态未更新的券，通常由定时触发器调用
 */

const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

/**
 * 云函数入口函数
 * @param {Object} event 云函数调用参数
 * @param {Object} context 云函数运行上下文
 */
exports.main = async (event, context) => {
  console.log('开始执行自动更新过期券状态云函数:', event);
  
  try {
    const currentTime = new Date();
    
    // 查询所有需要更新为过期状态的券
    // 条件：有效期小于当前时间，且状态不是 'redeemed' 或 'expired'
    const expiredCouponsQuery = await db.collection('coupons')
      .where({
        expiresAt: _.lt(currentTime),
        status: _.nin(['redeemed', 'expired'])
      })
      .get();

    const expiredCoupons = expiredCouponsQuery.data || [];
    
    if (expiredCoupons.length === 0) {
      console.log('没有需要更新的过期券');
      return {
        success: true,
        data: {
          processedCount: 0,
          message: '没有需要更新的过期券',
          timestamp: currentTime.toISOString()
        }
      };
    }

    console.log(`发现 ${expiredCoupons.length} 张需要更新为过期状态的券`);

    // 批量更新券状态和创建通知
    const updatePromises = expiredCoupons.map(async (coupon) => {
      try {
        // 更新券状态为过期
        await db.collection('coupons').doc(coupon._id).update({
          data: {
            status: 'expired',
            expiredTime: currentTime,
            updateTime: currentTime
          }
        });

        console.log(`券 ${coupon._id} (${coupon.title}) 已更新为过期状态`);

        // 创建过期通知（给券的双方）
        const notifications = [
          {
            recipientId: coupon.creatorId,
            type: 'coupon_expired',
            couponId: coupon._id,
            title: '信誉券已过期',
            message: `您发送的信誉券"${coupon.title}"已过期`,
            isRead: false,
            createTime: currentTime
          },
          {
            recipientId: coupon.recipientId,
            type: 'coupon_expired',
            couponId: coupon._id,
            title: '信誉券已过期',
            message: `您收到的信誉券"${coupon.title}"已过期`,
            isRead: false,
            createTime: currentTime
          }
        ];

        // 批量创建过期通知
        for (const notification of notifications) {
          try {
            await db.collection('notifications').add({
              data: notification
            });
          } catch (notificationError) {
            console.error(`创建过期通知失败 (券 ${coupon._id}):`, notificationError);
          }
        }

        return {
          couponId: coupon._id,
          couponTitle: coupon.title,
          success: true
        };
        
      } catch (error) {
        console.error(`更新券 ${coupon._id} 状态失败:`, error);
        return {
          couponId: coupon._id,
          couponTitle: coupon.title,
          success: false,
          error: error.message
        };
      }
    });

    // 等待所有更新操作完成
    const updateResults = await Promise.all(updatePromises);
    
    // 统计成功和失败数量
    const successCount = updateResults.filter(result => result.success).length;
    const failureCount = updateResults.filter(result => !result.success).length;

    console.log(`过期券状态更新完成: 成功 ${successCount} 张, 失败 ${failureCount} 张`);

    // 清理过期的通知记录（可选，保留30天的通知记录）
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const cleanupQuery = await db.collection('notifications')
        .where({
          createTime: _.lt(thirtyDaysAgo),
          isRead: true
        })
        .remove();

      console.log(`清理了 ${cleanupQuery.stats.removed} 条过期通知记录`);
    } catch (cleanupError) {
      console.error('清理过期通知记录失败:', cleanupError);
    }

    return {
      success: true,
      data: {
        totalFound: expiredCoupons.length,
        processedCount: successCount,
        failedCount: failureCount,
        successfulUpdates: updateResults.filter(r => r.success).map(r => ({
          couponId: r.couponId,
          couponTitle: r.couponTitle
        })),
        failedUpdates: updateResults.filter(r => !r.success),
        timestamp: currentTime.toISOString(),
        message: `成功更新 ${successCount} 张券状态为过期`
      }
    };

  } catch (error) {
    console.error('自动更新券过期状态失败:', error);
    
    return {
      success: false,
      message: '自动更新券过期状态失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}; 