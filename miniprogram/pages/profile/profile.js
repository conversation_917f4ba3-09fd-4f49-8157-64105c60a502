// profile.js
const app = getApp();

// 默认个性签名列表
const DEFAULT_SIGNATURES = [
  "爱是最美的语言 💕",
  "有你的日子都是晴天 ☀️",
  "心有所属，爱有所归 💖",
  "陪你看遍世间风景 🌸",
  "余生请多指教 💝",
  "简单的爱，纯真的心 🌹",
  "一生一世一双人 💑",
  "愿得一人心，白首不分离 💍",
  "你是我的小确幸 🍀",
  "平凡日子里的不平凡 ✨"
];

Page({
  data: {
    userInfo: null,
    coupleInfo: null,
    stats: {
      sentCount: 0,
      receivedCount: 0,
      redeemedCount: 0,
      togetherDays: 0,
    },
    notifications: {
      unreadCount: 0,
    },
    isLoading: false,
    loadingText: "",
    showUnbindModal: false,
    confirmText: "",
  },

  onLoad: function () {
    console.log("个人中心页面加载");

    // 添加全局数据刷新监听器
    app.addEventListener('globalDataRefresh', (data) => {
      console.log('个人中心页面收到全局数据刷新事件:', data);
      if (data.userInfo) {
        this.setData({
          userInfo: data.userInfo
        });
      }
      if (data.coupleInfo) {
        this.setData({
          coupleInfo: data.coupleInfo
        });
      }
    });

    // 添加情侣信息刷新监听器
    app.addEventListener('refreshCoupleInfo', (data) => {
      console.log('个人中心页面收到情侣信息刷新事件:', data);
      // 重新获取情侣信息
      this.getCoupleInfo();
    });

    // 添加伴侣资料更新通知监听器
    this.partnerProfileUpdateHandler = (notificationData) => {
      console.log('个人中心页面收到伴侣资料更新通知:', notificationData);

      if (notificationData.type === 'profile_updated' && notificationData.data?.shouldRefreshCache) {
        console.log('伴侣更新了资料，强制刷新情侣信息');
        console.log('更新的字段:', notificationData.data.updatedFields);
        console.log('伴侣最新信息:', notificationData.data.userInfo);

        // 彻底清除所有相关缓存
        wx.removeStorageSync('coupleInfo');
        wx.removeStorageSync('userInfo');
        app.globalData.coupleInfo = null;

        // 强制重新获取情侣信息（不使用缓存）
        this.getCoupleInfoForced().then(() => {
          console.log('伴侣资料更新后，情侣信息已刷新');

          // 修复问题1：强制刷新页面显示，确保头像等信息更新
          const currentCoupleInfo = this.data.coupleInfo;
          if (currentCoupleInfo) {
            console.log('当前显示的伴侣头像:', currentCoupleInfo.partnerAvatar);
            console.log('当前显示的伴侣昵称:', currentCoupleInfo.partnerNickname);
          }
        }).catch((error) => {
          console.error('刷新情侣信息失败:', error);
        });
      }
    };

    // 监听通知事件
    app.addEventListener('notificationReceived', this.partnerProfileUpdateHandler);
  },

  onShow: function () {
    console.log("个人中心页面显示");

    // 每次显示时，都直接从全局数据刷新用户信息，保证头像和昵称的实时性
    if (app.globalData.userInfo) {
      this.setData({
        userInfo: app.globalData.userInfo,
      });
      console.log("从全局数据更新用户信息:", app.globalData.userInfo);

      // 检查未读消息数量，确保徽标实时更新
      app.checkUnreadMessages();
    }

    // 然后再刷新其他关联数据
    this.refreshData();
  },

  /**
   * 加载缓存数据
   */
  loadCachedData() {
    const userInfo = wx.getStorageSync("userInfo") || app.globalData.userInfo;
    const coupleInfo =
      wx.getStorageSync("coupleInfo") || app.globalData.coupleInfo;
    
    if (userInfo) {
      this.setData({ userInfo });
    }
    
    if (coupleInfo) {
      this.setData({ coupleInfo });
    }
  },

  /**
   * 加载用户资料
   */
  loadUserProfile() {
    // 首次加载显示骨架屏，后续刷新不显示
    if (!this.data.userInfo) {
      this.setData({ isLoading: true });
    }

    // 并发请求多个接口，优化加载性能
    Promise.all([
      this.getCoupleInfo(),
      this.getUserStats(),
      this.getNotificationCount(),
    ])
      .then(() => {
      this.setData({ isLoading: false });
        console.log("个人中心数据加载完成");
      })
      .catch((error) => {
        console.error("加载用户资料失败:", error);
      this.setData({ isLoading: false });
      wx.showToast({
          title: "部分数据加载失败",
          icon: "none",
          duration: 1500,
      });
    });
  },

  /**
   * 获取情侣信息
   */
  getCoupleInfo() {
    return new Promise((resolve, reject) => {
      // 先检查缓存
      const cachedCoupleInfo =
        wx.getStorageSync("coupleInfo") || app.globalData.coupleInfo;
      if (cachedCoupleInfo && cachedCoupleInfo.partnerId) {
        this.setData({ coupleInfo: cachedCoupleInfo });
        resolve(cachedCoupleInfo);
        return;
      }

      wx.cloud.callFunction({
        name: "checkCoupleStatus",
        data: {},
        success: (res) => {
          if (res.result.success && res.result.data.isBound) {
            const formattedCoupleInfo = this.formatCoupleInfo(
              res.result.data.coupleInfo
            );

            // 更新缓存
            wx.setStorageSync("coupleInfo", formattedCoupleInfo);
            app.globalData.coupleInfo = formattedCoupleInfo;

            this.setData({
              coupleInfo: formattedCoupleInfo,
            });
            console.log("情侣信息:", formattedCoupleInfo);
          }
          resolve(res);
        },
        fail: reject,
      });
    });
  },

  /**
   * 强制获取情侣信息（不使用缓存）- 修复问题1
   */
  getCoupleInfoForced() {
    return new Promise((resolve, reject) => {
      console.log('强制获取最新情侣信息，跳过缓存');

      wx.cloud.callFunction({
        name: "checkCoupleStatus",
        data: {},
        success: (res) => {
          console.log('checkCoupleStatus 云函数返回结果:', res.result);

          if (res.result.success && res.result.data.isBound) {
            // 修复问题1：详细记录云函数返回的原始数据
            console.log('云函数返回的原始情侣数据:', res.result.data.coupleInfo);
            console.log('云函数返回的伴侣信息:', res.result.data.coupleInfo.partner);

            const formattedCoupleInfo = this.formatCoupleInfo(
              res.result.data.coupleInfo
            );

            if (formattedCoupleInfo) {
              // 更新缓存
              wx.setStorageSync("coupleInfo", formattedCoupleInfo);
              app.globalData.coupleInfo = formattedCoupleInfo;

              this.setData({
                coupleInfo: formattedCoupleInfo,
              });
              console.log("强制刷新后的情侣信息:", formattedCoupleInfo);
              console.log("页面数据更新后的伴侣头像:", this.data.coupleInfo?.partnerAvatar);
            } else {
              console.error('formatCoupleInfo 返回 null');
            }
          } else {
            // 如果没有绑定关系，清除数据
            console.log('未绑定情侣关系，清除数据');
            this.setData({ coupleInfo: null });
          }
          resolve(res);
        },
        fail: (error) => {
          console.error('checkCoupleStatus 云函数调用失败:', error);
          reject(error);
        },
      });
    });
  },

  /**
   * 获取用户统计数据
   */
  getUserStats() {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: "getUserStats",
        data: {},
        success: (res) => {
          if (res.result.success) {
            const statsData = res.result.data.stats || {};
            
            // 格式化统计数据
            const formattedStats = {
              sentCount: statsData.totalCoupons || 0,
              receivedCount: statsData.receivedCoupons || 0, 
              redeemedCount: statsData.redeemedCoupons || 0,
              togetherDays: statsData.loveDays || 0,
            };
            
            this.setData({
              stats: formattedStats,
            });
          }
          resolve(res);
        },
        fail: reject,
      });
    });
  },

  /**
   * 获取通知数量
   */
  getNotificationCount() {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: "getNotificationCount",
        data: {},
        success: (res) => {
          if (res.result.success) {
            this.setData({
              notifications: {
                unreadCount: res.result.data.unreadCount,
              },
            });
          }
          resolve(res);
        },
        fail: reject,
      });
    });
  },

  /**
   * 刷新数据
   */
  refreshData() {
    // 确保用户已登录
    if (app.globalData.userInfo) {
    this.loadUserProfile();
    }
  },

  /**
   * 格式化情侣信息数据
   */
  formatCoupleInfo(coupleData) {
    if (!coupleData || !coupleData.partner) {
      console.log('formatCoupleInfo: 缺少情侣数据或伴侣信息');
      return null;
    }

    const partner = coupleData.partner;
    let togetherDays = 0;

    // 修复问题1：添加详细的头像调试日志
    console.log('formatCoupleInfo - 伴侣原始数据:', {
      partnerId: partner._id,
      partnerNickname: partner.nickName,
      partnerAvatarUrl: partner.avatarUrl,
      partnerUpdateTime: partner.updateTime
    });

    // 计算相恋天数
    if (coupleData.relationshipStartDate) {
      const startDate = new Date(coupleData.relationshipStartDate);
      const today = new Date();
      const timeDiff = today.getTime() - startDate.getTime();
      togetherDays = Math.max(0, Math.floor(timeDiff / (1000 * 3600 * 24)));
    }

    const formattedInfo = {
      partnerId: partner._id,
      partnerNickname: partner.nickName || "用户",
      partnerAvatar: partner.avatarUrl || "/images/default-avatar.png",
      relationshipStartDate: coupleData.relationshipStartDate,
      togetherDays: togetherDays,
      status: coupleData.status || "active",
    };

    console.log('formatCoupleInfo - 格式化后的数据:', formattedInfo);
    return formattedInfo;
  },



  /**
   * 修复问题1：伴侣头像加载成功事件
   */
  onPartnerAvatarLoad(e) {
    console.log('伴侣头像加载成功:', {
      src: e.currentTarget.src,
      naturalWidth: e.detail.width,
      naturalHeight: e.detail.height
    });
  },

  /**
   * 修复问题1：伴侣头像加载失败事件
   */
  onPartnerAvatarError(e) {
    console.error('伴侣头像加载失败:', {
      src: e.currentTarget.src,
      error: e.detail
    });
    // 尝试使用默认头像
    const coupleInfo = this.data.coupleInfo;
    if (coupleInfo) {
      coupleInfo.partnerAvatar = "/images/default-avatar.png";
      this.setData({ coupleInfo });
    }
  },

  /**
   * 修复问题1：我的头像加载失败事件
   */
  onMyAvatarError(e) {
    console.error('我的头像加载失败:', {
      src: e.currentTarget.src,
      error: e.detail
    });
  },

  /**
   * 新增：跳转到帮助反馈
   */
  goToFeedback() {
    console.log("跳转到帮助反馈");
    wx.navigateTo({
      url: "/pages/feedback/feedback",
    });
  },

  /**
   * 新增：跳转到关于我们
   */
  goToAbout() {
    console.log("跳转到关于我们");
    wx.navigateTo({
      url: "/pages/about/about",
    });
  },

  /**
   * 更换头像
   */
  changeAvatar() {
    wx.chooseImage({
      count: 1,
      sizeType: ["compressed"],
      sourceType: ["album", "camera"],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadAvatar(tempFilePath);
      },
      fail: (error) => {
        console.log("选择图片失败:", error);
        wx.showToast({
          title: "选择图片失败",
          icon: "none",
        });
      },
    });
  },

  /**
   * 上传头像
   */
  uploadAvatar(filePath) {
    wx.showLoading({ title: "上传中..." });

    const cloudPath = `avatars/${this.data.userInfo._id}_${Date.now()}.jpg`;
    
    wx.cloud.uploadFile({
      cloudPath: cloudPath,
      filePath: filePath,
      success: (uploadRes) => {
        this.updateUserAvatar(uploadRes.fileID);
      },
      fail: (error) => {
        console.error("头像上传失败:", error);
        wx.hideLoading();
        wx.showToast({
          title: "上传失败，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },

  /**
   * 更新用户头像
   */
  updateUserAvatar(avatarUrl) {
    wx.cloud.callFunction({
      name: "updateUserProfile",
      data: {
        avatarUrl: avatarUrl,
      },
      success: (res) => {
        wx.hideLoading();
        if (res.result.success) {
          const userInfo = { ...this.data.userInfo, avatarUrl };
          this.setData({ userInfo });
          
          // 更新本地缓存和全局状态
          wx.setStorageSync("userInfo", userInfo);
          app.globalData.userInfo = userInfo;

          // 刷新情侣信息以同步头像
          this.getCoupleInfo();
          
          wx.showToast({
            title: "头像更新成功",
            icon: "success",
            duration: 2000,
          });
        } else {
          wx.showToast({
            title: "更新失败，请重试",
            icon: "none",
            duration: 2000,
          });
        }
      },
      fail: (error) => {
        console.error("更新头像失败:", error);
        wx.hideLoading();
        wx.showToast({
          title: "更新失败，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },

  /**
   * 编辑个人资料
   */
  editProfile() {
    wx.navigateTo({
      url: "/pages/editProfile/editProfile",
    });
  },

  /**
   * 跳转到绑定页面
   */
  goToBinding() {
    wx.navigateTo({
      url: "/pages/binding/binding",
    });
  },

  /**
   * 显示解绑弹窗
   */
  showUnbindModal() {
    this.setData({ 
      showUnbindModal: true,
      confirmText: "",
    });
  },

  /**
   * 隐藏解绑弹窗
   */
  hideUnbindModal() {
    this.setData({ 
      showUnbindModal: false,
      confirmText: "",
    });
  },

  /**
   * 防止事件冒泡
   */
  preventClose() {
    // 空函数，防止事件冒泡
  },

  /**
   * 确认文本输入
   */
  onConfirmInput(e) {
    this.setData({
      confirmText: e.detail.value,
    });
  },

  /**
   * 确认解绑
   */
  confirmUnbind() {
    if (this.data.confirmText !== "确认解绑") {
      return;
    }

    this.setData({ 
      isLoading: true, 
      loadingText: "解绑中...",
      showUnbindModal: false,
    });

    wx.cloud.callFunction({
      name: "unbindCouple",
      data: {},
      success: (res) => {
        this.setData({ isLoading: false });
        
        if (res.result.success) {
          console.log('解绑成功，开始彻底清理所有数据');
          console.log('解绑结果:', res.result.data);

          // 彻底清除本地状态 - 修复问题2：彻底清除所有数据
          this.setData({
            coupleInfo: null,
            confirmText: "",
            stats: {
              sentCount: 0,
              receivedCount: 0,
              redeemedCount: 0
            },
            unreadCount: 0
          });

          // 彻底清除全局状态和所有相关缓存
          app.globalData.coupleInfo = null;
          app.globalData.userInfo = null; // 清除用户信息，强制重新获取
          app.globalData.unreadCount = 0; // 清除未读消息数

          // 清除所有本地存储
          wx.removeStorageSync("coupleInfo");
          wx.removeStorageSync("userInfo");
          wx.removeStorageSync("myCoupons");
          wx.removeStorageSync("notifications");

          // 清除消息徽章
          wx.removeTabBarBadge({
            index: 2 // 消息页面的索引
          });

          // 通知所有页面刷新状态
          app.emitEvent('globalDataRefresh', {
            reason: 'couple_unbound',
            timestamp: Date.now()
          });

          wx.showToast({
            title: "解绑成功",
            icon: "success",
            duration: 2000,
          });

          // 延迟刷新数据，确保状态清理完成
          setTimeout(() => {
            this.refreshData();
          }, 500);

          // 清除其他页面的缓存数据
          try {
            // 清除我的券页面的缓存
            const pages = getCurrentPages();
            const myCouponsPage = pages.find(
              (page) => page.route === "pages/myCoupons/myCoupons"
            );
            if (myCouponsPage) {
              myCouponsPage.setData({
                coupons: [],
                filteredCoupons: [],
              });
            }

            // 清除通知页面的缓存
            const notificationsPage = pages.find(
              (page) => page.route === "pages/notifications/notifications"
            );
            if (notificationsPage) {
              notificationsPage.setData({
                notifications: [],
                unreadCount: 0,
              });
            }
          } catch (e) {
            console.log("清除页面缓存时出错:", e);
          }
          
          // 通知其他页面更新状态
          wx.showModal({
            title: "解绑成功",
            content: `情侣关系已解除，已删除${
              res.result.data.deletedCoupons || 0
            }张券和${
              res.result.data.deletedNotifications || 0
            }条消息。所有相关数据已彻底清除。`,
            showCancel: false,
            confirmText: "知道了",
            success: () => {
              // 跳转到首页，确保状态同步 - 修复问题2
              wx.switchTab({
                url: '/pages/index/index',
                success: () => {
                  console.log('解绑后跳转到首页成功');
                },
                fail: (err) => {
                  console.error('跳转到首页失败:', err);
                }
              });
            },
          });
        } else {
          wx.showToast({
            title: res.result.message || "解绑失败",
            icon: "none",
            duration: 2000,
          });
        }
      },
      fail: (error) => {
        console.error("解绑失败:", error);
        this.setData({ isLoading: false });
        wx.showToast({
          title: "解绑失败，请重试",
          icon: "none",
          duration: 2000,
    });
  },
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.refreshData();
    // 延迟停止下拉刷新，确保用户看到刷新效果
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面卸载时清理事件监听器
   */
  onUnload() {
    console.log('个人中心页面卸载，清理事件监听器');

    // 清理伴侣资料更新通知监听器
    if (this.partnerProfileUpdateHandler) {
      app.removeEventListener('notificationReceived', this.partnerProfileUpdateHandler);
      this.partnerProfileUpdateHandler = null;
    }
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: "情侣信任券 - 记录我们的美好约定",
      path: "/pages/index/index",
    };
  },
});
