const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 获取应用统计信息
 */
exports.main = async (event, context) => {
  try {
    // 并行获取各种统计数据
    const [
      totalUsersResult,
      totalCouplesResult,
      totalCouponsResult,
      activeCouponsResult,
      redeemedCouponsResult,
      totalNotificationsResult
    ] = await Promise.all([
      // 总用户数
      db.collection('users').count(),
      // 总情侣数
      db.collection('couples').count(),
      // 总券数
      db.collection('coupons').count(),
      // 活跃券数
      db.collection('coupons').where({ status: 'active' }).count(),
      // 已兑换券数
      db.collection('coupons').where({ status: 'redeemed' }).count(),
      // 总通知数
      db.collection('notifications').count()
    ])
    
    // 获取最近7天的数据统计
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    
    const [
      recentUsersResult,
      recentCouponsResult,
      recentCouplesResult
    ] = await Promise.all([
      // 最近7天新用户
      db.collection('users')
        .where({
          createTime: db.command.gte(sevenDaysAgo)
        })
        .count(),
      // 最近7天新券
      db.collection('coupons')
        .where({
          createTime: db.command.gte(sevenDaysAgo)
        })
        .count(),
      // 最近7天新情侣
      db.collection('couples')
        .where({
          createTime: db.command.gte(sevenDaysAgo)
        })
        .count()
    ])
    
    // 获取券类型分布
    const couponTypeStats = await db.collection('coupons')
      .aggregate()
      .group({
        _id: '$type',
        count: db.command.aggregate.sum(1)
      })
      .end()
    
    // 计算兑换率
    const redemptionRate = totalCouponsResult.total > 0 
      ? (redeemedCouponsResult.total / totalCouponsResult.total * 100).toFixed(2)
      : 0
    
    // 计算情侣绑定率
    const coupleBindingRate = totalUsersResult.total > 0
      ? (totalCouplesResult.total * 2 / totalUsersResult.total * 100).toFixed(2)
      : 0
    
    const stats = {
      // 基础统计
      totalUsers: totalUsersResult.total,
      totalCouples: totalCouplesResult.total,
      totalCoupons: totalCouponsResult.total,
      activeCoupons: activeCouponsResult.total,
      redeemedCoupons: redeemedCouponsResult.total,
      totalNotifications: totalNotificationsResult.total,
      
      // 比率统计
      redemptionRate: parseFloat(redemptionRate),
      coupleBindingRate: parseFloat(coupleBindingRate),
      
      // 最近7天统计
      recentStats: {
        newUsers: recentUsersResult.total,
        newCoupons: recentCouponsResult.total,
        newCouples: recentCouplesResult.total
      },
      
      // 券类型分布
      couponTypes: couponTypeStats.list.map(item => ({
        type: item._id,
        count: item.count
      })),
      
      // 更新时间
      updateTime: new Date()
    }
    
    return {
      success: true,
      data: stats
    }
    
  } catch (error) {
    console.error('获取应用统计失败:', error)
    return {
      success: false,
      error: 'SYSTEM_ERROR',
      message: '系统错误，请稍后重试',
      data: {
        totalUsers: 0,
        totalCouples: 0,
        totalCoupons: 0,
        activeCoupons: 0,
        redeemedCoupons: 0,
        totalNotifications: 0,
        redemptionRate: 0,
        coupleBindingRate: 0,
        recentStats: {
          newUsers: 0,
          newCoupons: 0,
          newCouples: 0
        },
        couponTypes: [],
        updateTime: new Date()
      }
    }
  }
} 