/* pages/createCoupon/createCoupon.wxss */
@import "../../app.wxss";

.container {
  padding-bottom: 50rpx;
}

.form-preview-container {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* Coupon Preview */
.coupon-preview-wrapper {
  padding: 20rpx; /* Less padding for wrapper */
}
.coupon-preview {
  position: relative;
  padding: 40rpx 32rpx;
  border-radius: 20rpx;
  color: white;
  text-align: center;
  min-height: 320rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  /* 真实卡片比例 */
  aspect-ratio: 1.59 / 1;
}
.preview-number {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  font-size: 20rpx;
  font-weight: 700;
  font-family: 'Georgia', serif;
  background: linear-gradient(45deg, #ffffff, #ffffff);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.preview-number::before {
  content: '';
  color: rgba(255, 255, 255, 0.9);
  background: none;
  -webkit-text-fill-color: rgba(255, 255, 255, 0.9);
  margin-right: 4rpx;
  font-size: 18rpx;
}
.preview-type-tag {
  position: absolute;
  top: 24rpx;
  left: 24rpx;
  padding: 6rpx 12rpx;
  font-size: 18rpx;
  font-weight: 600;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(8rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  text-transform: uppercase;
  letter-spacing: 1rpx;
}
.preview-icon {
  font-size: 64rpx;
  margin: 20rpx 0 16rpx 0;
  filter: drop-shadow(0 4rpx 12rpx rgba(0, 0, 0, 0.2));
  animation: previewIconFloat 3s ease-in-out infinite;
}
@keyframes previewIconFloat {
  0%, 100% { 
    transform: translateY(0) rotate(0deg); 
  }
  50% { 
    transform: translateY(-6rpx) rotate(3deg); 
  }
}
.preview-title {
  font-size: 32rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  letter-spacing: 1rpx;
}
.preview-description {
  font-size: 22rpx;
  line-height: 1.4;
  opacity: 0.95;
  margin-bottom: 20rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}
.preview-expiry,
.preview-sender {
  font-size: 20rpx;
  opacity: 0.8;
  margin-bottom: 4rpx;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}
.preview-sender {
  margin-top: 8rpx;
}

/* Form Card */
.form-card {
  padding: 30rpx;
}
.form-group {
  margin-bottom: 30rpx;
}
.form-label {
  display: block;
  font-size: 26rpx;
  color: #555;
  margin-bottom: 10rpx;
  font-weight: 500;
}
.required-star {
  color: #ff69b4;
  margin-left: 5rpx;
}
.form-input,
.form-textarea {
  width: 100%;
  background: #f7f8fa;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  min-height: 80rpx;
}
.form-textarea {
  min-height: 150rpx;
  line-height: 1.5;
}
.form-input:focus,
.form-textarea:focus {
  border-color: #ffb6c1;
  background-color: #fff;
}
.picker-input {
  line-height: normal; /* Fix picker text vertical alignment */
}
.error-message {
  font-size: 22rpx;
  color: #e53e3e; /* Red for errors */
  margin-top: 8rpx;
}
.inline-group {
  display: flex;
  gap: 20rpx;
}
.half-group {
  flex: 1;
}

/* Customization Section */
.customization-section {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
  padding: 20rpx 0;
  border-top: 1rpx dashed #eee;
  border-bottom: 1rpx dashed #eee;
}
.custom-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 10rpx;
}
.custom-label {
  font-size: 24rpx;
  color: #666;
}
.selected-display {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  border: 1rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-display {
  font-size: 32rpx;
}
.color-display {
  border: 2rpx solid #fff; /* White border for dark colors */
  box-shadow: 0 0 5rpx rgba(0, 0, 0, 0.1);
}

/* Picker Container */
.picker-container {
  margin-top: 20rpx;
  padding: 20rpx;
  max-height: 300rpx;
  overflow-y: auto;
}
.picker-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(70rpx, 1fr));
  gap: 20rpx;
}
.color-picker-grid {
  grid-template-columns: repeat(auto-fill, minmax(100rpx, 1fr));
}
.picker-option {
  padding: 15rpx;
  border-radius: 12rpx;
  background-color: #f0f0f0;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s;
}
.icon-option {
  font-size: 36rpx;
}
.picker-option:active {
  background-color: #ffd2e0; /* Light pink on press */
}
.color-option-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  background-color: transparent; /* Wrapper is transparent */
}
.color-option {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%; /* Circles for colors */
  border: 2rpx solid rgba(0, 0, 0, 0.1);
}
.color-name {
  font-size: 20rpx;
  color: #555;
}

.submit-btn {
  width: 100%;
  margin-top: 20rpx; /* Add some space before the button if pickers are open */
  padding: 24rpx;
}

/* ==================== 券类型选择器样式 ==================== */
.coupon-type-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.type-tag {
  flex: 0 0 calc(25% - 12rpx); /* 一行4个 */
  min-width: 140rpx;
  padding: 20rpx 16rpx;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  text-align: center;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.type-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  transition: all 300ms ease;
  z-index: 0;
}

.type-tag:hover::before {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
}

.type-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.type-name {
  font-size: 24rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  position: relative;
  z-index: 1;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

/* 券类型颜色变体 */
.type-promise {
  background: linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%);
  box-shadow: 0 8rpx 25rpx rgba(255, 109, 150, 0.3);
}

.type-task {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.type-reward {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  box-shadow: 0 8rpx 25rpx rgba(255, 215, 0, 0.3);
}

.type-reward .type-name {
  color: #2C2C2C;
  text-shadow: 0 1rpx 3rpx rgba(255, 255, 255, 0.3);
}

.type-apology {
  background: linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%);
  box-shadow: 0 8rpx 25rpx rgba(138, 43, 226, 0.3);
}

.type-special {
  background: linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 25%, #45B7D1 50%, #96CEB4 75%, #FFEAA7 100%);
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.3);
  animation: specialGlow 3s ease-in-out infinite;
}

@keyframes specialGlow {
  0%, 100% {
    box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.3);
  }
  50% {
    box-shadow: 0 12rpx 35rpx rgba(255, 107, 107, 0.5);
  }
}

/* 选中状态 */
.type-tag.selected {
  transform: translateY(-4rpx) scale(1.05);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 12rpx 35rpx rgba(0, 0, 0, 0.2);
}

.type-tag.selected::before {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
}

.type-tag.selected .type-icon {
  font-size: 36rpx;
  animation: selectedBounce 0.5s ease-out;
}

.type-tag.selected .type-name {
  font-weight: 700;
  color: white;
}

@keyframes selectedBounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

/* 点击反馈 */
.type-tag:active {
  transform: translateY(-2rpx) scale(0.98);
}

/* ==================== 响应式适配 ==================== */
@media (max-width: 750rpx) {
  .type-tag {
    flex: 0 0 calc(33.333% - 11rpx); /* 小屏幕一行3个 */
    min-width: 120rpx;
    padding: 16rpx 12rpx;
  }
  
  .type-icon {
    font-size: 28rpx;
  }
  
  .type-name {
    font-size: 22rpx;
  }
  
  .preview-number {
    font-size: 18rpx;
    top: 20rpx;
    right: 20rpx;
  }
  
  .preview-type-tag {
    font-size: 16rpx;
    padding: 4rpx 10rpx;
    top: 20rpx;
    left: 20rpx;
  }
  
  .preview-icon {
    font-size: 56rpx;
  }
  
  .preview-title {
    font-size: 28rpx;
  }
}

/* ==================== 深色模式适配 ==================== */
@media (prefers-color-scheme: dark) {
  .type-tag {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .type-tag.selected {
    border-color: rgba(255, 255, 255, 0.4);
  }
}
