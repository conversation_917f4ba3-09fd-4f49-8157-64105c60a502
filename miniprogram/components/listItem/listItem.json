{"component": true, "componentGenerics": {}, "usingComponents": {}, "componentPlaceholder": {}, "styleIsolation": "isolated", "description": "通用列表项组件，适用于设置菜单、通知列表等，支持左侧图标/图片、主标题、副标题/描述、右侧箭头或操作元素", "version": "1.0.0", "author": "情侣信任券小程序团队", "category": "Navigation Components", "keywords": ["列表", "列表项", "菜单", "导航", "设置"], "repository": {"type": "git", "url": "https://github.com/coupleapp/miniprogram-components"}, "license": "MIT", "properties": {"icon": {"type": "String", "description": "左侧图标 (emoji 或字符)", "default": "", "required": false}, "iconImage": {"type": "String", "description": "左侧图标图片URL", "default": "", "required": false}, "title": {"type": "String", "description": "主标题", "default": "", "required": true}, "subtitle": {"type": "String", "description": "副标题", "default": "", "required": false}, "description": {"type": "String", "description": "详细描述", "default": "", "required": false}, "rightText": {"type": "String", "description": "右侧文本", "default": "", "required": false}, "showArrow": {"type": "Boolean", "description": "是否显示右侧箭头", "default": false, "required": false}, "arrowIcon": {"type": "String", "description": "箭头图标", "default": "›", "required": false}, "badge": {"type": "String", "description": "徽章文本", "default": "", "required": false}, "tags": {"type": "Array", "description": "标签数组", "default": [], "required": false}, "status": {"type": "String", "description": "状态", "default": "", "enum": ["success", "warning", "error", "info", "neutral"], "required": false}, "statusText": {"type": "String", "description": "状态文本", "default": "", "required": false}, "clickable": {"type": "Boolean", "description": "是否可点击", "default": true, "required": false}, "disabled": {"type": "Boolean", "description": "是否禁用", "default": false, "required": false}, "active": {"type": "Boolean", "description": "是否激活状态", "default": false, "required": false}, "showDivider": {"type": "Boolean", "description": "是否显示分隔线", "default": false, "required": false}, "size": {"type": "String", "description": "列表项尺寸", "default": "normal", "enum": ["normal", "compact", "large"], "required": false}, "itemId": {"type": "String", "description": "列表项ID，用于识别", "default": "", "required": false}, "extraData": {"type": "Object", "description": "额外数据，方便在事件中传递", "default": {}, "required": false}}, "events": {"tap": {"description": "列表项点击事件", "parameters": {"itemId": "String - 列表项ID", "title": "String - 标题", "subtitle": "String - 副标题", "extraData": "Object - 额外数据", "index": "Number - 索引", "timeStamp": "Number - 时间戳"}}, "iconError": {"description": "图标加载失败事件", "parameters": {"itemId": "String - 列表项ID", "iconImage": "String - 图标URL", "error": "Object - 错误信息"}}, "feedback": {"description": "用户交互反馈事件", "parameters": {"type": "String - 反馈类型", "itemId": "String - 列表项ID"}}}, "methods": {"setActive": {"description": "设置激活状态", "parameters": {"active": "Boolean - 是否激活"}, "returns": "void"}, "setDisabled": {"description": "设置禁用状态", "parameters": {"disabled": "Boolean - 是否禁用"}, "returns": "void"}, "updateBadge": {"description": "更新徽章", "parameters": {"badge": "String - 徽章文本"}, "returns": "void"}, "addTag": {"description": "添加标签", "parameters": {"tag": "String - 标签文本"}, "returns": "void"}, "removeTag": {"description": "移除标签", "parameters": {"tag": "String - 标签文本"}, "returns": "void"}, "clearTags": {"description": "清空所有标签", "parameters": {}, "returns": "void"}, "getItemInfo": {"description": "获取列表项完整信息", "parameters": {}, "returns": "Object - 列表项信息"}}, "slots": {"content": {"description": "自定义内容插槽，显示在描述区域下方"}, "right": {"description": "自定义右侧内容插槽，替代默认的右侧内容"}}, "examples": [{"title": "基础使用", "description": "显示基本的列表项", "code": {"wxml": "<list-item icon=\"❤️\" title=\"我的情侣\" subtitle=\"小甜心 · 已绑定\" show-arrow=\"{{true}}\" bind:tap=\"onItemTap\" />", "js": "Page({ onItemTap(e) { console.log('列表项被点击:', e.detail); } })"}}, {"title": "带徽章和状态", "description": "显示带有徽章和状态指示的列表项", "code": {"wxml": "<list-item icon=\"🔔\" title=\"消息通知\" subtitle=\"新消息提醒\" badge=\"3\" status=\"warning\" status-text=\"有新消息\" show-arrow=\"{{true}}\" />"}}, {"title": "带标签列表项", "description": "显示带有标签的列表项", "code": {"wxml": "<list-item icon=\"⚙️\" title=\"系统设置\" tags=\"{{['隐私', '安全', '通用']}}\" show-arrow=\"{{true}}\" />"}}, {"title": "使用图片图标", "description": "使用图片作为左侧图标", "code": {"wxml": "<list-item icon-image=\"https://example.com/avatar.jpg\" title=\"用户头像\" subtitle=\"点击更换头像\" show-arrow=\"{{true}}\" bind:iconError=\"onIconError\" />"}}, {"title": "自定义内容", "description": "使用插槽自定义内容", "code": {"wxml": "<list-item title=\"自定义内容\" show-arrow=\"{{true}}\"><view slot=\"content\"><text>这是自定义内容区域</text></view><button slot=\"right\" size=\"mini\">操作</button></list-item>"}}, {"title": "不同尺寸", "description": "展示不同尺寸的列表项", "code": {"wxml": "<list-item title=\"紧凑模式\" size=\"compact\" />\n<list-item title=\"正常模式\" size=\"normal\" />\n<list-item title=\"大尺寸模式\" size=\"large\" />"}}], "changelog": [{"version": "1.0.0", "date": "2024-01-01", "changes": ["初始版本发布", "支持左侧图标/图片展示", "支持标题、副标题、描述文本", "支持右侧文本、状态、箭头", "支持徽章和标签功能", "支持多种尺寸和状态", "支持点击交互", "支持自定义插槽内容", "支持图标加载失败处理", "支持响应式设计", "支持深色模式", "支持无障碍功能"]}], "dependencies": {}, "devDependencies": {}, "peerDependencies": {}, "browserslist": ["iOS >= 9", "Android >= 5"], "miniProgramMinVersion": "2.10.0", "tags": ["UI", "Navigation", "List", "<PERSON><PERSON>", "Interactive"], "screenshots": ["https://example.com/screenshots/list-item-basic.png", "https://example.com/screenshots/list-item-with-badge.png", "https://example.com/screenshots/list-item-with-tags.png"]}