# 云函数文件检查清单

## 📋 新增云函数文件完整性检查

在部署之前，请确认以下所有文件都已正确创建：

### 1. markNotificationAsRead
- [ ] `cloudfunctions/markNotificationAsRead/index.js` 存在且内容完整
- [ ] `cloudfunctions/markNotificationAsRead/package.json` 存在且格式正确

### 2. markAllNotificationsAsRead
- [ ] `cloudfunctions/markAllNotificationsAsRead/index.js` 存在且内容完整
- [ ] `cloudfunctions/markAllNotificationsAsRead/package.json` 存在且格式正确

### 3. deleteNotification
- [ ] `cloudfunctions/deleteNotification/index.js` 存在且内容完整
- [ ] `cloudfunctions/deleteNotification/package.json` 存在且格式正确

### 4. clearAllNotifications
- [ ] `cloudfunctions/clearAllNotifications/index.js` 存在且内容完整
- [ ] `cloudfunctions/clearAllNotifications/package.json` 存在且格式正确

### 5. getNotificationCount
- [ ] `cloudfunctions/getNotificationCount/index.js` 存在且内容完整
- [ ] `cloudfunctions/getNotificationCount/package.json` 存在且格式正确

### 6. unbindCouple
- [ ] `cloudfunctions/unbindCouple/index.js` 存在且内容完整
- [ ] `cloudfunctions/unbindCouple/package.json` 存在且格式正确

### 7. getCouponDetail
- [ ] `cloudfunctions/getCouponDetail/index.js` 存在且内容完整
- [ ] `cloudfunctions/getCouponDetail/package.json` 存在且格式正确

### 8. getAppStats
- [ ] `cloudfunctions/getAppStats/index.js` 存在且内容完整
- [ ] `cloudfunctions/getAppStats/package.json` 存在且格式正确

### 9. submitFeedback
- [ ] `cloudfunctions/submitFeedback/index.js` 存在且内容完整
- [ ] `cloudfunctions/submitFeedback/package.json` 存在且格式正确

## 🔧 快速验证命令

您可以在终端中运行以下命令来快速检查文件是否完整：

```bash
# 检查所有新增云函数目录是否存在
echo "检查云函数目录..."
for func in markNotificationAsRead markAllNotificationsAsRead deleteNotification clearAllNotifications getNotificationCount unbindCouple getCouponDetail getAppStats submitFeedback; do
  if [ -d "cloudfunctions/$func" ]; then
    echo "✅ $func 目录存在"
  else
    echo "❌ $func 目录不存在"
  fi
done

# 检查所有必要文件是否存在
echo -e "\n检查必要文件..."
for func in markNotificationAsRead markAllNotificationsAsRead deleteNotification clearAllNotifications getNotificationCount unbindCouple getCouponDetail getAppStats submitFeedback; do
  if [ -f "cloudfunctions/$func/index.js" ] && [ -f "cloudfunctions/$func/package.json" ]; then
    echo "✅ $func 文件完整"
  else
    echo "❌ $func 文件不完整"
  fi
done
```

## 📊 文件内容要点检查

### index.js 文件应包含：
- [ ] `const cloud = require('wx-server-sdk')` 导入
- [ ] `cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })` 初始化
- [ ] `const db = cloud.database()` 数据库实例
- [ ] `exports.main = async (event, context) => {}` 主函数
- [ ] 完整的错误处理 try-catch
- [ ] 参数验证逻辑
- [ ] 权限检查（如适用）
- [ ] 详细的JSDoc注释

### package.json 文件应包含：
- [ ] 正确的 `name` 字段（与文件夹名相同）
- [ ] `"main": "index.js"` 入口文件
- [ ] `"wx-server-sdk": "~2.6.3"` 依赖
- [ ] 正确的JSON格式（无语法错误）

## 🎯 部署前最终检查

### 开发环境检查
- [ ] 微信开发者工具版本 >= 1.05.2103200
- [ ] 小程序基础库版本 >= 2.8.0
- [ ] 云开发环境已创建并选择
- [ ] 用户具有云函数部署权限

### 代码质量检查
- [ ] 所有云函数代码无语法错误
- [ ] 所有必要的参数验证已添加
- [ ] 所有数据库操作已添加错误处理
- [ ] 所有敏感操作已添加权限检查

### 项目配置检查
- [ ] `project.config.json` 中 `cloudfunctionRoot` 设置正确
- [ ] `app.json` 配置无误
- [ ] 云开发环境ID配置正确

## 📝 部署顺序建议

建议按以下顺序部署云函数，以便更好地跟踪问题：

1. **通知相关函数（优先级高）**
   - getNotificationCount
   - markNotificationAsRead
   - markAllNotificationsAsRead
   - deleteNotification
   - clearAllNotifications

2. **业务功能函数**
   - getCouponDetail
   - unbindCouple

3. **统计和反馈函数**
   - getAppStats
   - submitFeedback

## ❗ 重要提醒

1. **备份现有数据**: 部署前请确保数据库数据已备份
2. **测试环境优先**: 建议先在测试环境部署验证
3. **逐个部署**: 不要一次性部署所有函数，逐个部署便于排查问题
4. **查看日志**: 每次部署后都要查看云函数日志确认无误
5. **功能测试**: 部署成功后立即进行功能测试

---

**检查完成时间**: ___________  
**检查人员**: ___________  
**所有项目是否通过**: □ 是 □ 否  
**备注**: ___________ 