// pages/createCoupon/createCoupon.js
const app = getApp();
const predefinedIcons = ['💖', '🎁', '✨', '🎉', '😊', '😘', '🤝', '💪', '💡', '📅', '🍕', '✈️', '👑', '🌟'];
const predefinedColors = [
  { name: '经典粉', value: 'linear-gradient(135deg, #FF69B4, #FFB6C1)' },
  { name: '活力橙', value: 'linear-gradient(135deg, #FFA07A, #FF7F50)' },
  { name: '清新蓝', value: 'linear-gradient(135deg, #87CEFA, #1E90FF)' },
  { name: '生机绿', value: 'linear-gradient(135deg, #98FB98, #3CB371)' },
  { name: '温柔紫', value: 'linear-gradient(135deg, #D8BFD8, #BA55D3)' },
];

Page({
  data: {
    userInfo: null,
    coupleInfo: null,
    name: '',
    description: '',
    selectedCouponType: 'promise',
    selectedIcon: '💕',
    selectedColor: 'linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%)',
    expiresAtDate: '',
    expiresAtTime: '23:59',
    previewCouponNumber: '',
    selectedCouponTypeText: '承诺券',
    couponTypes: [
      { value: 'promise', name: '承诺券', icon: '💕', color: 'linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%)' },
      { value: 'task', name: '任务券', icon: '📝', color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
      { value: 'reward', name: '奖励券', icon: '🎁', color: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)' },
      { value: 'apology', name: '道歉券', icon: '🙏', color: 'linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%)' },
      { value: 'special', name: '特别券', icon: '✨', color: 'linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 25%, #45B7D1 50%, #96CEB4 75%, #FFEAA7 100%)' }
    ],
    predefinedIcons: ['💕', '❤️', '🌹', '💖', '💝', '🎁', '🌟', '✨'],
    predefinedColors: [
      { name: '浪漫粉', value: 'linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%)' },
      { name: '深邃蓝', value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
      { name: '奢华金', value: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)' },
      { name: '神秘紫', value: 'linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%)' },
      { name: '彩虹色', value: 'linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 25%, #45B7D1 50%, #96CEB4 75%, #FFEAA7 100%)' }
    ],
    showIconPicker: false,
    showColorPicker: false,
    minExpiryDate: '',
    isSubmitting: false,
    themeGradient: app.globalData.gradientBackground,
    formErrors: {},
  },

  onLoad: function (options) {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    this.setData({
      userInfo: app.globalData.userInfo,
      coupleInfo: app.globalData.coupleInfo,
      themeGradient: app.globalData.gradientBackground,
      minExpiryDate: this.formatDateForPicker(today),
      expiresAtDate: this.formatDateForPicker(tomorrow),
    });

    // 处理模板数据 - 新格式
    if (options.template) {
      try {
        const templateData = JSON.parse(decodeURIComponent(options.template));
        console.log('使用模板数据:', templateData);
        
        this.setData({
          name: templateData.title || '',
          description: templateData.description || '',
          selectedCouponType: templateData.couponType || 'promise',
          selectedIcon: templateData.icon || '💕'
        });
        
        // 更新券类型相关的默认值
        if (templateData.couponType) {
          this.updateCouponTypeDefaults(templateData.couponType);
        }

        // 移除"模板已应用"弹窗提示，提升用户体验
      } catch (error) {
        console.error('解析模板数据失败:', error);
      }
    }
    
    // 兼容旧格式（保留向后兼容）
    if (options.templateTitle) {
        this.setData({ name: decodeURIComponent(options.templateTitle) });
    }
    if (options.templateDesc) {
        this.setData({ description: decodeURIComponent(options.templateDesc) });
    }

    if (!this.data.userInfo) {
      wx.showModal({
        title: '请先登录',
        content: '登录后才能创建信任券哦！',
        confirmText: '去登录',
        showCancel: false,
        success: (res) => {
          if (res.confirm) {
            wx.switchTab({ url: '/pages/index/index' });
          }
        }
      });
      return;
    }

    if (!app.isCoupleBound()) {
      wx.showModal({
        title: '请先绑定情侣',
        content: '绑定情侣后才能发送信任券哦！',
        showCancel: true,
        confirmText: '去绑定',
        cancelText: '返回首页',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/binding/binding' });
          } else {
            wx.switchTab({ url: '/pages/index/index' });
          }
        }
      });
    }

    this.initializeData();
    this.generatePreviewCouponNumber();
  },

  formatDateForPicker: function(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  onInput: function (e) {
    const field = e.currentTarget.dataset.field;
    this.setData({
      [field]: e.detail.value,
      [`formErrors.${field}`]: ''
    });
  },

  onDateChange: function (e) {
    this.setData({ expiresAtDate: e.detail.value, 'formErrors.expiresAtDate': '' });
  },

  onTimeChange: function (e) {
    this.setData({ expiresAtTime: e.detail.value, 'formErrors.expiresAtTime': '' });
  },

  toggleIconPicker: function() {
    this.setData({ showIconPicker: !this.data.showIconPicker, showColorPicker: false });
  },
  
  selectIcon: function(e) {
    this.setData({ selectedIcon: e.currentTarget.dataset.icon, showIconPicker: false });
  },

  toggleColorPicker: function() {
    this.setData({ showColorPicker: !this.data.showColorPicker, showIconPicker: false });
  },

  selectColor: function(e) {
    this.setData({ selectedColor: e.currentTarget.dataset.color, showColorPicker: false });
  },
  
  validateForm: function() {
    const errors = {};
    let isValid = true;
    if (!this.data.name.trim()) {
      errors.name = "券标题不能为空";
      isValid = false;
    } else if (this.data.name.trim().length > 20) {
      errors.name = "券标题不能超过20个字";
      isValid = false;
    }
    if (!this.data.description.trim()) {
      errors.description = "券描述不能为空";
      isValid = false;
    } else if (this.data.description.trim().length > 100) {
      errors.description = "券描述不能超过100个字";
      isValid = false;
    }
    if (this.data.expiresAtDate && new Date(this.data.expiresAtDate + ' ' + this.data.expiresAtTime) < new Date()) {
        errors.expiresAtDate = "有效期不能早于当前时间";
        isValid = false;
    }

    this.setData({ formErrors: errors });
    return isValid;
  },

  handleSubmit: function () {
    if (!this.validateForm()) {
      wx.showToast({ title: '请检查表单信息', icon: 'none' });
      return;
    }
    if (!this.data.userInfo || !app.isCoupleBound()) {
      wx.showToast({ title: '请先登录并绑定情侣', icon: 'none' });
      return;
    }

    this.setData({ isSubmitting: true });

    const fullExpiresAt = this.data.expiresAtDate ? new Date(this.data.expiresAtDate + 'T' + this.data.expiresAtTime + ':00') : null;

    const couponData = {
      name: this.data.name.trim(),
      description: this.data.description.trim(),
      icon: this.data.selectedIcon,
      bgColor: this.data.selectedColor,
      expiryDate: fullExpiresAt ? fullExpiresAt.toISOString() : null,
      status: 'received'
    };

    wx.cloud.callFunction({
      name: 'createOrUpdateCoupon',
      data: couponData,
      success: res => {
        if (res.result && res.result.success) {
          wx.showToast({ title: '信任券已发送！', icon: 'success', duration: 2000 });
          
          // 发券成功后，立即检查未读消息数量（确保对方能及时看到徽章）
          console.log("发券成功，开始检查消息徽章更新...");
          
          // 多次检查确保徽章更新成功
          const checkBadge = (attempt = 1) => {
            setTimeout(() => {
              console.log(`第${attempt}次检查消息徽章...`);
              app.checkUnreadMessages();
              
              // 最多尝试3次
              if (attempt < 3) {
                checkBadge(attempt + 1);
              }
            }, attempt * 1000); // 1秒、2秒、3秒后分别检查
          };
          
          checkBadge();
          
          setTimeout(() => {
            // 跳转到"我的券"页面的"我发送的"页签
            wx.switchTab({
              url: '/pages/myCoupons/myCoupons',
              success: () => {
                // 通过全局事件通知页面切换到"我发送的"页签
                const app = getApp();
                app.emitEvent('switchToSentTab', {
                  reason: 'coupon_created',
                  timestamp: Date.now()
                });
              }
            });
          }, 2000);
        } else {
          wx.showToast({ title: res.result.message || '创建失败', icon: 'none' });
        }
      },
      fail: err => {
        wx.showToast({ title: '网络错误，请重试', icon: 'none' });
        console.error("Coupon creation failed:", err);
      },
      complete: () => {
        this.setData({ isSubmitting: false });
      }
    });
  },

  initializeData() {
    const today = new Date();
    const minDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    this.setData({ minExpiryDate: minDate });
    
    const userInfo = wx.getStorageSync('userInfo') || {};
    this.setData({ userInfo });
    
    this.updateCouponTypeDefaults('promise');
  },

  generatePreviewCouponNumber() {
    const currentYear = new Date().getFullYear();
    const yearSuffix = String(currentYear).slice(-2);
    const randomNum = Math.floor(Math.random() * 9000) + 1000;
    const couponNumber = `${yearSuffix}${randomNum}`;
    
    this.setData({ previewCouponNumber: couponNumber });
  },

  selectCouponType(e) {
    const { type } = e.currentTarget.dataset;
    const selectedType = this.data.couponTypes.find(t => t.value === type);
    
    if (selectedType) {
      this.setData({
        selectedCouponType: type,
        selectedCouponTypeText: selectedType.name,
        selectedIcon: selectedType.icon,
        selectedColor: selectedType.color
      });
      
      this.updateCouponTypeDefaults(type);
      
      this.clearFieldError('couponType');
    }
  },

  updateCouponTypeDefaults(type) {
    const typeIconsMap = {
      promise: ['💕', '❤️', '🌹', '💖', '💝', '💐', '🎀', '💌'],
      task: ['📝', '✅', '📋', '🎯', '⚡', '🔥', '💪', '🏆'],
      reward: ['🎁', '🏆', '🎉', '🌟', '💎', '👑', '🎊', '🥇'],
      apology: ['🙏', '😔', '💐', '🌹', '😢', '🤝', '💔', '🕊️'],
      special: ['✨', '🌈', '🎆', '💫', '🔮', '🎭', '🎪', '🦄']
    };
    
    const typeColorsMap = {
      promise: [
        { name: '浪漫粉', value: 'linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%)' },
        { name: '温柔红', value: 'linear-gradient(135deg, #FF69B4 0%, #FFB6C1 100%)' },
        { name: '甜蜜橙', value: 'linear-gradient(135deg, #FF7F50 0%, #FFA07A 100%)' }
      ],
      task: [
        { name: '深邃蓝', value: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
        { name: '海洋蓝', value: 'linear-gradient(135deg, #4A90E2 0%, #7BB3F0 100%)' },
        { name: '天空蓝', value: 'linear-gradient(135deg, #87CEEB 0%, #B0E0E6 100%)' }
      ],
      reward: [
        { name: '奢华金', value: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)' },
        { name: '皇家金', value: 'linear-gradient(135deg, #D4AF37 0%, #FFD700 100%)' },
        { name: '阳光黄', value: 'linear-gradient(135deg, #FFF700 0%, #FFED4E 100%)' }
      ],
      apology: [
        { name: '神秘紫', value: 'linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%)' },
        { name: '优雅紫', value: 'linear-gradient(135deg, #9966CC 0%, #DA70D6 100%)' },
        { name: '薰衣草', value: 'linear-gradient(135deg, #E6E6FA 0%, #DDA0DD 100%)' }
      ],
      special: [
        { name: '彩虹色', value: 'linear-gradient(135deg, #FF6B6B 0%, #4ECDC4 25%, #45B7D1 50%, #96CEB4 75%, #FFEAA7 100%)' },
        { name: '极光色', value: 'linear-gradient(135deg, #00C9FF 0%, #92FE9D 100%)' },
        { name: '夕阳色', value: 'linear-gradient(135deg, #FC466B 0%, #3F5EFB 100%)' }
      ]
    };
    
    this.setData({
      predefinedIcons: typeIconsMap[type] || typeIconsMap.promise,
      predefinedColors: typeColorsMap[type] || typeColorsMap.promise
    });
  },

  clearFieldError(field) {
    const formErrors = { ...this.data.formErrors };
    delete formErrors[field];
    this.setData({ formErrors });
  },
});
