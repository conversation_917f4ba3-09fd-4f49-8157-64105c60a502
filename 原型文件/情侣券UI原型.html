<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情侣信誉券小程序 - UI原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .phone-frame {
            width: 377px;
            height: 814px;
            border: 1px solid #333;
            border-radius: 25px;
            background: #000;
            padding: 2px;
            position: relative;
            overflow: hidden;
        }
        
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 23px;
            background: #fff;
            overflow: hidden;
            position: relative;
        }
        
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .page-content {
            height: calc(100% - 44px);
            position: relative;
            overflow: hidden;
        }
        
        /* 玻璃拟态效果 */
        .glass-card {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        
        /* 配色方案 */
        .primary { color: #FF6D96; }
        .secondary { color: #FFE4E1; }
        .accent { color: #FFD700; }
        .text-dark { color: #333333; }
        .text-medium { color: #666666; }
        .text-light { color: #CCCCCC; }
        
        .bg-primary { background-color: #FF6D96; }
        .bg-secondary { background-color: #FFE4E1; }
        .bg-accent { background-color: #FFD700; }
        
        /* 按钮样式 */
        .btn {
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #FF6D96, #FF8FA3);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 109, 150, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 109, 150, 0.6);
        }
        
        /* 卡片样式 */
        .card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }
        
        /* 导航标签 */
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #666;
        }
        
        .tab-item.active {
            color: #FF6D96;
        }
        
        /* 页面标题 */
        .page-title {
            text-align: center;
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        /* 动画效果 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        .floating {
            animation: float 3s ease-in-out infinite;
        }
        
        .pulsing {
            animation: pulse 2s ease-in-out infinite;
        }
        
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
        
        /* 折线图动画 */
        .chart-line {
            stroke-dasharray: 200;
            stroke-dashoffset: 200;
            animation: draw-line 2s forwards;
        }
        
        @keyframes draw-line {
            to { stroke-dashoffset: 0; }
        }
        
        /* 数据统计动画 */
        .stat-bar {
            animation: grow-bar 1s ease-out;
            transform-origin: bottom;
        }
        
        @keyframes grow-bar {
            from { transform: scaleY(0); }
            to { transform: scaleY(1); }
        }
        
        /* 新消息闪烁 */
        .new-indicator {
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
    </style>
    
    <!-- SVG图标系统 -->
    <svg style="display: none;">
        <symbol id="heart" viewBox="0 0 24 24">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
        </symbol>
        <symbol id="plus" viewBox="0 0 24 24">
            <path d="M12 2v20M2 12h20" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </symbol>
        <symbol id="gift" viewBox="0 0 24 24">
            <path d="M20 12v6a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-6M2 7h20v4H2zM12 22V7M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7zM12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"/>
        </symbol>
        <symbol id="user" viewBox="0 0 24 24">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"/>
        </symbol>
        <symbol id="bell" viewBox="0 0 24 24">
            <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9M13.73 21a2 2 0 0 1-3.46 0"/>
        </symbol>
        <symbol id="link" viewBox="0 0 24 24">
            <path d="M15 7h3a5 5 0 0 1 5 5 5 5 0 0 1-5 5h-3m-6 0H6a5 5 0 0 1-5-5 5 5 0 0 1 5-5h3m1 5h8"/>
        </symbol>
    </svg>
</head>
<body>
    <div class="container">
        
        <!-- 1. 登录页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(135deg, #FF6D96 0%, #FFE4E1 100%); display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center; padding: 40px 30px;">
                    <div class="page-title">登录页面</div>
                    <svg width="120" height="120" style="margin-bottom: 40px;" class="floating">
                        <use href="#heart" fill="#fff" width="120" height="120"/>
                    </svg>
                    <h1 style="color: white; font-size: 32px; margin-bottom: 16px; font-weight: 300;">情侣信誉券</h1>
                    <p style="color: rgba(255,255,255,0.8); font-size: 16px; margin-bottom: 60px; line-height: 1.5;">让爱的承诺有迹可循<br>让甜蜜的约定成为现实</p>
                    <button class="btn" style="background: rgba(255,255,255,0.9); color: #FF6D96; width: 200px; margin-bottom: 20px;">
                        <svg width="20" height="20" style="margin-right: 8px; vertical-align: middle;">
                            <use href="#user"/>
                        </svg>
                        微信一键登录
                    </button>
                    <p style="color: rgba(255,255,255,0.6); font-size: 12px;">登录即表示同意用户协议和隐私政策</p>
                </div>
            </div>
        </div>
        
        <!-- 2. 首页/主页 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #FFE4E1 0%, #fff 30%); padding: 20px;">
                    <div class="page-title">首页</div>
                    <!-- 顶部问候 -->
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h2 style="color: #333; font-size: 24px; margin-bottom: 8px;">早安，小甜心 ♡</h2>
                        <p style="color: #666; font-size: 14px;">今天也要甜甜地度过呢</p>
                    </div>
                    
                    <!-- 情侣状态卡片 -->
                    <div class="glass-card" style="padding: 20px; margin-bottom: 20px; text-align: center;">
                        <div style="display: flex; justify-content: center; align-items: center; gap: 20px; margin-bottom: 15px;">
                            <div style="text-align: center;">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616c68d82b8?w=60&h=60&fit=crop&crop=face" style="width: 50px; height: 50px; border-radius: 50%; border: 2px solid #FF6D96;">
                                <div style="font-size: 12px; color: #666; margin-top: 5px;">小甜心</div>
                            </div>
                            <svg width="30" height="30" style="color: #FF6D96;">
                                <use href="#heart"/>
                            </svg>
                            <div style="text-align: center;">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face" style="width: 50px; height: 50px; border-radius: 50%; border: 2px solid #FF6D96;">
                                <div style="font-size: 12px; color: #666; margin-top: 5px;">大宝贝</div>
                            </div>
                        </div>
                        <p style="color: #666; font-size: 14px;">恋爱第 <span style="color: #FF6D96; font-weight: 600;">365</span> 天</p>
                    </div>
                    
                    <!-- 快捷操作 -->
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                        <div class="card" style="text-align: center; padding: 20px;">
                            <svg width="40" height="40" style="color: #FF6D96; margin-bottom: 10px;">
                                <use href="#plus"/>
                            </svg>
                            <div style="font-size: 14px; color: #333;">创建券</div>
                        </div>
                        <div class="card" style="text-align: center; padding: 20px;">
                            <svg width="40" height="40" style="color: #FFD700; margin-bottom: 10px;">
                                <use href="#gift"/>
                            </svg>
                            <div style="font-size: 14px; color: #333;">我的券</div>
                        </div>
                    </div>
                    
                    <!-- 统计信息 -->
                    <div class="card">
                        <h3 style="color: #333; font-size: 16px; margin-bottom: 15px;">本月统计</h3>
                        <div style="display: flex; justify-content: space-between;">
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 600; color: #FF6D96;">12</div>
                                <div style="font-size: 12px; color: #666;">发出的券</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 600; color: #FFD700;">8</div>
                                <div style="font-size: 12px; color: #666;">已兑现</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 20px; font-weight: 600; color: #666;">4</div>
                                <div style="font-size: 12px; color: #666;">待使用</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 底部导航 -->
                <div class="tab-bar">
                    <div class="tab-item active">
                        <svg width="24" height="24"><use href="#heart"/></svg>
                        <span>首页</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#gift"/></svg>
                        <span>我的券</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#bell"/></svg>
                        <span>消息</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#user"/></svg>
                        <span>我的</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 3. 绑定伴侣页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(135deg, #FFE4E1 0%, #fff 50%); padding: 20px;">
                    <div class="page-title">绑定伴侣</div>
                    
                    <div style="text-align: center; margin: 40px 0;">
                        <svg width="100" height="100" style="color: #FF6D96; margin-bottom: 20px;">
                            <use href="#link"/>
                        </svg>
                        <h2 style="color: #333; font-size: 24px; margin-bottom: 10px;">邀请你的另一半</h2>
                        <p style="color: #666; font-size: 14px; line-height: 1.5;">发送邀请码给TA，一起开启<br>甜蜜的信誉券之旅吧</p>
                    </div>
                    
                    <div class="glass-card" style="padding: 30px; margin-bottom: 30px; text-align: center;">
                        <div style="background: #f8f8f8; border-radius: 12px; padding: 20px; margin-bottom: 20px;">
                            <div style="font-size: 24px; font-weight: 600; color: #FF6D96; letter-spacing: 4px;">ABC123</div>
                            <div style="font-size: 12px; color: #666; margin-top: 5px;">邀请码</div>
                        </div>
                        <p style="color: #666; font-size: 12px; margin-bottom: 20px;">有效期：24小时</p>
                        <button class="btn btn-primary" style="width: 100%;">复制邀请码</button>
                    </div>
                    
                    <div style="text-align: center;">
                        <p style="color: #999; font-size: 14px; margin-bottom: 20px;">或者让TA扫描你的专属二维码</p>
                        <div style="width: 120px; height: 120px; background: #f0f0f0; border-radius: 12px; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                            <div style="font-size: 12px; color: #999;">二维码</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 4. 创建信誉券页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="page-content" style="background: #fff; padding: 20px; overflow-y: auto;">
                    <div class="page-title">创建信誉券</div>
                    
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h2 style="color: #333; font-size: 20px;">创建一张爱的券</h2>
                        <p style="color: #666; font-size: 14px;">让承诺变得更有仪式感</p>
                    </div>
                    
                    <!-- 券面预览 -->
                    <div class="glass-card" style="padding: 20px; margin-bottom: 30px; background: linear-gradient(135deg, #FF6D96, #FFE4E1); color: white; text-align: center; position: relative;">
                        <div style="position: absolute; top: 10px; right: 15px; font-size: 12px; opacity: 0.8;">No.001</div>
                        <svg width="40" height="40" style="margin-bottom: 10px;">
                            <use href="#gift" fill="white"/>
                        </svg>
                        <h3 style="font-size: 18px; margin-bottom: 8px;">爱心早餐券</h3>
                        <p style="font-size: 12px; opacity: 0.9;">承诺为你制作一顿温馨早餐</p>
                        <div style="margin-top: 15px; font-size: 12px; opacity: 0.8;">有效期至：2024-02-14</div>
                    </div>
                    
                    <!-- 表单 -->
                    <div style="space-y: 20px;">
                        <div>
                            <label style="display: block; color: #333; font-size: 14px; margin-bottom: 8px;">券名称 *</label>
                            <input type="text" placeholder="例如：爱心早餐券" style="width: 100%; padding: 12px 16px; border: none; border-radius: 12px; background: #f8f8f8; font-size: 16px;">
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <label style="display: block; color: #333; font-size: 14px; margin-bottom: 8px;">券类型</label>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px;">
                                <div style="padding: 10px; background: #FFE4E1; border-radius: 8px; text-align: center; font-size: 12px; color: #FF6D96; border: 2px solid #FF6D96;">服务券</div>
                                <div style="padding: 10px; background: #f8f8f8; border-radius: 8px; text-align: center; font-size: 12px; color: #666;">体验券</div>
                                <div style="padding: 10px; background: #f8f8f8; border-radius: 8px; text-align: center; font-size: 12px; color: #666;">按摩券</div>
                                <div style="padding: 10px; background: #f8f8f8; border-radius: 8px; text-align: center; font-size: 12px; color: #666;">买单券</div>
                                <div style="padding: 10px; background: #f8f8f8; border-radius: 8px; text-align: center; font-size: 12px; color: #666;">奖励券</div>
                                <div style="padding: 10px; background: #f8f8f8; border-radius: 8px; text-align: center; font-size: 12px; color: #666;">自定义</div>
                            </div>
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <label style="display: block; color: #333; font-size: 14px; margin-bottom: 8px;">券描述</label>
                            <textarea placeholder="详细描述券的内容和使用条件..." style="width: 100%; height: 80px; padding: 12px 16px; border: none; border-radius: 12px; background: #f8f8f8; font-size: 14px; resize: none;"></textarea>
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <label style="display: block; color: #333; font-size: 14px; margin-bottom: 8px;">有效期</label>
                            <select style="width: 100%; padding: 12px 16px; border: none; border-radius: 12px; background: #f8f8f8; font-size: 16px;">
                                <option>7天</option>
                                <option>30天</option>
                                <option>永久有效</option>
                                <option>指定日期</option>
                            </select>
                        </div>
                    </div>
                    
                    <button class="btn btn-primary" style="width: 100%; margin-top: 30px; margin-bottom: 20px;">创建并发送</button>
                </div>
            </div>
        </div>
        
        <!-- 5. 我发出的券列表 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="page-content" style="background: #fff; padding: 20px;">
                    <div class="page-title">我发出的券</div>
                    
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2 style="color: #333; font-size: 20px;">我的承诺</h2>
                        <p style="color: #666; font-size: 14px;">已发出 12 张券，兑现 8 张</p>
                    </div>
                    
                    <!-- 筛选标签 -->
                    <div style="display: flex; gap: 10px; margin-bottom: 20px; overflow-x: auto;">
                        <div style="padding: 8px 16px; background: #FF6D96; color: white; border-radius: 20px; font-size: 12px; white-space: nowrap;">全部</div>
                        <div style="padding: 8px 16px; background: #f8f8f8; color: #666; border-radius: 20px; font-size: 12px; white-space: nowrap;">待使用</div>
                        <div style="padding: 8px 16px; background: #f8f8f8; color: #666; border-radius: 20px; font-size: 12px; white-space: nowrap;">已使用</div>
                        <div style="padding: 8px 16px; background: #f8f8f8; color: #666; border-radius: 20px; font-size: 12px; white-space: nowrap;">已过期</div>
                    </div>
                    
                    <!-- 券列表 -->
                    <div style="space-y: 15px;">
                        <!-- 券卡片1 -->
                        <div class="card" style="border-left: 4px solid #FF6D96;">
                            <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                                <div>
                                    <h3 style="color: #333; font-size: 16px; margin-bottom: 5px;">爱心早餐券</h3>
                                    <p style="color: #666; font-size: 12px;">承诺为你制作一顿温馨早餐</p>
                                </div>
                                <span style="background: #FFE4E1; color: #FF6D96; padding: 4px 8px; border-radius: 12px; font-size: 10px;">待使用</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px; color: #999;">
                                <span>发送给：小甜心</span>
                                <span>2024-01-15</span>
                            </div>
                        </div>
                        
                        <!-- 券卡片2 -->
                        <div class="card" style="border-left: 4px solid #FFD700;">
                            <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                                <div>
                                    <h3 style="color: #333; font-size: 16px; margin-bottom: 5px;">按摩服务券</h3>
                                    <p style="color: #666; font-size: 12px;">30分钟专业肩颈按摩</p>
                                </div>
                                <span style="background: #E8F5E8; color: #4CAF50; padding: 4px 8px; border-radius: 12px; font-size: 10px;">已使用</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px; color: #999;">
                                <span>发送给：小甜心</span>
                                <span>2024-01-10</span>
                            </div>
                        </div>
                        
                        <!-- 券卡片3 -->
                        <div class="card" style="border-left: 4px solid #ccc; opacity: 0.7;">
                            <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                                <div>
                                    <h3 style="color: #333; font-size: 16px; margin-bottom: 5px;">电影约会券</h3>
                                    <p style="color: #666; font-size: 12px;">一起看一场浪漫电影</p>
                                </div>
                                <span style="background: #f0f0f0; color: #999; padding: 4px 8px; border-radius: 12px; font-size: 10px;">已过期</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px; color: #999;">
                                <span>发送给：小甜心</span>
                                <span>2023-12-25</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 底部导航 -->
                <div class="tab-bar">
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#heart"/></svg>
                        <span>首页</span>
                    </div>
                    <div class="tab-item active">
                        <svg width="24" height="24"><use href="#gift"/></svg>
                        <span>我的券</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#bell"/></svg>
                        <span>消息</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#user"/></svg>
                        <span>我的</span>
                    </div>
                </div>
            </div>
        </div>
        
                 <!-- 6. 我收到的券列表 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="page-content" style="background: #fff; padding: 20px;">
                    <div class="page-title">我收到的券</div>
                    
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2 style="color: #333; font-size: 20px;">TA的承诺</h2>
                        <p style="color: #666; font-size: 14px;">收到 10 张券，使用 6 张</p>
                    </div>
                    
                    <!-- 新收到的券提醒 -->
                    <div style="background: linear-gradient(135deg, #FFD700, #FFA500); color: white; padding: 15px; border-radius: 12px; margin-bottom: 20px; text-align: center;">
                        <div style="font-size: 14px; font-weight: 600; margin-bottom: 5px;">🎉 收到新券啦！</div>
                        <div style="font-size: 12px; opacity: 0.9;">大宝贝刚刚给你发了一张"游戏时光券"</div>
                    </div>
                    
                    <!-- 券列表 -->
                    <div style="space-y: 15px;">
                        <!-- 新券卡片 -->
                        <div class="card" style="border-left: 4px solid #FFD700; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: -5px; right: -5px; background: #FF6D96; color: white; padding: 2px 8px; border-radius: 0 0 0 8px; font-size: 10px;">NEW</div>
                            <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                                <div>
                                    <h3 style="color: #333; font-size: 16px; margin-bottom: 5px;">游戏时光券</h3>
                                    <p style="color: #666; font-size: 12px;">不限时间畅玩你喜欢的游戏</p>
                                </div>
                                <button style="background: #FF6D96; color: white; border: none; padding: 6px 12px; border-radius: 20px; font-size: 12px;">立即使用</button>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px; color: #999;">
                                <span>来自：大宝贝 💕</span>
                                <span>刚刚</span>
                            </div>
                        </div>
                        
                        <!-- 普通券卡片 -->
                        <div class="card" style="border-left: 4px solid #FF6D96;">
                            <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                                <div>
                                    <h3 style="color: #333; font-size: 16px; margin-bottom: 5px;">家务免做券</h3>
                                    <p style="color: #666; font-size: 12px;">本周家务全包，你只需要美美的</p>
                                </div>
                                <button style="background: #FF6D96; color: white; border: none; padding: 6px 12px; border-radius: 20px; font-size: 12px;">使用</button>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px; color: #999;">
                                <span>来自：大宝贝</span>
                                <span>2024-01-14</span>
                            </div>
                        </div>
                        
                        <!-- 已使用券卡片 -->
                        <div class="card" style="border-left: 4px solid #4CAF50; opacity: 0.8;">
                            <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 10px;">
                                <div>
                                    <h3 style="color: #333; font-size: 16px; margin-bottom: 5px;">浪漫晚餐券</h3>
                                    <p style="color: #666; font-size: 12px;">精心准备的烛光晚餐</p>
                                </div>
                                <span style="background: #E8F5E8; color: #4CAF50; padding: 4px 8px; border-radius: 12px; font-size: 10px;">已使用</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 12px; color: #999;">
                                <span>来自：大宝贝</span>
                                <span>使用于：2024-01-12</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 底部导航 -->
                <div class="tab-bar">
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#heart"/></svg>
                        <span>首页</span>
                    </div>
                    <div class="tab-item active">
                        <svg width="24" height="24"><use href="#gift"/></svg>
                        <span>我的券</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#bell"/></svg>
                        <span>消息</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#user"/></svg>
                        <span>我的</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 7. 券详情页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #FFE4E1 0%, #fff 40%); padding: 20px;">
                    <div class="page-title">券详情</div>
                    
                    <!-- 大号券面展示 -->
                    <div style="margin: 30px 0 40px 0;">
                        <div class="glass-card" style="padding: 30px; background: linear-gradient(135deg, #FF6D96, #FFE4E1); color: white; text-align: center; position: relative; transform: perspective(1000px) rotateX(5deg); box-shadow: 0 20px 40px rgba(255, 109, 150, 0.3);">
                            <div style="position: absolute; top: 15px; right: 20px; font-size: 12px; opacity: 0.8;">No.008</div>
                            <div style="position: absolute; top: 15px; left: 20px; font-size: 10px; opacity: 0.7;">服务券</div>
                            <svg width="60" height="60" style="margin-bottom: 20px;">
                                <use href="#gift" fill="white"/>
                            </svg>
                            <h2 style="font-size: 24px; margin-bottom: 15px; font-weight: 300;">爱心早餐券</h2>
                            <p style="font-size: 14px; opacity: 0.9; line-height: 1.4; margin-bottom: 20px;">承诺为你制作一顿温馨早餐<br>包含你最爱的荷包蛋和小粥</p>
                            <div style="display: flex; justify-content: space-between; font-size: 12px; opacity: 0.8;">
                                <span>有效期至：2024-02-14</span>
                                <span>发券人：大宝贝</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 券信息 -->
                    <div class="card" style="margin-bottom: 20px;">
                        <h3 style="color: #333; font-size: 16px; margin-bottom: 15px;">券信息</h3>
                        <div style="space-y: 12px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                                <span style="color: #666; font-size: 14px;">券类型</span>
                                <span style="color: #333; font-size: 14px;">服务券</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                                <span style="color: #666; font-size: 14px;">发券时间</span>
                                <span style="color: #333; font-size: 14px;">2024-01-15 10:30</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #f0f0f0;">
                                <span style="color: #666; font-size: 14px;">状态</span>
                                <span style="background: #FFE4E1; color: #FF6D96; padding: 4px 8px; border-radius: 12px; font-size: 12px;">待使用</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                <span style="color: #666; font-size: 14px;">发券留言</span>
                                <span style="color: #333; font-size: 14px;">"亲爱的，辛苦了~"</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 使用按钮 -->
                    <button class="btn btn-primary" style="width: 100%; font-size: 18px; padding: 16px 0; margin-bottom: 20px;">
                        <svg width="20" height="20" style="margin-right: 8px; vertical-align: middle;">
                            <use href="#gift"/>
                        </svg>
                        立即使用这张券
                    </button>
                    
                    <p style="text-align: center; color: #999; font-size: 12px;">使用后此券将变为已使用状态</p>
                </div>
            </div>
        </div>
        
        <!-- 8. 消息通知页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="page-content" style="background: #fff; padding: 20px;">
                    <div class="page-title">消息中心</div>
                    
                    <div style="text-align: center; margin-bottom: 20px;">
                        <h2 style="color: #333; font-size: 20px;">消息通知</h2>
                        <p style="color: #666; font-size: 14px;">甜蜜时刻不错过</p>
                    </div>
                    
                    <!-- 消息列表 -->
                    <div style="space-y: 10px;">
                        <!-- 新消息 -->
                        <div class="card" style="border-left: 4px solid #FF6D96; position: relative;">
                            <div style="position: absolute; top: 10px; right: 15px; width: 8px; height: 8px; background: #FF6D96; border-radius: 50%;" class="new-indicator"></div>
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="background: #FFE4E1; border-radius: 50%; padding: 12px; flex-shrink: 0;">
                                    <svg width="20" height="20" style="color: #FF6D96;">
                                        <use href="#gift"/>
                                    </svg>
                                </div>
                                <div style="flex: 1;">
                                    <h3 style="color: #333; font-size: 15px; margin-bottom: 5px;">收到新券</h3>
                                    <p style="color: #666; font-size: 13px;">大宝贝给你发送了"游戏时光券"</p>
                                    <p style="color: #999; font-size: 11px; margin-top: 5px;">刚刚</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 使用提醒 -->
                        <div class="card" style="border-left: 4px solid #FFD700;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="background: #FFF8DC; border-radius: 50%; padding: 12px; flex-shrink: 0;">
                                    <svg width="20" height="20" style="color: #FFD700;">
                                        <use href="#bell"/>
                                    </svg>
                                </div>
                                <div style="flex: 1;">
                                    <h3 style="color: #333; font-size: 15px; margin-bottom: 5px;">券使用提醒</h3>
                                    <p style="color: #666; font-size: 13px;">你有一张"按摩服务券"即将过期</p>
                                    <p style="color: #999; font-size: 11px; margin-top: 5px;">2小时前</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 兑现通知 -->
                        <div class="card" style="border-left: 4px solid #4CAF50;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="background: #E8F5E8; border-radius: 50%; padding: 12px; flex-shrink: 0;">
                                    <svg width="20" height="20" style="color: #4CAF50;">
                                        <use href="#heart"/>
                                    </svg>
                                </div>
                                <div style="flex: 1;">
                                    <h3 style="color: #333; font-size: 15px; margin-bottom: 5px;">券已兑现</h3>
                                    <p style="color: #666; font-size: 13px;">小甜心使用了你的"浪漫晚餐券"</p>
                                    <p style="color: #999; font-size: 11px; margin-top: 5px;">昨天 20:30</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 系统消息 -->
                        <div class="card" style="border-left: 4px solid #999;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="background: #f0f0f0; border-radius: 50%; padding: 12px; flex-shrink: 0;">
                                    <svg width="20" height="20" style="color: #999;">
                                        <use href="#bell"/>
                                    </svg>
                                </div>
                                <div style="flex: 1;">
                                    <h3 style="color: #333; font-size: 15px; margin-bottom: 5px;">恋爱纪念日提醒</h3>
                                    <p style="color: #666; font-size: 13px;">你们的恋爱1周年纪念日快到了</p>
                                    <p style="color: #999; font-size: 11px; margin-top: 5px;">1月10日</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 清空按钮 -->
                    <div style="text-align: center; margin-top: 30px;">
                        <button style="background: transparent; border: 1px solid #ddd; color: #666; padding: 8px 20px; border-radius: 20px; font-size: 14px;">清空所有消息</button>
                    </div>
                </div>
                
                <!-- 底部导航 -->
                <div class="tab-bar">
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#heart"/></svg>
                        <span>首页</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#gift"/></svg>
                        <span>我的券</span>
                    </div>
                    <div class="tab-item active">
                        <svg width="24" height="24"><use href="#bell"/></svg>
                        <span>消息</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#user"/></svg>
                        <span>我的</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 9. 个人中心页面 -->
        <div class="phone-frame">
            <div class="phone-screen">
                <div class="status-bar">
                    <span>9:41</span>
                    <span>100%</span>
                </div>
                <div class="page-content" style="background: linear-gradient(180deg, #FFE4E1 0%, #fff 30%); padding: 20px;">
                    <div class="page-title">个人中心</div>
                    
                    <!-- 用户信息卡片 -->
                    <div class="glass-card" style="padding: 25px; margin-bottom: 25px; text-align: center;">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616c68d82b8?w=80&h=80&fit=crop&crop=face" style="width: 80px; height: 80px; border-radius: 50%; border: 3px solid #FF6D96; margin-bottom: 15px;">
                        <h2 style="color: #333; font-size: 20px; margin-bottom: 8px;">小甜心</h2>
                        <p style="color: #666; font-size: 14px; margin-bottom: 15px;">与大宝贝相恋 365 天</p>
                        <div style="display: flex; justify-content: center; gap: 30px;">
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: 600; color: #FF6D96;">24</div>
                                <div style="font-size: 12px; color: #666;">发出券数</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: 600; color: #FFD700;">18</div>
                                <div style="font-size: 12px; color: #666;">收到券数</div>
                            </div>
                            <div style="text-align: center;">
                                <div style="font-size: 18px; font-weight: 600; color: #4CAF50;">32</div>
                                <div style="font-size: 12px; color: #666;">已兑现</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 功能菜单 -->
                    <div style="space-y: 2px;">
                        <div class="card" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 20px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="background: #FFE4E1; border-radius: 8px; padding: 8px;">
                                    <svg width="20" height="20" style="color: #FF6D96;">
                                        <use href="#user"/>
                                    </svg>
                                </div>
                                <span style="color: #333; font-size: 15px;">个人资料</span>
                            </div>
                            <span style="color: #ccc;">></span>
                        </div>
                        
                        <div class="card" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 20px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="background: #FFF8DC; border-radius: 8px; padding: 8px;">
                                    <svg width="20" height="20" style="color: #FFD700;">
                                        <use href="#link"/>
                                    </svg>
                                </div>
                                <span style="color: #333; font-size: 15px;">伴侣设置</span>
                            </div>
                            <span style="color: #ccc;">></span>
                        </div>
                        
                        <div class="card" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 20px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="background: #E8F5E8; border-radius: 8px; padding: 8px;">
                                    <svg width="20" height="20" style="color: #4CAF50;">
                                        <use href="#gift"/>
                                    </svg>
                                </div>
                                <span style="color: #333; font-size: 15px;">历史记录</span>
                            </div>
                            <span style="color: #ccc;">></span>
                        </div>
                        
                        <div class="card" style="display: flex; justify-content: space-between; align-items: center; padding: 15px 20px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="background: #f0f0f0; border-radius: 8px; padding: 8px;">
                                    <svg width="20" height="20" style="color: #999;">
                                        <use href="#bell"/>
                                    </svg>
                                </div>
                                <span style="color: #333; font-size: 15px;">通知设置</span>
                            </div>
                            <span style="color: #ccc;">></span>
                        </div>
                    </div>
                    
                    <!-- 数据统计图表 -->
                    <div class="card" style="margin-top: 20px;">
                        <h3 style="color: #333; font-size: 16px; margin-bottom: 15px;">本月活跃度</h3>
                                                 <div style="height: 100px; background: #f8f8f8; border-radius: 8px; display: flex; align-items: end; justify-content: space-around; padding: 10px;">
                             <div style="width: 20px; height: 60%; background: linear-gradient(to top, #FF6D96, #FFE4E1); border-radius: 3px;" class="stat-bar"></div>
                             <div style="width: 20px; height: 80%; background: linear-gradient(to top, #FF6D96, #FFE4E1); border-radius: 3px;" class="stat-bar"></div>
                             <div style="width: 20px; height: 40%; background: linear-gradient(to top, #FF6D96, #FFE4E1); border-radius: 3px;" class="stat-bar"></div>
                             <div style="width: 20px; height: 70%; background: linear-gradient(to top, #FF6D96, #FFE4E1); border-radius: 3px;" class="stat-bar"></div>
                             <div style="width: 20px; height: 90%; background: linear-gradient(to top, #FF6D96, #FFE4E1); border-radius: 3px;" class="stat-bar"></div>
                             <div style="width: 20px; height: 50%; background: linear-gradient(to top, #FF6D96, #FFE4E1); border-radius: 3px;" class="stat-bar"></div>
                             <div style="width: 20px; height: 85%; background: linear-gradient(to top, #FF6D96, #FFE4E1); border-radius: 3px;" class="stat-bar"></div>
                         </div>
                        <div style="display: flex; justify-content: space-around; margin-top: 10px; font-size: 11px; color: #999;">
                            <span>周一</span><span>周二</span><span>周三</span><span>周四</span><span>周五</span><span>周六</span><span>周日</span>
                        </div>
                    </div>
                </div>
                
                <!-- 底部导航 -->
                <div class="tab-bar">
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#heart"/></svg>
                        <span>首页</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#gift"/></svg>
                        <span>我的券</span>
                    </div>
                    <div class="tab-item">
                        <svg width="24" height="24"><use href="#bell"/></svg>
                        <span>消息</span>
                    </div>
                    <div class="tab-item active">
                        <svg width="24" height="24"><use href="#user"/></svg>
                        <span>我的</span>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
    <script>
        // 简单的交互动效
        document.querySelectorAll('.card').forEach((card, index) => {
            // 延迟加载动画
            setTimeout(() => {
                card.classList.add('slide-in');
            }, index * 100);
            
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.15)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
            });
        });
        
        // 按钮点击效果
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
                
                // 添加波纹效果
                const ripple = document.createElement('span');
                ripple.style.cssText = `
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255,255,255,0.6);
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    left: 50%;
                    top: 50%;
                    width: 20px;
                    height: 20px;
                    margin: -10px 0 0 -10px;
                `;
                
                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
        
        // 添加波纹动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
        
        // 数据统计条动画触发
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationDelay = Math.random() * 0.5 + 's';
                }
            });
        });
        
        document.querySelectorAll('.stat-bar').forEach(bar => {
            observer.observe(bar);
        });
        
        // 页面滚动视差效果
        let ticking = false;
        
        function updateParallax() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelectorAll('.glass-card');
            const speed = -0.5;
            
            parallax.forEach(element => {
                const yPos = -(scrolled * speed);
                element.style.transform = `translate3d(0, ${yPos}px, 0)`;
            });
            
            ticking = false;
        }
        
        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', requestTick);
        
        // 添加触摸反馈
        document.querySelectorAll('.card, .btn').forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });
            
            element.addEventListener('touchend', function() {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html> 