{"component": true, "componentGenerics": {}, "usingComponents": {}, "componentPlaceholder": {}, "styleIsolation": "isolated", "description": "加载指示器组件，提供多种加载动画效果，支持不同尺寸和显示模式", "version": "1.0.0", "author": "情侣信任券小程序团队", "category": "Feedback Components", "keywords": ["加载", "指示器", "动画", "等待", "反馈"], "repository": {"type": "git", "url": "https://github.com/coupleapp/miniprogram-components"}, "license": "MIT", "properties": {"visible": {"type": "Boolean", "description": "是否显示加载指示器", "default": false, "required": false}, "text": {"type": "String", "description": "加载文本", "default": "加载中...", "required": false}, "type": {"type": "String", "description": "加载器类型", "default": "default", "enum": ["spinner", "dots", "pulse", "wave", "default"], "required": false}, "size": {"type": "String", "description": "尺寸大小", "default": "medium", "enum": ["small", "medium", "large"], "required": false}, "mode": {"type": "String", "description": "显示模式", "default": "inline", "enum": ["inline", "overlay"], "required": false}, "theme": {"type": "String", "description": "主题样式", "default": "primary", "enum": ["primary", "success", "warning", "error"], "required": false}, "state": {"type": "String", "description": "加载状态", "default": "loading", "enum": ["loading", "success", "error"], "required": false}, "autoHide": {"type": "Number", "description": "自动隐藏时间(毫秒)", "default": 0, "required": false}, "delay": {"type": "Number", "description": "延迟显示时间(毫秒)", "default": 0, "required": false}, "minDuration": {"type": "Number", "description": "最小显示时间(毫秒)", "default": 800, "required": false}}, "events": {"show": {"description": "显示时触发", "parameters": {"type": "String - 加载器类型", "timeStamp": "Number - 时间戳"}}, "hide": {"description": "隐藏时触发", "parameters": {"type": "String - 加载器类型", "timeStamp": "Number - 时间戳"}}}, "methods": {"show": {"description": "显示加载指示器", "parameters": {}, "returns": "void"}, "hide": {"description": "隐藏加载指示器", "parameters": {}, "returns": "void"}, "toggle": {"description": "切换显示状态", "parameters": {}, "returns": "void"}, "updateText": {"description": "更新加载文本", "parameters": {"text": "String - 新的加载文本"}, "returns": "void"}, "updateState": {"description": "更新加载状态", "parameters": {"state": "String - 新的状态"}, "returns": "void"}, "setTheme": {"description": "设置主题", "parameters": {"theme": "String - 主题名称"}, "returns": "void"}, "getState": {"description": "获取当前状态", "parameters": {}, "returns": "Object - 当前状态信息"}}, "slots": {"content": {"description": "自定义内容插槽，可替代默认的加载动画和文本"}}, "examples": [{"title": "基础使用", "description": "显示基本的加载指示器", "code": {"wxml": "<loading-indicator visible=\"{{loading}}\" text=\"正在加载数据...\" />", "js": "Page({ data: { loading: true }, onLoad() { setTimeout(() => { this.setData({ loading: false }); }, 2000); } })"}}, {"title": "不同类型", "description": "展示不同类型的加载动画", "code": {"wxml": "<loading-indicator visible=\"{{true}}\" type=\"spinner\" text=\"旋转加载\" />\n<loading-indicator visible=\"{{true}}\" type=\"dots\" text=\"点动画\" />\n<loading-indicator visible=\"{{true}}\" type=\"pulse\" text=\"脉冲\" />\n<loading-indicator visible=\"{{true}}\" type=\"wave\" text=\"波纹\" />"}}, {"title": "覆盖层模式", "description": "全屏覆盖显示加载指示器", "code": {"wxml": "<loading-indicator visible=\"{{loading}}\" mode=\"overlay\" text=\"正在处理中...\" auto-hide=\"{{3000}}\" />"}}, {"title": "不同尺寸和主题", "description": "展示不同尺寸和主题的加载器", "code": {"wxml": "<loading-indicator visible=\"{{true}}\" size=\"small\" theme=\"success\" text=\"小尺寸\" />\n<loading-indicator visible=\"{{true}}\" size=\"large\" theme=\"warning\" text=\"大尺寸\" />"}}, {"title": "自定义内容", "description": "使用插槽自定义加载内容", "code": {"wxml": "<loading-indicator visible=\"{{true}}\"><view slot=\"content\"><image src=\"custom-loading.gif\" /><text>自定义加载动画</text></view></loading-indicator>"}}], "changelog": [{"version": "1.0.0", "date": "2024-01-01", "changes": ["初始版本发布", "支持多种加载动画类型", "支持不同尺寸和主题", "支持两种显示模式", "支持自动隐藏和延迟显示", "支持最小显示时间控制", "支持自定义内容插槽", "支持响应式设计", "支持深色模式", "支持无障碍功能"]}]}