<!--
  自定义模态框组件 WXML
  支持标题、内容展示、确认/取消按钮，可自定义按钮文本和回调函数
-->
<wxs src="./customModal.wxs" module="utils"></wxs>

<view class="custom-modal {{visible ? 'show' : 'hide'}}" wx:if="{{visible}}" 
      catch:tap="onMaskTap" catch:touchmove="prevent">
  <view class="modal-content" catch:tap="prevent">
    
    <!-- 模态框头部 -->
    <view class="modal-header" wx:if="{{title}}">
      <view class="modal-title">{{title}}</view>
      <view class="modal-close" wx:if="{{showCloseIcon}}" bind:tap="onCancel">
        <text class="close-icon">✕</text>
      </view>
    </view>
    
    <!-- 模态框内容 -->
    <view class="modal-body">
      <view class="modal-content-text" wx:if="{{content}}">{{content}}</view>
      
      <!-- 插槽内容 -->
      <slot name="content"></slot>
    </view>
    
    <!-- 模态框底部按钮 -->
    <view class="modal-footer" wx:if="{{showActions}}">
      <button class="modal-btn cancel-btn" 
              wx:if="{{showCancel}}" 
              bind:tap="onCancel"
              hover-class="btn-hover">
        {{cancelText}}
      </button>
      <button class="modal-btn confirm-btn" 
              bind:tap="onConfirm"
              hover-class="btn-hover">
        {{confirmText}}
      </button>
    </view>
    
  </view>
</view>

<!-- 自定义样式 -->
<style>
.custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  transition: opacity 300ms ease;
}

.custom-modal.show {
  opacity: 1;
  animation: fadeIn 300ms ease;
}

.custom-modal.hide {
  opacity: 0;
  animation: fadeOut 300ms ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.modal-content {
  background: white;
  border-radius: 32rpx;
  max-width: 600rpx;
  width: 90vw;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  transform: scale(0.95);
  opacity: 0;
  animation: modalShow 300ms ease forwards 100ms;
  overflow: hidden;
}

@keyframes modalShow {
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.modal-header {
  position: relative;
  padding: 48rpx 48rpx 0;
  text-align: center;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
}

.modal-close {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f3f4f6;
  transition: background 200ms ease;
}

.modal-close:active {
  background: #e5e7eb;
  transform: scale(0.95);
}

.close-icon {
  font-size: 32rpx;
  color: #6b7280;
}

.modal-body {
  padding: 24rpx 48rpx 48rpx;
  text-align: center;
}

.modal-content-text {
  font-size: 30rpx;
  color: #6b7280;
  line-height: 1.6;
}

.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 0 48rpx 48rpx;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 200ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-btn::after {
  border: none;
}

.cancel-btn {
  background: #f3f4f6;
  color: #374151;
}

.confirm-btn {
  background: linear-gradient(135deg, #e91e63 0%, #f48fb1 100%);
  color: white;
}

.btn-hover {
  transform: scale(0.98);
  opacity: 0.9;
}

/* 单按钮样式 */
.modal-footer .modal-btn:only-child {
  background: linear-gradient(135deg, #e91e63 0%, #f48fb1 100%);
  color: white;
}

/* 适配深色模式 */
@media (prefers-color-scheme: dark) {
  .modal-content {
    background: #1f2937;
  }
  
  .modal-title {
    color: #f9fafb;
  }
  
  .modal-content-text {
    color: #d1d5db;
  }
  
  .modal-close {
    background: #374151;
  }
  
  .close-icon {
    color: #9ca3af;
  }
  
  .cancel-btn {
    background: #374151;
    color: #d1d5db;
  }
}
</style>
