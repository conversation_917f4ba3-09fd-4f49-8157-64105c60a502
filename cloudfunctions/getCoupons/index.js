/**
 * 获取券列表
 * 获取用户相关的信任券
 */
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  const openid = wxContext.OPENID;
  
  console.log('获取券列表, openid:', openid, 'params:', event);
  
  try {
    // 首先查找用户信息
    const userResult = await db.collection('users').where({
      _openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    const user = userResult.data[0];
    console.log('用户信息:', {
      _id: user._id,
      _openid: user._openid,
      nickName: user.nickName
    });
    
    // 构建查询条件 - 修复：使用正确的字段名
    const queryCondition = {
      $or: [
        { creatorId: openid }, // 我创建的券
        { recipientId: openid } // 我收到的券
      ],
      // 默认过滤掉失效和草稿状态的券
      status: db.command.nin(['invalid', 'draft'])
    };
    
    // 如果指定了状态过滤
    if (event.status) {
      if (Array.isArray(event.status)) {
        queryCondition.status = db.command.in(event.status);
      } else {
        queryCondition.status = event.status;
      }
    }
    
    // 如果明确要求获取所有状态（包括失效的）
    if (event.includeInvalid === true) {
      delete queryCondition.status;
    }
    
    // 如果指定了过滤类型
    if (event.filterType) {
      switch (event.filterType) {
        case 'sent':
          // 只获取我发送的券
          queryCondition.$or = [{ creatorId: openid }];
          break;
        case 'received':
          // 只获取我收到的券
          queryCondition.$or = [{ recipientId: openid }];
          break;
        // 其他情况保持原有的$or条件
      }
    }
    
    console.log('查询条件:', JSON.stringify(queryCondition));
    
    // 查询券列表
    let query = db.collection('coupons').where(queryCondition);
    
    // 排序：创建时间倒序
    query = query.orderBy('createTime', 'desc');
    
    // 分页
    const limit = event.limit || 20;
    const skip = event.skip || 0;
    
    query = query.limit(limit).skip(skip);
    
    const couponsResult = await query.get();
    console.log('查询到券数量:', couponsResult.data.length);
    
    // 获取相关用户信息
    const userIds = new Set();
    couponsResult.data.forEach(coupon => {
      if (coupon.creatorId) userIds.add(coupon.creatorId);
      if (coupon.recipientId) userIds.add(coupon.recipientId);
    });
    
    let usersMap = {};
    if (userIds.size > 0) {
      const usersResult = await db.collection('users').where({
        _openid: db.command.in([...userIds])
      }).get();
      
      usersResult.data.forEach(user => {
        usersMap[user._openid] = user;
      });
    }
    
    // 组装返回数据 - 修复：正确映射用户信息
    const couponsWithUserInfo = couponsResult.data.map(coupon => {
      const creator = usersMap[coupon.creatorId];
      const recipient = usersMap[coupon.recipientId];
      
      return {
        ...coupon,
        // 保持向后兼容
        createdBy: coupon.creatorId, // 兼容旧字段名
        title: coupon.name,           // 添加：向后兼容 title 字段
        expiresAt: coupon.expiryDate, // 添加：向后兼容 expiresAt 字段
        creator: creator ? {
          _id: creator._id,
          _openid: creator._openid,
          nickName: creator.nickName,
          avatarUrl: creator.avatarUrl
        } : null,
        recipient: recipient ? {
          _id: recipient._id,
          _openid: recipient._openid,
          nickName: recipient.nickName,
          avatarUrl: recipient.avatarUrl
        } : null,
        // 添加便于前端使用的字段
        creatorNickname: creator?.nickName || '未知用户',
        creatorAvatar: creator?.avatarUrl || '/images/default-avatar.png',
        recipientNickname: recipient?.nickName || '未知用户',
        recipientAvatar: recipient?.avatarUrl || '/images/default-avatar.png'
      };
    });
    
    console.log('返回券数据:', couponsWithUserInfo.map(c => ({
      _id: c._id,
      name: c.name,        // 修复：使用 name 而不是 title
      status: c.status,
      creatorId: c.creatorId,
      recipientId: c.recipientId
    })));
    
    return {
      success: true,
      message: '获取券列表成功',
      data: {
        coupons: couponsWithUserInfo,
        total: couponsResult.data.length,
        hasMore: couponsResult.data.length === limit
      }
    };
    
  } catch (error) {
    console.error('获取券列表失败:', error);
    return {
      success: false,
      message: '获取券列表失败: ' + error.message,
      error: error.message
    };
  }
}; 