/**
 * 加载指示器组件样式
 * 提供多种加载动画效果和尺寸选项
 */

/* 主容器 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  text-align: center;
  position: relative;
  min-height: 160rpx;
}

/* 覆盖层模式 */
.loading-indicator.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8rpx);
  z-index: 9999;
  min-height: 100vh;
}

/* 内联模式 */
.loading-indicator.inline {
  position: relative;
  min-height: auto;
  padding: 32rpx 16rpx;
}

/* 动画容器 */
.loading-animation {
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 旋转圆环加载器 */
.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  position: relative;
}

.loading-spinner.small { 
  width: 56rpx; 
  height: 56rpx; 
}

.loading-spinner.medium { 
  width: 80rpx; 
  height: 80rpx; 
}

.loading-spinner.large { 
  width: 112rpx; 
  height: 112rpx; 
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 6rpx solid rgba(233, 30, 99, 0.15);
  border-top: 6rpx solid #e91e63;
  border-radius: 50%;
  animation: spinner-rotate 1.2s linear infinite;
  position: relative;
}

.loading-spinner.small .spinner-ring {
  border-width: 4rpx;
}

.loading-spinner.large .spinner-ring {
  border-width: 8rpx;
}

/* 圆环闪光效果 */
.spinner-ring::before {
  content: '';
  position: absolute;
  top: -6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 12rpx;
  height: 12rpx;
  background: #e91e63;
  border-radius: 50%;
  box-shadow: 0 0 20rpx rgba(233, 30, 99, 0.6);
}

@keyframes spinner-rotate {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}

/* 点状加载器 */
.loading-dots {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #e91e63;
  animation: dots-bounce 1.4s ease-in-out infinite both;
  box-shadow: 0 0 8rpx rgba(233, 30, 99, 0.3);
}

.loading-indicator.small .dot {
  width: 12rpx;
  height: 12rpx;
}

.loading-indicator.large .dot {
  width: 20rpx;
  height: 20rpx;
}

.dot-1 { 
  animation-delay: -0.32s;
  background: linear-gradient(45deg, #e91e63, #f48fb1);
}

.dot-2 { 
  animation-delay: -0.16s;
  background: linear-gradient(45deg, #f48fb1, #e91e63);
}

.dot-3 { 
  animation-delay: 0s;
  background: linear-gradient(45deg, #e91e63, #ad1457);
}

@keyframes dots-bounce {
  0%, 80%, 100% {
    transform: scale(0.3) translateY(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1) translateY(-20rpx);
    opacity: 1;
  }
}

/* 脉冲加载器 */
.loading-pulse {
  width: 80rpx;
  height: 80rpx;
  position: relative;
}

.loading-indicator.small .loading-pulse {
  width: 56rpx;
  height: 56rpx;
}

.loading-indicator.large .loading-pulse {
  width: 112rpx;
  height: 112rpx;
}

.pulse-circle {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(233, 30, 99, 0.8), rgba(233, 30, 99, 0.2));
  animation: pulse-scale 1.5s ease-in-out infinite;
  position: relative;
}

.pulse-circle::before {
  content: '';
  position: absolute;
  top: 25%;
  left: 25%;
  width: 50%;
  height: 50%;
  background: #e91e63;
  border-radius: 50%;
  animation: pulse-inner 1.5s ease-in-out infinite;
}

@keyframes pulse-scale {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.4;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

@keyframes pulse-inner {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

/* 波纹加载器 */
.loading-wave {
  display: flex;
  gap: 8rpx;
  align-items: center;
  height: 80rpx;
}

.loading-indicator.small .loading-wave {
  height: 56rpx;
}

.loading-indicator.large .loading-wave {
  height: 112rpx;
}

.wave-bar {
  width: 8rpx;
  background: linear-gradient(180deg, #e91e63 0%, #f48fb1 100%);
  border-radius: 4rpx;
  animation: wave-stretch 1.2s ease-in-out infinite;
  box-shadow: 0 0 8rpx rgba(233, 30, 99, 0.3);
}

.loading-indicator.small .wave-bar {
  width: 6rpx;
  border-radius: 3rpx;
}

.loading-indicator.large .wave-bar {
  width: 10rpx;
  border-radius: 5rpx;
}

.bar-1 { 
  animation-delay: -1.2s;
  background: linear-gradient(180deg, #e91e63 0%, #ad1457 100%);
}

.bar-2 { 
  animation-delay: -1.0s;
  background: linear-gradient(180deg, #f48fb1 0%, #e91e63 100%);
}

.bar-3 { 
  animation-delay: -0.8s;
  background: linear-gradient(180deg, #e91e63 0%, #f48fb1 100%);
}

.bar-4 { 
  animation-delay: -0.6s;
  background: linear-gradient(180deg, #f48fb1 0%, #e91e63 100%);
}

.bar-5 { 
  animation-delay: -0.4s;
  background: linear-gradient(180deg, #ad1457 0%, #e91e63 100%);
}

@keyframes wave-stretch {
  0%, 40%, 100% {
    height: 20rpx;
    opacity: 0.6;
  }
  20% {
    height: 100%;
    opacity: 1;
  }
}

/* 默认旋转加载器 */
.loading-default {
  width: 80rpx;
  height: 80rpx;
  position: relative;
}

.loading-indicator
.small .loading-default {
  width: 56rpx;
  height: 56rpx;
}

.loading-indicator.large .loading-default {
  width: 112rpx;
  height: 112rpx;
}

.default-ring {
  width: 100%;
  height: 100%;
  border: 6rpx solid #f1f5f9;
  border-top: 6rpx solid #e91e63;
  border-radius: 50%;
  animation: default-spin 1s linear infinite;
  position: relative;
}

.loading-indicator.small .default-ring {
  border-width: 4rpx;
}

.loading-indicator.large .default-ring {
  border-width: 8rpx;
}

/* 默认加载器光晕效果 */
.default-ring::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(233, 30, 99, 0.1), transparent 70%);
}

@keyframes default-spin {
  0% { 
    transform: rotate(0deg); 
  }
  100% { 
    transform: rotate(360deg); 
  }
}

/* 加载文本 */
.loading-text {
  margin-top: 24rpx;
  text-align: center;
}

.text-content {
  font-size: 30rpx;
  color: #64748b;
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 0.5rpx;
  animation: text-fade 2s ease-in-out infinite;
}

@keyframes text-fade {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

/* 尺寸变体文本调整 */
.loading-indicator.small .loading-animation {
  margin-bottom: 20rpx;
}

.loading-indicator.small .text-content {
  font-size: 26rpx;
}

.loading-indicator.large .loading-animation {
  margin-bottom: 40rpx;
}

.loading-indicator.large .text-content {
  font-size: 34rpx;
}

/* 主题变体 */
.loading-indicator.theme-primary {
  background: rgba(233, 30, 99, 0.05);
  border-radius: 24rpx;
}

.loading-indicator.theme-success .spinner-ring,
.loading-indicator.theme-success .default-ring {
  border-top-color: #10b981;
}

.loading-indicator.theme-success .dot,
.loading-indicator.theme-success .pulse-circle {
  background: #10b981;
}

.loading-indicator.theme-warning .spinner-ring,
.loading-indicator.theme-warning .default-ring {
  border-top-color: #f59e0b;
}

.loading-indicator.theme-warning .dot,
.loading-indicator.theme-warning .pulse-circle {
  background: #f59e0b;
}

/* 响应式适配 */
@media screen and (max-width: 480rpx) {
  .loading-indicator {
    padding: 32rpx 20rpx;
  }
  
  .loading-spinner,
  .loading-pulse,
  .loading-default {
    width: 64rpx;
    height: 64rpx;
  }
  
  .loading-wave {
    height: 64rpx;
  }
  
  .text-content {
    font-size: 28rpx;
  }
  
  .loading-animation {
    margin-bottom: 20rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .loading-indicator.overlay {
    background: rgba(15, 23, 42, 0.95);
  }
  
  .loading-indicator.theme-primary {
    background: rgba(244, 143, 177, 0.08);
  }
  
  .spinner-ring {
    border-color: rgba(244, 143, 177, 0.2);
    border-top-color: #f48fb1;
  }
  
  .spinner-ring::before {
    background: #f48fb1;
    box-shadow: 0 0 20rpx rgba(244, 143, 177, 0.6);
  }
  
  .default-ring {
    border-color: #334155;
    border-top-color: #f48fb1;
  }
  
  .dot {
    background: #f48fb1;
    box-shadow: 0 0 8rpx rgba(244, 143, 177, 0.4);
  }
  
  .dot-1 {
    background: linear-gradient(45deg, #f48fb1, #fce7f3);
  }
  
  .dot-2 {
    background: linear-gradient(45deg, #fce7f3, #f48fb1);
  }
  
  .dot-3 {
    background: linear-gradient(45deg, #f48fb1, #ec4899);
  }
  
  .pulse-circle {
    background: radial-gradient(circle, rgba(244, 143, 177, 0.8), rgba(244, 143, 177, 0.2));
  }
  
  .pulse-circle::before {
    background: #f48fb1;
  }
  
  .wave-bar {
    background: linear-gradient(180deg, #f48fb1 0%, #fce7f3 100%);
    box-shadow: 0 0 8rpx rgba(244, 143, 177, 0.3);
  }
  
  .bar-1 {
    background: linear-gradient(180deg, #f48fb1 0%, #ec4899 100%);
  }
  
  .bar-2 {
    background: linear-gradient(180deg, #fce7f3 0%, #f48fb1 100%);
  }
  
  .bar-3 {
    background: linear-gradient(180deg, #f48fb1 0%, #fce7f3 100%);
  }
  
  .bar-4 {
    background: linear-gradient(180deg, #fce7f3 0%, #f48fb1 100%);
  }
  
  .bar-5 {
    background: linear-gradient(180deg, #ec4899 0%, #f48fb1 100%);
  }
  
  .text-content {
    color: #cbd5e1;
  }
  
  .default-ring::after {
    background: radial-gradient(circle, rgba(244, 143, 177, 0.1), transparent 70%);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .spinner-ring,
  .default-ring {
    border-top-color: #000;
  }
  
  .dot,
  .pulse-circle {
    background: #000;
  }
  
  .wave-bar {
    background: #000;
  }
  
  .text-content {
    color: #000;
    font-weight: 600;
  }
}

/* 无障碍支持 - 减少动画 */
@media (prefers-reduced-motion: reduce) {
  .spinner-ring,
  .default-ring,
  .dot,
  .pulse-circle,
  .wave-bar {
    animation: none;
  }
  
  .text-content {
    animation: none;
    opacity: 1;
  }
  
  .loading-indicator::after {
    content: '正在加载...';
    position: absolute;
    bottom: 20rpx;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24rpx;
    color: #64748b;
    font-weight: 500;
  }
  
  .loading-indicator.overlay::after {
    bottom: 40%;
  }
}

/* 打印样式 */
@media print {
  .loading-indicator {
    display: none;
  }
}

/* 自定义加载状态样式 */
.loading-indicator.state-error .spinner-ring,
.loading-indicator.state-error .default-ring {
  border-top-color: #ef4444;
}

.loading-indicator.state-error .dot,
.loading-indicator.state-error .pulse-circle {
  background: #ef4444;
}

.loading-indicator.state-success .spinner-ring,
.loading-indicator.state-success .default-ring {
  border-top-color: #10b981;
}

.loading-indicator.state-success .dot,
.loading-indicator.state-success .pulse-circle {
  background: #10b981;
}
