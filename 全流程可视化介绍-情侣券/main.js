// Main JavaScript file for handling document behavior

document.addEventListener('DOMContentLoaded', function() {
    // Highlight active section in navigation based on scroll position
    const sections = document.querySelectorAll('section.section-content');
    const navItems = document.querySelectorAll('.nav-item');
    
    // Initialize content loading
    loadAllSectionContent();
    
    // Update active navigation item on scroll
    window.addEventListener('scroll', function() {
        let currentSection = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            
            if (pageYOffset >= (sectionTop - 200)) {
                currentSection = section.getAttribute('id');
            }
        });
        
        navItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('data-section') === currentSection) {
                item.classList.add('active');
            }
        });
    });
    
    // Handle navigation click events
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            const targetSection = this.getAttribute('data-section');
            if (targetSection) {
                e.preventDefault();
                document.getElementById(targetSection).scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
});

// Function to load all section content
function loadAllSectionContent() {
    // Load feasibility study
    const feasibilityContent = document.getElementById('feasibility-content');
    if (feasibilityContent) {
        feasibilityContent.innerHTML = renderFeasibilityStudy();
    }
    
    // Load requirements (PRD)
    const requirementsContent = document.getElementById('requirements-content');
    if (requirementsContent) {
        requirementsContent.innerHTML = renderRequirements();
    }
    
    // Load user journey
    const userJourneyContent = document.getElementById('user-journey-content');
    if (userJourneyContent) {
        userJourneyContent.innerHTML = renderUserJourney();
    }
    
    // Load business process
    const businessProcessContent = document.getElementById('business-process-content');
    if (businessProcessContent) {
        businessProcessContent.innerHTML = renderBusinessProcess();
    }
    
    // Load technology
    const technologyContent = document.getElementById('technology-content');
    if (technologyContent) {
        technologyContent.innerHTML = renderTechnologySelection();
    }
    
    // Load interaction logic
    const interactionContent = document.getElementById('interaction-content');
    if (interactionContent) {
        interactionContent.innerHTML = renderInteractionLogic();
    }
    
    // Load UI/UX
    const uiuxContent = document.getElementById('uiux-content');
    if (uiuxContent) {
        uiuxContent.innerHTML = renderUIUXSuggestions();
    }
}
