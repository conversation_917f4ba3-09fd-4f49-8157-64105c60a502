/**
 * API请求封装工具
 * 封装 wx.cloud.callFunction，统一处理请求loading状态、错误提示、登录态失效等逻辑
 */

// 云函数请求封装
async function cloudRequest(functionName, data = {}, options = {}) {
    const {
        showLoading = true,
        loadingText = '加载中...',
        showError = true,
        timeout = 10000
    } = options;

    // 显示加载状态
    if (showLoading) {
        showLoadingToast(loadingText);
    }

    try {
        // 模拟云函数调用 (在实际小程序中使用 wx.cloud.callFunction)
        const result = await mockCloudFunction(functionName, data);
        
        // 隐藏加载状态
        if (showLoading) {
            hideLoadingToast();
        }

        // 检查返回结果
        if (result.code !== 0) {
            throw new Error(result.message || '请求失败');
        }

        return {
            success: true,
            data: result.data,
            message: result.message
        };

    } catch (error) {
        // 隐藏加载状态
        if (showLoading) {
            hideLoadingToast();
        }

        // 显示错误提示
        if (showError) {
            showErrorToast(error.message || '网络请求失败');
        }

        // 处理登录态失效
        if (error.code === 'AUTH_FAILED') {
            handleAuthFailed();
        }

        return {
            success: false,
            error: error.message || '请求失败',
            code: error.code
        };
    }
}

// 模拟云函数调用 (实际开发中替换为真实的云函数调用)
function mockCloudFunction(name, data) {
    return new Promise((resolve) => {
        setTimeout(() => {
            switch (name) {
                case 'getUserInfo':
                    resolve({
                        code: 0,
                        data: {
                            openid: 'mock_openid_123',
                            nickName: '小甜心',
                            avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
                            coupleId: 'couple_123'
                        },
                        message: '获取用户信息成功'
                    });
                    break;
                
                case 'createCoupon':
                    resolve({
                        code: 0,
                        data: {
                            couponId: 'coupon_' + Date.now(),
                            createdAt: new Date().toISOString()
                        },
                        message: '券创建成功'
                    });
                    break;
                
                case 'sendCoupon':
                    resolve({
                        code: 0,
                        data: {
                            sendTime: new Date().toISOString()
                        },
                        message: '券发送成功'
                    });
                    break;
                
                default:
                    resolve({
                        code: 0,
                        data: {},
                        message: '操作成功'
                    });
            }
        }, Math.random() * 1000 + 500); // 模拟网络延迟
    });
}

// 显示加载提示
function showLoadingToast(text = '加载中...') {
    // 在实际小程序中使用 wx.showLoading
    console.log('Loading:', text);
    
    // 创建加载提示DOM (用于演示)
    const loadingEl = document.createElement('div');
    loadingEl.id = 'api-loading';
    loadingEl.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg z-50';
    loadingEl.textContent = text;
    document.body.appendChild(loadingEl);
}

// 隐藏加载提示
function hideLoadingToast() {
    // 在实际小程序中使用 wx.hideLoading
    console.log('Hide loading');
    
    // 移除加载提示DOM (用于演示)
    const loadingEl = document.getElementById('api-loading');
    if (loadingEl) {
        loadingEl.remove();
    }
}

// 显示错误提示
function showErrorToast(message) {
    // 在实际小程序中使用 wx.showToast
    console.error('Error:', message);
    
    // 创建错误提示DOM (用于演示)
    const errorEl = document.createElement('div');
    errorEl.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-4 py-2 rounded-lg z-50';
    errorEl.textContent = message;
    document.body.appendChild(errorEl);
    
    setTimeout(() => {
        errorEl.remove();
    }, 3000);
}

// 处理登录态失效
function handleAuthFailed() {
    console.log('Auth failed, need re-login');
    // 在实际小程序中跳转到登录页面或重新登录
    showErrorToast('登录已过期，请重新登录');
}

// 常用API封装

// 获取用户信息
async function getUserInfo() {
    return await cloudRequest('getUserInfo');
}

// 创建信任券
async function createCoupon(couponData) {
    return await cloudRequest('createCoupon', couponData);
}

// 发送信任券
async function sendCoupon(couponId, receiverId) {
    return await cloudRequest('sendCoupon', { couponId, receiverId });
}

// 接收信任券
async function receiveCoupon(couponId) {
    return await cloudRequest('receiveCoupon', { couponId });
}

// 申请核销信任券
async function requestRedeemCoupon(couponId) {
    return await cloudRequest('requestRedeemCoupon', { couponId });
}

// 确认核销信任券
async function confirmRedeemCoupon(couponId) {
    return await cloudRequest('confirmRedeemCoupon', { couponId });
}

// 获取我的券列表
async function getMyCoupons(type = 'all') {
    return await cloudRequest('getMyCoupons', { type });
}

// 获取通知列表
async function getNotifications() {
    return await cloudRequest('getNotifications');
}

// 情侣绑定
async function bindCouple(inviteCode) {
    return await cloudRequest('bindCouple', { inviteCode });
}

// 解除情侣绑定
async function unbindCouple() {
    return await cloudRequest('unbindCouple');
}
