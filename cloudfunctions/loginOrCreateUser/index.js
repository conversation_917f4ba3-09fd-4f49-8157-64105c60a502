/**
 * 用户登录或创建云函数
 * 支持自动分配随机昵称和头像，实现一步登录
 */

const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event 云函数调用参数
 * @param {Object} event.autoProfile 自动生成的用户资料 {nickName, avatarUrl}
 * @param {Object} context 云函数运行上下文
 */
exports.main = async (event, context) => {
  console.log('用户登录/创建云函数开始执行');
  
  try {
    // 获取微信调用上下文
    const wxContext = cloud.getWXContext();
    const { OPENID } = wxContext;
    
    if (!OPENID) {
      throw new Error('无法获取用户 OpenID');
    }

    console.log('用户 OpenID:', OPENID);

    // 查询用户是否已存在
    const userQuery = await db.collection('users').where({
      _openid: OPENID
    }).get();

    let userInfo;
    let isNewUser = false;

    if (userQuery.data.length > 0) {
      // 用户已存在，返回现有用户信息
      userInfo = userQuery.data[0];
      console.log('用户已存在，返回现有信息:', userInfo._id);
    } else {
      // 新用户，创建用户记录
      console.log('新用户，开始创建用户记录');
      isNewUser = true;
      
      const currentTime = new Date();
      const { autoProfile } = event;
      
      // 默认个性签名列表
      const defaultSignatures = [
        "爱是最美的语言 💕",
        "有你的日子都是晴天 ☀️",
        "心有所属，爱有所归 💖",
        "陪你看遍世间风景 🌸",
        "余生请多指教 💝",
        "简单的爱，纯真的心 🌹",
        "一生一世一双人 💑",
        "愿得一人心，白首不分离 💍",
        "你是我的小确幸 🍀",
        "平凡日子里的不平凡 ✨"
      ];
      
      // 随机选择一个个性签名
      const randomSignature = defaultSignatures[Math.floor(Math.random() * defaultSignatures.length)];
      
      // 使用自动生成的资料或默认值
      const newUserData = {
        _openid: OPENID,
        nickName: autoProfile?.nickName || '情侣用户',
        avatarUrl: autoProfile?.avatarUrl || '/images/default-avatar.png',
        signature: randomSignature, // 添加随机个性签名
        
        // 基础信息
        gender: 0, // 0:未知
        city: '',
        province: '',
        country: '',
        
        // 关系信息
        partnerId: null,
        relationshipStatus: 'single',
        
        // 统计信息
        stats: {
          couponsCreated: 0,
          couponsReceived: 0,
          couponsRedeemed: 0,
          totalLovePoints: 0
        },
        
        // 设置信息
        settings: {
          notificationEnabled: true,
          soundEnabled: true,
          vibrationEnabled: true,
          privacyLevel: 'couple_only'
        },
        
        // 时间信息
        createTime: currentTime,
        updateTime: currentTime,
        lastLoginTime: currentTime,
        
        // 状态标记
        isProfileComplete: false, // 标记资料是否完善
        isAutoGenerated: true     // 标记是否为自动生成的资料
      };

      // 创建新用户
      const createResult = await db.collection('users').add({
        data: newUserData
      });

      if (!createResult._id) {
        throw new Error('用户创建失败');
      }

      // 获取完整的用户信息
      const newUserQuery = await db.collection('users').doc(createResult._id).get();
      userInfo = newUserQuery.data;
      
      console.log('新用户创建成功:', userInfo._id);
    }

    // 更新最后登录时间（对于现有用户）
    if (!isNewUser) {
      await db.collection('users').doc(userInfo._id).update({
        data: {
          lastLoginTime: new Date(),
          updateTime: new Date()
        }
      });
    }

    // 返回用户信息，添加 isNewUser 标记
    const responseUserInfo = {
      ...userInfo,
      isNewUser: isNewUser
    };

    console.log('登录成功，返回用户信息');

    return {
      success: true,
      message: isNewUser ? '注册成功' : '登录成功',
      data: {
        userInfo: responseUserInfo,
        isNewUser: isNewUser
      }
    };

  } catch (error) {
    console.error('用户登录/创建云函数执行失败:', error);
    
    return {
      success: false,
      message: '登录失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}; 