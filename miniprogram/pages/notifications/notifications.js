// notifications.js
const app = getApp();

Page({
  data: {
    notifications: [],
    filteredNotifications: [],
    currentFilter: "all",
    currentSubFilter: null, // 当前选中的二级筛选
    filterTabs: [
      { type: "all", label: "全部", count: 0 },
      { type: "new_coupon", label: "新券提醒", count: 0 },
      { type: "profile_updated", label: "资料更新", count: 0 },
      { 
        type: "redeem_request", 
        label: "兑现申请", 
        count: 0,
        hasSubTabs: true,
        subTabs: [
          { type: "redeem_request_my", label: "我申请的", count: 0 },
          { type: "redeem_request_received", label: "我接收的", count: 0 }
        ]
      },
      { 
        type: "redeem_result", 
        label: "兑现完成", 
        count: 0,
        hasSubTabs: true,
        subTabs: [
          { type: "redeem_result_my", label: "我申请的", count: 0 },
          { type: "redeem_result_processed", label: "我兑现的", count: 0 }
        ]
      },
      { type: "expiry", label: "过期提醒", count: 0 },
    ],
    totalCount: 0,
    unreadCount: 0,
    isLoading: false,
    isRefreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    loadingText: "加载中...",
    currentFilterLabel: "全部",
    currentMainTab: null, // 当前主tab对象
    themeGradient: app.globalData.gradientBackground,
  },

  onLoad: function () {
    console.log("消息通知页面加载");
    this.setData({
      themeGradient: app.globalData.gradientBackground,
    });

    // 添加全局刷新事件监听器 - 修复问题2：确保解绑后通知页面也能正确刷新
    this.globalRefreshHandler = (data) => {
      console.log("通知页面接收到全局刷新事件:", data);
      if (data.reason === 'couple_unbound') {
        console.log('检测到解绑事件，清空通知数据');
        this.setData({
          notifications: [],
          filteredNotifications: [],
          totalCount: 0,
          unreadCount: 0,
          hasMore: false
        });
        this.updateFilterCounts([]);
      } else {
        this.refreshNotifications();
      }
    };
    app.addEventListener('globalDataRefresh', this.globalRefreshHandler);
  },

  onShow: function () {
    console.log("消息通知页面显示");
    
    // 注意：不在这里清除徽章，只有进入详情页或全部已读才清除
    // 每次显示时刷新数据
    this.refreshNotifications();
  },

  onPullDownRefresh: function () {
    console.log("下拉刷新");
    this.setData({ isRefreshing: true });
    this.refreshNotifications().then(() => {
      this.setData({ isRefreshing: false });
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom: function () {
    console.log("触底加载更多");
    this.loadMore();
  },

  /**
   * 加载通知列表
   */
  loadNotifications() {
    if (this.data.isLoading) return;

    this.setData({ 
      isLoading: true, 
      loadingText: this.data.page === 1 ? "加载中..." : "加载更多...",
    });

    const { page, pageSize } = this.data;

    wx.cloud.callFunction({
      name: "getNotifications",
      data: {
        page: page,
        pageSize: pageSize,
      },
      success: (res) => {
        console.log("获取通知列表成功:", res.result);
        if (res.result.success) {
          const newNotifications = res.result.data.notifications || [];
          // 预处理通知数据，添加格式化字段
          const processedNotifications = newNotifications.map(
            (notification) => ({
            ...notification,
            notificationTitle: this.getNotificationTitle(notification),
            notificationMessage: this.getNotificationMessage(notification),
            formattedTime: this.formatTime(notification.createTime),
            couponStatusText: notification.couponInfo
              ? this.getCouponStatusText(notification.couponInfo.status)
              : "",
            couponStatusClass: notification.couponInfo
              ? this.getCouponStatusClass(notification.couponInfo.status)
              : "", // 修复问题6：添加状态类名
            })
          );
          
          const hasMore = newNotifications.length === pageSize;
          
          const unreadCount = res.result.data.unreadCount || 0;
          
          this.setData({
            notifications:
              page === 1
                ? processedNotifications
                : [...this.data.notifications, ...processedNotifications],
            totalCount: res.result.data.totalCount || 0,
            unreadCount: unreadCount,
            hasUnreadMessages: unreadCount > 0, // 添加未读消息标识
            hasMore: hasMore,
            isLoading: false,
            currentFilterLabel: this.getCurrentFilterLabel(),
          });

          // 更新app全局的未读数量但不清除徽章
          app.globalData.unreadCount = res.result.data.unreadCount || 0;

          // 更新筛选标签计数
          this.updateFilterCounts();
          // 应用当前筛选
          this.applyFilter();
        } else {
          wx.showToast({
            title: res.result.message || "获取失败",
            icon: "none",
            duration: 2000,
          });
          this.setData({ isLoading: false });
        }
      },
      fail: (error) => {
        console.error("获取通知列表失败:", error);
        wx.showToast({
          title: "加载失败，请重试",
          icon: "none",
          duration: 2000,
        });
        this.setData({ isLoading: false });
      },
    });
  },

  /**
   * 刷新通知
   */
  refreshNotifications() {
    return new Promise((resolve) => {
      this.setData({
        page: 1,
        hasMore: true,
        notifications: [],
        filteredNotifications: [],
      });
      this.loadNotifications();
      setTimeout(resolve, 500); // 给一个最小刷新时间
    });
  },

  /**
   * 加载更多
   */
  loadMore() {
    if (!this.data.hasMore || this.data.isLoading) return;
    
    this.setData({
      page: this.data.page + 1,
    });
    this.loadNotifications();
  },

  /**
   * 更新筛选标签计数
   */
  updateFilterCounts() {
    const notifications = this.data.notifications;
    const activeNotifications = notifications.filter(
      (item) => item.notificationStatus === 'active' || !item.notificationStatus
    );
    
    const filterTabs = this.data.filterTabs.map((tab) => {
      if (tab.type === "all") {
        tab.count = activeNotifications.length;
      } else if (tab.type === "expiry") {
        // 修复问题4：过期提醒包含所有过期相关类型
        tab.count = activeNotifications.filter(
          (item) => item.type === "coupon_expiry" ||
                    item.type === "coupon_expiry_warning" ||
                    item.type === "coupon_expired" ||
                    item.couponStatus === 'expired'
        ).length;
      } else if (tab.hasSubTabs) {
        // 有二级tab的情况，计算总数并更新子tab计数
        if (tab.type === "redeem_request") {
          const myRequests = activeNotifications.filter(
            (item) => item.type === "redeem_request" && item.userRole === 'applicant'
          ).length;
          const receivedRequests = activeNotifications.filter(
            (item) => item.type === "redeem_request" && item.userRole === 'approver'
          ).length;
          
          tab.subTabs[0].count = myRequests; // 我申请的
          tab.subTabs[1].count = receivedRequests; // 我接收的
          tab.count = myRequests + receivedRequests;
        } else if (tab.type === "redeem_result") {
          const myResults = activeNotifications.filter(
            (item) => item.type === "redeem_result" && item.userRole === 'beneficiary'
          ).length;
          const processedResults = activeNotifications.filter(
            (item) => item.type === "redeem_result" && item.userRole === 'processor'
          ).length;
          
          tab.subTabs[0].count = myResults; // 我申请的
          tab.subTabs[1].count = processedResults; // 我兑现的
          tab.count = myResults + processedResults;
        }
      } else {
        tab.count = activeNotifications.filter(
          (item) => item.type === tab.type
        ).length;
      }
      return tab;
    });

    this.setData({ filterTabs });
  },

  /**
   * 切换筛选条件
   */
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter;
    if (filter === this.data.currentFilter) return;
    
    console.log("切换筛选条件:", filter);
    
    // 找到当前主tab对象
    const currentMainTab = this.data.filterTabs.find(tab => tab.type === filter);
    
    this.setData({ 
      currentFilter: filter,
      currentMainTab: currentMainTab,
      currentSubFilter: currentMainTab && currentMainTab.hasSubTabs ? currentMainTab.subTabs[0].type : null
    });
    
    this.applyFilter();
  },

  /**
   * 切换二级筛选条件
   */
  switchSubFilter(e) {
    const subFilter = e.currentTarget.dataset.filter;
    if (subFilter === this.data.currentSubFilter) return;
    
    console.log("切换二级筛选条件:", subFilter);
    this.setData({ currentSubFilter: subFilter });
    this.applyFilter();
  },

  /**
   * 应用筛选
   */
  applyFilter() {
    const { notifications, currentFilter, currentSubFilter } = this.data;
    let filteredNotifications = [];

    if (currentFilter === "all") {
      // 显示所有活跃的通知
      filteredNotifications = notifications.filter(
        (item) => item.notificationStatus === 'active' || !item.notificationStatus
      );
    } else if (currentFilter === "expiry") {
      // 修复问题4：过期提醒筛选包含所有过期相关类型
      filteredNotifications = notifications.filter(
        (item) => (item.type === "coupon_expiry" ||
                   item.type === "coupon_expiry_warning" ||
                   item.type === "coupon_expired" ||
                   item.couponStatus === 'expired') &&
                  (item.notificationStatus === 'active' || !item.notificationStatus)
      );
    } else if (currentFilter === "redeem_request" && currentSubFilter) {
      // 兑现申请的二级筛选
      if (currentSubFilter === "redeem_request_my") {
        // 我申请的：显示我作为申请人的通知
        filteredNotifications = notifications.filter(
          (item) => item.type === "redeem_request" && 
                    item.userRole === 'applicant' &&
                    (item.notificationStatus === 'active' || !item.notificationStatus)
        );
      } else if (currentSubFilter === "redeem_request_received") {
        // 我接收的：显示我作为审批人的通知
        filteredNotifications = notifications.filter(
          (item) => item.type === "redeem_request" && 
                    item.userRole === 'approver' &&
                    (item.notificationStatus === 'active' || !item.notificationStatus)
        );
      }
    } else if (currentFilter === "redeem_result" && currentSubFilter) {
      // 兑现完成的二级筛选
      if (currentSubFilter === "redeem_result_my") {
        // 我申请的：显示我作为受益人的通知
        filteredNotifications = notifications.filter(
          (item) => item.type === "redeem_result" && 
                    item.userRole === 'beneficiary' &&
                    (item.notificationStatus === 'active' || !item.notificationStatus)
        );
      } else if (currentSubFilter === "redeem_result_processed") {
        // 我兑现的：显示我作为处理人的通知
        filteredNotifications = notifications.filter(
          (item) => item.type === "redeem_result" && 
                    item.userRole === 'processor' &&
                    (item.notificationStatus === 'active' || !item.notificationStatus)
        );
      }
    } else {
      // 其他类型的筛选
      filteredNotifications = notifications.filter(
        (item) => item.type === currentFilter &&
                  (item.notificationStatus === 'active' || !item.notificationStatus)
      );
    }

    this.setData({
      filteredNotifications,
      currentFilterLabel: this.getCurrentFilterLabel(),
    });
  },

  /**
   * 处理通知点击
   */
  handleNotificationTap(e) {
    const notification = e.currentTarget.dataset.notification;
    console.log("点击通知:", notification);

    // 如果是未读通知，标记为已读
    if (!notification.isRead) {
      this.markAsReadAndUpdateBadge(notification._id);
    }

    // 修复问题3：根据通知类型进行不同的处理
    if (notification.type === 'profile_updated') {
      // 个人资料更新通知：只显示已读提示，不跳转页面
      wx.showToast({
        title: "已读",
        icon: "success",
        duration: 1500,
      });
      return;
    }

    // 其他类型通知：跳转到对应页面
    if (notification.couponInfo && notification.couponInfo._id) {
      wx.navigateTo({
        url: `/pages/couponDetail/couponDetail?id=${notification.couponInfo._id}`,
      });
    } else if (notification.couponId) {
      // 如果有券ID但没有券信息，尝试直接跳转
      wx.navigateTo({
        url: `/pages/couponDetail/couponDetail?id=${notification.couponId}`,
      });
    } else {
      wx.showToast({
        title: "相关券信息不存在",
        icon: "none",
        duration: 2000,
      });
    }
  },

  /**
   * 标记为已读并更新徽章
   */
  markAsReadAndUpdateBadge(notificationId) {
    wx.cloud.callFunction({
      name: "markNotificationAsRead",
      data: { notificationId },
      success: (res) => {
        console.log("标记通知为已读成功:", res.result);
        if (res.result.success) {
          // 更新本地数据
          const notifications = this.data.notifications.map(item => {
            if (item._id === notificationId) {
              return { ...item, isRead: true };
            }
            return item;
          });
          
          const filteredNotifications = this.data.filteredNotifications.map(item => {
            if (item._id === notificationId) {
              return { ...item, isRead: true };
            }
            return item;
          });
          
          const newUnreadCount = Math.max(0, this.data.unreadCount - 1);
          
          this.setData({
            notifications,
            filteredNotifications,
            unreadCount: newUnreadCount,
            hasUnreadMessages: newUnreadCount > 0, // 更新未读消息标识
          });
          
          // 更新全局未读数量和徽章
          app.globalData.unreadCount = newUnreadCount;
          app.updateMessageBadge(newUnreadCount);
        }
      },
      fail: (error) => {
        console.error("标记通知为已读失败:", error);
      },
    });
  },

  /**
   * 单独标记已读
   */
  markAsRead(e) {
    const notificationId = e.currentTarget.dataset.id;
    this.markAsReadAndUpdateBadge(notificationId);
  },

  /**
   * 全部标记为已读
   */
  markAllAsRead() {
    // 如果没有未读消息，直接返回
    if (this.data.unreadCount === 0) {
      wx.showToast({
        title: "暂无未读消息",
        icon: "none",
        duration: 1500,
      });
      return;
    }
    
    wx.showModal({
      title: "确认操作",
      content: "确定要将所有消息标记为已读吗？",
      success: (res) => {
        if (res.confirm) {
          this.performMarkAllAsRead();
        }
      },
    });
  },

  /**
   * 执行全部标记为已读
   */
  performMarkAllAsRead() {
    wx.cloud.callFunction({
      name: "markAllNotificationsAsRead",
      data: {},
      success: (res) => {
        console.log("全部标记为已读成功:", res.result);
        if (res.result.success) {
          // 更新本地数据
          const notifications = this.data.notifications.map(item => ({
            ...item,
            isRead: true
          }));
          
          const filteredNotifications = this.data.filteredNotifications.map(item => ({
            ...item,
            isRead: true
          }));
          
          this.setData({
            notifications,
            filteredNotifications,
            unreadCount: 0,
            hasUnreadMessages: false, // 更新未读消息标识
          });
          
          // 更新全局未读数量和徽章
          app.globalData.unreadCount = 0;
          app.updateMessageBadge(0);
          
          wx.showToast({
            title: "全部已读",
            icon: "success",
            duration: 2000,
          });
        } else {
          wx.showToast({
            title: res.result.message || "操作失败",
            icon: "none",
            duration: 2000,
          });
        }
      },
      fail: (error) => {
        console.error("全部标记为已读失败:", error);
        wx.showToast({
          title: "操作失败，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },

  /**
   * 删除通知
   */
  deleteNotification(e) {
    const notificationId = e.currentTarget.dataset.id;
    
    wx.showModal({
      title: "确认删除",
      content: "确定要删除这条消息吗？",
      success: (res) => {
        if (res.confirm) {
          this.performDeleteNotification(notificationId);
        }
      },
    });
  },

  /**
   * 执行删除通知
   */
  performDeleteNotification(notificationId) {
    wx.cloud.callFunction({
      name: "deleteNotification",
      data: { notificationId },
      success: (res) => {
        console.log("删除通知成功:", res.result);
        if (res.result.success) {
          // 查找要删除的通知
          const deletedNotification = this.data.notifications.find(
            item => item._id === notificationId
          );
          
          // 更新本地数据
          const notifications = this.data.notifications.filter(
            item => item._id !== notificationId
          );
          
          const filteredNotifications = this.data.filteredNotifications.filter(
            item => item._id !== notificationId
          );
          
          // 如果删除的是未读通知，更新未读数量
          let newUnreadCount = this.data.unreadCount;
          if (deletedNotification && !deletedNotification.isRead) {
            newUnreadCount = Math.max(0, newUnreadCount - 1);
          }
          
          this.setData({
            notifications,
            filteredNotifications,
            totalCount: Math.max(0, this.data.totalCount - 1),
            unreadCount: newUnreadCount,
          });
          
          // 更新筛选标签计数
          this.updateFilterCounts();
          
          // 更新全局未读数量和徽章
          app.globalData.unreadCount = newUnreadCount;
          app.updateMessageBadge(newUnreadCount);
          
          wx.showToast({
            title: "删除成功",
            icon: "success",
            duration: 2000,
          });
        } else {
          wx.showToast({
            title: res.result.message || "删除失败",
            icon: "none",
            duration: 2000,
          });
        }
      },
      fail: (error) => {
        console.error("删除通知失败:", error);
        wx.showToast({
          title: "删除失败，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },

  /**
   * 清空所有通知
   */
  clearAllNotifications() {
    wx.showModal({
      title: "确认清空",
      content: "确定要清空所有消息吗？此操作不可恢复！",
      success: (res) => {
        if (res.confirm) {
          this.performClearAllNotifications();
        }
      },
    });
  },

  /**
   * 执行清空所有通知
   */
  performClearAllNotifications() {
    wx.cloud.callFunction({
      name: "clearAllNotifications",
      data: {},
      success: (res) => {
        console.log("清空所有通知成功:", res.result);
        if (res.result.success) {
          this.setData({
            notifications: [],
            filteredNotifications: [],
            totalCount: 0,
            unreadCount: 0,
          });
          
          // 更新筛选标签计数
          this.updateFilterCounts();
          
          // 更新全局未读数量和徽章
          app.globalData.unreadCount = 0;
          app.updateTabBarBadge();
          
          wx.showToast({
            title: "清空成功",
            icon: "success",
            duration: 2000,
          });
        } else {
          wx.showToast({
            title: res.result.message || "清空失败",
            icon: "none",
            duration: 2000,
          });
        }
      },
      fail: (error) => {
        console.error("清空所有通知失败:", error);
        wx.showToast({
          title: "清空失败，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },

  /**
   * 获取当前筛选标签
   */
  getCurrentFilterLabel() {
    const currentTab = this.data.filterTabs.find(
      (tab) => tab.type === this.data.currentFilter
    );
    return currentTab ? currentTab.label : "全部";
  },

  /**
   * 获取通知标题
   */
  getNotificationTitle(notification) {
    const { type, subType, userRole } = notification;
    
    // 如果通知有自定义标题，直接使用
    if (notification.title) {
      return notification.title;
    }
    
    // 根据类型和角色生成标题
    switch (type) {
      case "new_coupon":
        return "新券提醒";
      case "redeem_request":
        if (userRole === 'applicant') {
          return "等待对方兑现";
        } else if (userRole === 'approver') {
          return "收到兑现申请";
        }
        return "兑现申请";
      case "redeem_result":
        if (userRole === 'beneficiary') {
          return "已成功兑现这张券";
        } else if (userRole === 'processor') {
          return "您已兑现爱人申请的券";
        }
        return "兑现完成";
      case "coupon_expiry":
      case "coupon_expiry_warning":
      case "coupon_expired":
        return "过期提醒";
      case "profile_updated":
        return "伴侣资料更新";
      default:
        return "系统通知";
    }
  },

  /**
   * 获取通知内容
   */
  getNotificationMessage(notification) {
    const { type, subType, userRole, couponInfo, senderInfo } = notification;
    
    // 如果通知有自定义内容，直接使用
    if (notification.content) {
      return notification.content;
    }
    
    if (!couponInfo) return "系统消息";
    
    const senderName = senderInfo?.nickname || senderInfo?.name || "对方";
    const couponName = couponInfo.name || couponInfo.title || "券券";
    
    switch (type) {
      case "new_coupon":
        return `${senderName}为你创建了新券：${couponName}`;
      case "redeem_request":
        if (userRole === 'applicant') {
          return `您已申请兑现券：${couponName}，等待对方处理`;
        } else if (userRole === 'approver') {
          return `对方申请兑现券：${couponName}`;
        }
        return `申请兑现券：${couponName}`;
      case "redeem_result":
        if (userRole === 'beneficiary') {
          return `您的券：${couponName} 已成功兑现`;
        } else if (userRole === 'processor') {
          return `您已成功兑现券：${couponName}`;
        }
        return `券"${couponName}"已兑现`;
      case "coupon_expiry":
      case "coupon_expiry_warning":
        return `券"${couponName}"即将过期，请及时使用`;
      case "coupon_expired":
        return `券"${couponName}"已过期`;
      default:
        return "系统消息";
    }
  },

  /**
   * 获取券状态文本
   */
  getCouponStatusText(status) {
    const statusMap = {
      draft: "草稿",
      received: "待兑现",
      pending_redeem: "申请兑现中",
      redeemed: "已兑现",
      cancelled: "已作废",
      expired: "已失效",
    };
    return statusMap[status] || "未知状态";
  },

  /**
   * 获取券状态样式类名 - 修复问题6
   */
  getCouponStatusClass(status) {
    const classMap = {
      'received': 'status-pending',      // 蓝色 - 待兑现
      'pending_redeem': 'status-warning', // 橙色 - 申请兑现中
      'redeemed': 'status-success',      // 绿色 - 已兑现
      'expired': 'status-expired',       // 灰色 - 已过期
      'cancelled': 'status-cancelled',   // 红色 - 已取消
      'draft': 'status-draft'            // 浅灰 - 草稿
    };
    return classMap[status] || 'status-unknown';
  },

  /**
   * 格式化时间
   */
  formatTime(dateStr) {
    if (!dateStr) return "";
    
    const date = new Date(dateStr);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    // 小于1分钟
    if (diff < 60000) {
      return "刚刚";
    }
    
    // 小于1小时
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000);
      return `${minutes}分钟前`;
    }
    
    // 小于1天
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000);
      return `${hours}小时前`;
    }
    
    // 小于7天
    if (diff < 604800000) {
      const days = Math.floor(diff / 86400000);
      return `${days}天前`;
    }
    
    // 超过7天显示具体日期
    return `${date.getMonth() + 1}月${date.getDate()}日`;
  },

  /**
   * 页面卸载
   */
  onUnload: function () {
    const app = getApp();
    if (this.globalRefreshHandler) {
      app.removeEventListener('globalDataRefresh', this.globalRefreshHandler);
    }
  },

  /**
   * 分享
   */
  onShareAppMessage() {
    return {
      title: "情侣信誉券 - 爱的承诺，甜蜜兑现",
      path: "/pages/index/index",
    };
  },
});
