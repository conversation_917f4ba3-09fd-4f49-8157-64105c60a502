/**
 * 生成邀请码云函数
 * 为用户生成唯一的情侣绑定邀请码
 */

const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event 云函数调用参数
 * @param {Object} context 云函数运行上下文
 */
exports.main = async (event, context) => {
  console.log('开始执行生成邀请码云函数:', event);
  
  try {
    // 获取微信调用上下文
    const wxContext = cloud.getWXContext();
    const { OPENID } = wxContext;
    
    if (!OPENID) {
      throw new Error('无法获取用户 OpenID');
    }

    console.log('用户 OPENID:', OPENID);

    // 查询用户信息 - 修复字段名问题
    const userQuery = await db.collection('users').where({
      _openid: OPENID
    }).get();

    if (userQuery.data.length === 0) {
      throw new Error('用户不存在，请先登录');
    }

    const userInfo = userQuery.data[0];
    console.log('查询到的用户信息:', userInfo);

    // 检查用户是否已经绑定情侣
    if (userInfo.partnerId) {
      console.log('用户已绑定情侣，partnerId:', userInfo.partnerId);
      throw new Error('您已经和伴侣绑定，无需再生成邀请码。如需重新绑定，请先解除当前绑定关系。');
    }

    // 生成唯一的邀请码
    let inviteCode;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 10;

    while (!isUnique && attempts < maxAttempts) {
      inviteCode = generateInviteCode();
      
      // 检查邀请码是否已存在 - 修复字段名问题
      const existingCodeQuery = await db.collection('users').where({
        inviteCode: inviteCode,
        inviteCodeExpiry: db.command.gt(new Date())
      }).get();
      
      if (existingCodeQuery.data.length === 0) {
        isUnique = true;
      }
      
      attempts++;
    }

    if (!isUnique) {
      throw new Error('生成邀请码失败，请稍后重试');
    }

    // 设置邀请码过期时间（24小时后）
    const expiryTime = new Date();
    expiryTime.setHours(expiryTime.getHours() + 24);

    // 更新用户的邀请码信息
    await db.collection('users').doc(userInfo._id).update({
      data: {
        inviteCode: inviteCode,
        inviteCodeExpiry: expiryTime,
        updateTime: new Date()
      }
    });

    console.log('邀请码生成成功:', inviteCode, '用户:', userInfo._id);

    return {
      success: true,
      message: '邀请码生成成功',
      data: {
        inviteCode: inviteCode,
        expiryTime: expiryTime.toISOString(),
        shareText: `Hi~ 这是我的情侣信任券邀请码：${inviteCode}，快来和我绑定吧！`,
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('生成邀请码云函数执行失败:', error);
    
    return {
      success: false,
      message: error.message || '生成邀请码失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 生成6位数字字母混合邀请码
 * @returns {string} 邀请码
 */
function generateInviteCode() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
} 