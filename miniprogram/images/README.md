# 图片资源说明

## 需要的图片文件

### 应用图标
- `app-logo.png` - 应用主图标 (建议尺寸: 120x120px)
- `default-avatar.png` - 默认头像 (建议尺寸: 80x80px)

### 功能图标 (SVG格式)
- `icon-add-pink.svg` - 粉色添加图标
- `lock-heart.svg` - 锁心图标
- `couple-hearts.svg` - 情侣心形图标

## 临时解决方案

在开发阶段，我们将使用：
1. 微信小程序提供的默认图标
2. 纯CSS绘制的简单图标
3. emoji表情作为临时图标

## 正式版本

建议找设计师设计专业的图标，或从以下途径获取：
- iconfont.cn
- iconpark.oceanengine.com
- 购买商业图标库 

# 图片资源目录

## 现有图片资源

### 应用图标
- `app-logo.png` - 应用Logo
- `default-avatar.png` - 默认头像

### SVG图标
- `couple-silhouette.svg` - 情侣剪影图标
- `couple-hearts.svg` - 双心图标
- `lock-heart.svg` - 爱心锁图标
- `icon-add-pink.svg` - 粉色添加图标

## 底部导航图标 (TabBar Icons)

### SVG源文件
- `tab-home.svg` - 首页图标（心形房子）
- `tab-coupon.svg` - 我的券图标（券卡片）
- `tab-message.svg` - 消息图标（爱心聊天气泡）
- `tab-profile.svg` - 我的图标（用户头像）

### 图标转换说明

**重要提示：微信小程序tabBar不支持SVG格式，需要转换为PNG格式**

请按以下步骤转换图标：

#### 方法一：在线转换工具
1. 访问 https://svgtopng.com/ 或类似的SVG转PNG在线工具
2. 上传SVG文件
3. 设置尺寸为 81x81 像素（推荐尺寸）
4. 下载PNG文件并命名为：
   - `tab-home.png` 和 `tab-home-active.png`
   - `tab-coupon.png` 和 `tab-coupon-active.png`
   - `tab-message.png` 和 `tab-message-active.png`
   - `tab-profile.png` 和 `tab-profile-active.png`

#### 方法二：使用设计工具
1. 在Figma、Sketch或Photoshop中打开SVG文件
2. 导出为PNG格式，尺寸81x81像素
3. 创建两个版本：
   - 普通状态：使用颜色 `#7A7E83`
   - 选中状态：使用颜色 `#FF69B4`

#### 方法三：命令行工具（开发者）
```bash
# 使用 librsvg (macOS)
brew install librsvg
rsvg-convert -w 81 -h 81 tab-home.svg > tab-home.png

# 使用 ImageMagick
convert -background none -size 81x81 tab-home.svg tab-home.png
```

### 使用方法

转换完成后，更新 `app.json` 中的图标路径：

```json
"tabBar": {
  "list": [
    {
      "pagePath": "pages/index/index",
      "text": "首页",
      "iconPath": "images/tab-home.png",
      "selectedIconPath": "images/tab-home-active.png"
    },
    {
      "pagePath": "pages/myCoupons/myCoupons", 
      "text": "我的券",
      "iconPath": "images/tab-coupon.png",
      "selectedIconPath": "images/tab-coupon-active.png"
    },
    {
      "pagePath": "pages/notifications/notifications",
      "text": "消息", 
      "iconPath": "images/tab-message.png",
      "selectedIconPath": "images/tab-message-active.png"
    },
    {
      "pagePath": "pages/profile/profile",
      "text": "我的",
      "iconPath": "images/tab-profile.png", 
      "selectedIconPath": "images/tab-profile-active.png"
    }
  ]
}
```

### 图标设计说明

所有图标都采用统一的设计风格：
- **主题色**：情侣粉色调 (#FF69B4, #FFB6C1, #FFC0CB)
- **风格**：简约线条 + 爱心元素装饰
- **尺寸**：24x24的SVG设计，适合缩放至81x81
- **特色**：每个图标都融入了情侣/爱心主题元素

#### 各图标含义
- 🏠 **首页**：心形房子，象征温馨的情侣小屋
- 🎫 **我的券**：爱心装饰的券卡，代表情侣之间的承诺卡片
- 💬 **消息**：爱心聊天气泡，表达情侣间的甜蜜交流
- 👤 **我的**：带心形装饰的用户头像，体现个人情侣身份

### 技术规格

- **推荐尺寸**：81x81 像素
- **格式**：PNG（支持透明背景）
- **颜色深度**：32位 RGBA
- **文件大小**：建议每个图标小于 40KB

## 其他资源

如需要其他尺寸或格式的图标，可以基于SVG源文件进行调整和导出。 