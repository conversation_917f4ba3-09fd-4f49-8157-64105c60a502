const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 删除单个通知
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    const { notificationId } = event
    
    // 参数验证
    if (!notificationId) {
      return {
        success: false,
        error: 'MISSING_PARAMS',
        message: '缺少必要参数：notificationId'
      }
    }
    
    // 删除通知（只能删除自己的通知） - 修复：使用recipientId字段
    const deleteResult = await db.collection('notifications')
      .where({
        _id: notificationId,
        recipientId: openid
      })
      .remove()
    
    if (deleteResult.stats.removed === 0) {
      return {
        success: false,
        error: 'NOTIFICATION_NOT_FOUND',
        message: '通知不存在或无权限操作'
      }
    }
    
    return {
      success: true,
      message: '通知已删除'
    }
    
  } catch (error) {
    console.error('删除通知失败:', error)
    return {
      success: false,
      error: 'SYSTEM_ERROR',
      message: '系统错误，请稍后重试'
    }
  }
} 