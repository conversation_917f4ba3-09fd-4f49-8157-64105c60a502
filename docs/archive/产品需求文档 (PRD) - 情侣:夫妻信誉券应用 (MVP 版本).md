# 产品需求文档 (PRD) - 情侣/夫妻信誉券应用 (MVP 版本)

## 1. 引言

### 1.1 项目背景

本项目的构想源于日常生活中的一个普遍痛点：在情侣/夫妻关系中，许多温暖的小承诺、小“甜头”常常由于繁忙或健忘而被忽视或遗忘，缺少一个有效的记录和履约机制。例如，一方口头承诺的“下次给你做顿好吃的”、“周末陪你打游戏”等，可能因缺乏具体凭证和提醒而未能实现，降低了承诺的“含金量”和彼此间的互动趣味。

“情侣信誉券”的概念由此诞生，旨在将这些情感化的承诺具象化为一张张可发行、可赠送、可核销的虚拟“票券”。通过这种形式，为情侣/夫妻提供一个充满仪式感和趣味性的工具，将日常互动中的承诺落地，增加关系的粘性和甜度，同时也鼓励双方的诚实守信。

### 1.2 项目目标

*   **MVP 核心目标:**
    *   快速实现信誉券的创建、发送、接收、查看和核销等核心闭环功能。
    *   验证产品概念在目标用户群体中的接受度和使用意愿。
    *   收集真实用户反馈，为后续迭代提供数据和方向。
    *   控制MVP开发成本和周期，确保项目在资源约束下快速启动。
*   **长远愿景:**
    *   成为情侣/夫妻间维系情感、增进互动、提升生活趣味和仪式感的首选线上工具。
    *   构建一个包含多种互动形式（信誉券、愿望清单、纪念日提醒等）的情侣专属空间。
    *   通过数据分析和个性化推荐，帮助伴侣更好地理解彼此需求，创造更多甜蜜时刻。

### 1.3 目标用户

*   **核心用户:** 处于稳定恋爱关系或已婚的伴侣双方。
*   **用户特征:**
    *   渴望为平淡的日常生活增加趣味性、仪式感和互动。
    *   愿意尝试新的方式来表达爱意和维系关系。
    *   对智能化工具接受度较高，习惯使用微信等社交应用。

## 2. 产品概述

### 2.1 产品定位

一款**增进情侣/夫妻情感互动与承诺履约的迷你工具**。它不是一个严肃的契约平台，而是以轻松、趣味的方式，用具象化的“信誉券”作为媒介，鼓励伴侣间表达爱意、设定承诺、并开心履约。

### 2.2 核心价值

*   **承诺具象化:** 将抽象的口头承诺转化为看得见、摸得着的虚拟“券”，赋予爱的承诺以形态。
*   **互动趣味化:** 信誉券的创建、发送、接收、核销过程充满游戏感和仪式感，为日常互动增添乐趣。
*   **履约激励:** “券”的存在本身就是一种提醒和动力，鼓励发行方积极履行承诺，接收方在合适时机甜言蜜语地“催促”兑现。
*   **情感记录:** 每一张券都记录着一次爱的表达或一份甜蜜的承诺，成为伴侣间珍贵的情感回忆。
*   **促进诚实守信:** 以轻松的方式，引导伴侣双方更重视彼此的承诺。

## 3. 用户场景与故事

为了更好地理解用户需求和产品功能，以下是几个典型的用户场景和对应的用户故事：

*   **场景1: 主动表达爱意或弥补过失**
    *   **情境:** 伴侣A最近因为工作忙忽略了伴侣B，想表达歉意和关爱。
    *   **用户故事:** *“作为一个忙于工作的伴侣，我想要创建一张新的‘爱心按摩券’，主动发送给我的另一半，以便于表达我的歉意和关爱，弥补我最近的疏忽。”*
    *   **操作流程:** A打开小程序 -> 进入发券页面 -> 选择“服务券”类型（或自定义名称）-> 填写券名称“睡前十分钟按摩券” -> 填写描述“承诺为你进行一次全身心的放松按摩” -> 设置有效期 -> 选择伴侣B -> 附带留言“亲爱的，辛苦了~” -> 确认发送。

*   **场景2: 鼓励与小小奖励**
    *   **情境:** 伴侣B最近在坚持学习一项新技能，伴侣A想给予鼓励和奖励。
    *   **用户故事:** *“作为一个支持伴侣的我，我想要创建一张‘游戏放飞券’，奖励我的另一半在技能学习上的坚持，以便于给她一点放松时间上的自由。”*
    *   **操作流程:** A打开小程序 -> 进入发券页面 -> 选择“奖励券”类型（或自定义名称）-> 填写券名称“游戏畅玩两小时券” -> 填写描述“凭此券，可获得连续两小时不受打扰的游戏时间” -> 设置有效期 -> 选择伴侣B -> 附带留言“奖励我的小机灵鬼！” -> 确认发送。

*   **场景3: 使用券并享受履约**
    *   **情境:** 伴侣B收到伴侣A发来的“爱心早餐券”，在某个周末想使用它。
    *   **用户故事:** *“作为一个收到信誉券的我，我想要在合适的时机找到那张‘爱心早餐券’，向我的另一半发起使用请求，以便于享受他亲手制作的美味早餐。”*
    *   **操作流程:** B打开小程序 -> 进入“我收到的券”列表 -> 找到“爱心早餐券” -> 点击查看详情 -> 确认券信息无误 -> 点击“使用此券/核销”按钮 -> **（可选，取决于是否需要发行方确认）** A收到核销请求并确认 -> 券状态变为“已使用”，B享受到承诺的早餐。

## 4. 核心功能需求 (MVP)

### 4.1 用户账户管理

*   **功能:**
    *   **微信一键授权登录:** 用户首次使用小程序时，通过微信授权快速创建账户，无需填写额外信息。
    *   **获取用户微信昵称、头像:** 登录成功后，自动获取并展示用户的微信昵称和头像作为应用内的身份标识。
    *   **伴侣绑定机制:**
        *   用户可在个人中心或其他指定入口发起伴侣绑定。
        *   生成一个唯一的邀请链接或邀请码。
        *   用户将链接/码分享给伴侣，伴侣点击/输入后确认绑定关系。
        *   绑定后，双方在应用内确认为唯一伴侣，实现定向发券的基础。
        *   （MVP阶段）只支持绑定一个伴侣。
*   **数据结构 (云数据库 `users` collection):**
    ```json
    {
      "_id": "string",      // 云开发自动生成
      "openid": "string",   // 微信用户唯一标识
      "nickName": "string", // 微信昵称
      "avatarUrl": "string",// 微信头像URL
      "partner_id": "string" // 绑定的伴侣用户ID (_id)，如果未绑定则为null
    }
    ```

### 4.2 信誉券创建

*   **功能:**
    *   用户在指定页面填写券的信息来创建一张新的信誉券。
    *   **券名称:** 必填项，简明扼要描述券的内容（如“一次电影约会”、“下周所有家务免做”）。文本输入。
    *   **券类型:**
        *   提供预设类型列表供用户选择（例如：服务券、体验券、按摩券、买单券、道歉券、奖励券、减免券、放假券、休闲券、游戏券等）。
        *   允许用户选择“自定义”类型，输入自己的类型名称。
    *   **券描述:** 选填项，对券名称的详细补充说明，明确承诺的具体内容、条件、范围等。多行文本输入。
    *   **有效期:**
        *   提供预设选项：7天、30天、永久有效。
        *   提供“指定日期前有效”的选项，用户可选择一个具体的截止日期。
        *   一旦设置并发送，有效期不可更改。
    *   **发行人:** 自动记录为当前创建券的用户。
    *   **（MVP可选）券面简单自定义:**
        *   提供几种预设的券面颜色主题或简单的图案背景供用户选择。
        *   用户创建时可选。
*   **数据结构 (云数据库 `vouchers` collection):**
    ```json
    {
      "_id": "string",              // 云开发自动生成
      "name": "string",             // 券名称
      "type": "string",             // 券类型（预设或自定义）
      "description": "string",      // 券描述 (可选)
      "issuer_id": "string",        // 发行人用户ID (_id)
      "recipient_id": "string",     // 接收人用户ID (_id)，发送时填写，创建时可为空
      "expiration_date": "Date",    // 过期时间 (Date对象，或Timestamp)
      "status": "string",           // 状态：pending (待发送或待使用), used (已使用), expired (已过期), revoked (已撤回 - MVP可选)
      "creation_time": "Date",      // 创建时间
      "used_time": "Date",          // 核销时间 (null 或 Date对象)
      "voucher_background": "string", // 券面背景标识或URL (MVP可选)
      "issue_message": "string"     // 发送附带留言 (可选)
    }
    ```

### 4.3 信誉券发送

*   **功能:**
    *   用户创建券后，或从“我发出的”待发送列表选择一张创建但未发送的券，进行发送操作。
    *   **选择接收人:** 列表中展示已绑定的伴侣，用户选择伴侣作为该券的唯一接收人。
    *   **发送前留言:** 提供一个选填的文本框，用户可输入一句想对伴侣说的话，随券一起发送。
    *   确认发送后，券状态变为“pending”（待使用），并关联上接收人ID。
    *   （MVP可选）发送后，该券不可再修改或撤回。
*   **关联操作:** 更新 `vouchers` collection 中对应券的 `recipient_id` 和 `status` 字段，并添加 `issue_message`。

### 4.4 信誉券接收与提醒

*   **功能:**
    *   接收方打开小程序时，如果在“我收到的券”列表中有新券（status为pending，recipient_id为当前用户ID），应有 visual 提示（如列表项高亮或角标）。
    *   **（如条件允许）微信服务通知:** 如果小程序获批发送服务通知，可以在接收方收到券时通过微信服务通知形式进行提醒，提高打开率。通知内容可包含券的名称和发行人。
    *   **“收到的券”列表:** 用户可在主页或指定页面查看所有发给自己的、状态为“pending”的信誉券列表。

### 4.5 信誉券查看与管理

*   **功能:**
    *   **券列表:** 提供两个TAB或独立的入口，“我发出的”和“我收到的”。
        *   “我发出的”列表：展示当前用户创建的所有券，包括待发送、待使用、已使用、已过期、已撤回（MVP可选）状态的券。
        *   “我收到的”列表：展示当前用户作为接收人的券，包括待使用、已使用、已过期状态的券。
    *   **券状态:** 每个列表项应清晰展示券的当前状态：
        *   待发送 (Status: pending, recipient_id is null) - 仅在“我发出的”列表中展示，可进行发送操作。
        *   待使用 (Status: pending, recipient_id is not null) - 在“我发出的”和“我收到的”列表中均展示，接收方可操作核销。
        *   已使用 (Status: used) - 在两个列表中均展示，不可再使用。
        *   已过期 (Status: expired) - 在两个列表中均展示，不可使用。
        *   已撤回 (Status: revoked) - 仅在“我发出的”列表中展示（MVP可选，如实现撤回功能）。
    *   **券详情:**
        *   点击列表项进入券详情页。
        *   展示券的名称、类型、描述、有效期、发行人、接收人、创建时间、发送留言（如有时）。
        *   根据券的状态，展示相应的操作按钮（如“使用此券”）。
        *   如果券已使用或已过期，应展示使用时间或过期提醒。

### 4.6 信誉券核销

*   **功能:**
    *   **由接收方发起:** 接收方在“收到的券”列表中找到状态为“待使用”的券，点击进入详情页。
    *   在券详情页有醒目的“使用此券”或“核销”按钮。
    *   接收方点击按钮后，该券的状态立即变为“已使用”。
    *   **（可选）发行方确认核销:** 接收方点击“使用此券”后，向发行方发送一个核销请求（应用内提示或服务通知），发行方收到请求后，可在券详情页点击“确认核销”，完成最终状态变更。这增加了仪式感和双方互动，但也增加了操作步骤。*MVP阶段建议：直接由接收方单方面核销后状态变更，简化流程。*
    *   核销后，记录核销 시간 (`used_time`)。
    *   已使用的券在列表和详情页显示为“已使用”状态，核销按钮消失。
*   **关联操作:** 更新 `vouchers` collection 中对应券的 `status` 为 `used`，填写 `used_time`。

### 4.7 历史记录

*   **功能:**
    *   提供一个入口，让用户可以查看所有非“待发送”或非“待使用”状态的券。
    *   历史记录可按照状态过滤查看：仅查看“已使用”的券，或者仅查看“已过期”的券。
    *   列表项简洁显示券名称、发行人、接收人、状态、以及创建/使用/过期时间等关键信息。
    *   点击列表项可查看该历史券的完整详情。
    *   双方（发行人和接收人）均可查看与自己相关的（即发行人或接收人是自己的）历史券记录。

## 5. 非功能性需求

*   **易用性:**
    *   界面设计简洁温馨，符合情侣应用的调性。
    *   核心操作流程（创建、发送、核销）应在3步以内完成。
    *   对技术不敏感的用户也能轻松上手，无复杂概念。
    *   提供简明的使用说明或新手指引。
*   **性能:**
    *   小程序启动和页面加载速度在合理范围内（< 3秒）。
    *   列表滚动流畅，数据加载及时。
    *   券的创建和发送响应迅速。
*   **安全性:**
    *   保障用户微信openid、昵称、头像等个人信息安全，不得泄露。
    *   信誉券数据（名称、描述、状态等）存储安全，防止泄露或被篡改。
    *   伴侣绑定关系应确保准确唯一，防止误绑或冒充。
*   **可扩展性:**
    *   数据库和云函数设计考虑未来增加新的券属性、新的互动功能（如愿望清单、积分体系）的可能性，数据结构和逻辑应有一定弹性。
    *   前端组件化设计，便于复用和功能模块的叠加。
*   **可维护性:**
    *   代码结构清晰，模块划分合理。
    *   关键逻辑和复杂部分应有必要的注释。
    *   使用版本控制工具（Git）管理代码，便于协作和回溯。
    *   利用微信开发者工具进行有效的调试。

## 6. 未来迭代功能建议 (Post-MVP)

以下功能可以在MVP版本上线并获得用户反馈后，根据优先级和资源情况逐步加入：

*   **情侣共享愿望清单:** 双方可添加共同或各自的愿望，例如“一起去旅行”、“想听你唱首歌”等。另一方可从愿望清单中选择一项，一键生成关联的信誉券进行“认领”履约。
*   **特殊纪念日/节日提醒与发券模板:** 设置纪念日（恋爱纪念日、结婚纪念日等）或系统预设节日（生日、情人节、圣诞节等），临近时自动提醒用户，并提供节日主题的券面模板供快速创建和发送。
*   **情侣积分/成就体系:** 根据用户创建、发送、接收、核销信誉券的行为给予积分奖励。积分可用于兑换高级券面、特殊模板等。设计成就徽章，鼓励用户积极互动（如“百券伴侣”、“履约达人”）。
*   **高级券面自定义:** 支持用户上传照片作为券背景、选择更多预设颜色主题、字体样式、添加贴纸等，让每张券都独一无二。
*   **“摇一摇”随机券:** 增加即时趣味互动。伴侣双方同时“摇一摇”手机，随机生成一个预设的“小任务”券（例如“一次爱的抱抱券”、“夸夸我券”），增加生活的随机小确幸。
*   **数据统计与回顾:** 提供可视化数据报告，例如“年度甜蜜回顾”。展示双方在过去一年中发过的券总数、使用率最高的券类型、谁是“发券达人”、谁是“核销小能手”等趣味统计。
*   **券的评论与心情记录:** 在券核销后，允许发行方和接收方对这次“履约”体验进行 간단한评价或记录此刻的心情（可私密仅双方可见）。
*   **券转赠功能:** （谨慎设计）允许接收方在特定条件下将未使用的券转赠给发行方（例如“一次家务券”接收方不想用，转赠回发行方，相当于一次家务变成发行方自己做）。
*   **小程序内聊天功能:** （高成本，谨慎考虑）内置简单的文字或语音聊天，方便伴侣围绕信誉券进行沟通。

## 7. 成功指标 (MVP)

衡量MVP版本是否成功的关键指标包括：

*   **用户注册量与活跃用户数 (DAU/MAU):** 衡量用户是否被产品的核心概念吸引并开始使用。日活跃用户数和月活跃用户数能够反映产品的日常使用频率。
*   **伴侣绑定成功率:** 衡量用户是否成功建立了伴侣关系，这是后续功能使用的基础。
*   **信誉券创建、发送、核销数量:** 这是核心功能的使用数据，直接反映了用户对产品的核心价值是否产生认可和行动。统计总数以及人均数量。
*   **信誉券核销率:** 已发送的券中，有多少比例最终被核销。这是衡量产品是否真正促进了“履约”的关键指标。
*   **用户留存率:** 衡量用户在使用一段时间后，是否愿意继续使用产品。高留存率说明产品具有长期价值。
*   **用户反馈与评分:** 通过用户在小程序商店的评分、评论，以及收集的用户访谈或问卷反馈，了解用户对产品的满意度、Bug反馈和改进建议。正向反馈多说明产品方向正确。

---

本PRD为情侣/夫妻信誉券应用的MVP版本提供了明确的需求和指导。后续的设计和开发工作应严格围绕上述核心功能展开，并在开发过程中充分利用微信小程序云开发和AI辅助工具的优势，以高效、低成本地实现产品落地。