<!--
  情侣信誉券卡片组件 WXML
  设计理念：突出"券"的概念，营造浪漫情侣氛围
  参考：情侣券卡片设计集，真实卡片比例 1.59:1
-->
<view class="coupon-card {{statusClass}} {{typeClass}} {{customClass}}" 
      bind:tap="onCardTap"
      hover-class="card-hover" 
      hover-stay-time="150">
  
  <!-- 券卡片主体 -->
  <view class="coupon-card-body">
    
    <!-- 券顶部区域 -->
    <view class="coupon-header">
      <!-- 券类型标签 -->
      <view class="coupon-type-tag {{typeClass}}" wx:if="{{couponType}}">
        {{couponTypeText}}
      </view>
      
      <!-- 券编号 -->
      <view class="coupon-number" wx:if="{{couponNumber && showCouponNumber}}">
        <text class="number-prefix">No.</text>
        <text class="number-value">{{couponNumber}}</text>
      </view>
    </view>
    
    <!-- 券中央内容区域 -->
    <view class="coupon-content">
      <!-- 券主题图标 -->
      <view class="coupon-main-icon" wx:if="{{typeIcon}}">
        {{typeIcon}}
      </view>
      
      <!-- 券标题 -->
      <view class="coupon-title">{{title}}</view>
      
      <!-- 券描述 -->
      <view class="coupon-description" wx:if="{{description}}">
        {{description}}
      </view>
    </view>
    
    <!-- 券底部信息区域 -->
    <view class="coupon-footer">
      <view class="coupon-meta">
        <!-- 发券人信息 -->
        <view class="sender-info" wx:if="{{sender}}">
          <text class="sender-label">发券人：</text>
          <text class="sender-name">{{sender}}</text>
        </view>
        
        <!-- 有效期信息 -->
        <view class="expiry-info" wx:if="{{expiryDate}}">
          <text class="expiry-label">有效期至：</text>
          <text class="expiry-date">{{formattedExpiryDate}}</text>
        </view>
      </view>
      
      <!-- 券状态指示器 -->
      <view class="coupon-status-indicator" wx:if="{{showStatus}}">
        <view class="status-badge status-{{status}}">
          <view class="status-icon">{{statusIcon}}</view>
          <view class="status-text">{{statusText}}</view>
        </view>
      </view>
    </view>
    
    <!-- 券装饰边框 -->
    <view class="coupon-decoration" wx:if="{{showDecoration}}">
      <!-- 左侧装饰孔 -->
      <view class="decoration-holes decoration-left">
        <view class="hole" wx:for="{{decorationHoles}}" wx:key="index"></view>
      </view>
      
      <!-- 右侧装饰孔 -->
      <view class="decoration-holes decoration-right">
        <view class="hole" wx:for="{{decorationHoles}}" wx:key="index"></view>
      </view>
      
      <!-- 顶部装饰线 -->
      <view class="decoration-line decoration-top"></view>
      
      <!-- 底部装饰线 -->
      <view class="decoration-line decoration-bottom"></view>
    </view>
    
    <!-- 背景装饰图案 -->
    <view class="background-pattern"></view>
    
    <!-- 快捷操作按钮 -->
    <view class="coupon-actions" wx:if="{{showActions}}">
      <slot name="actions"></slot>
    </view>
    
    <!-- 自定义内容插槽 -->
    <slot name="content"></slot>
    
  </view>
  
  <!-- 券卡片阴影 -->
  <view class="coupon-shadow"></view>
  
</view>
