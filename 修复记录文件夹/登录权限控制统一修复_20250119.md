# 情侣信誉券小程序登录权限控制统一修复

## 背景
用户反馈在未登录状态下切换底部导航栏时，权限控制不一致：
1. 只在第一次切换时提示登录，后续切换不再提示
2. 提示登录后不会自动跳转到首页
3. 各页面的权限检查逻辑不统一

## 产品分析与决策

### 主流产品权限控制模式分析
1. **严格登录模式**（微信、支付宝、淘宝）
   - 除首页外，其他功能页面都需要登录
   - 优势：数据安全性高，用户体验一致

2. **部分开放模式**（小红书、抖音）
   - 允许浏览部分内容，核心功能需要登录
   - 优势：降低使用门槛，提高转化率

### 最终决策：采用严格登录模式
**理由：**
- 情侣信誉券是强个人化、强关系绑定的应用
- 所有功能都基于用户身份和情侣关系
- 涉及个人隐私和情侣互动数据
- 无登录状态下展示的信息价值有限
- 符合用户对私密应用的安全期望

## 修复方案

### 1. 全局权限控制机制
在 `app.js` 中新增两个全局方法：

#### `checkPagePermission(pagePath, showModal)`
- 统一的页面权限检查方法
- 支持配置允许访问的页面列表（目前仅首页）
- 未登录时显示统一的登录提示弹窗
- 自动处理页面跳转逻辑

#### `interceptTabSwitch(currentPagePath)`
- 专门用于底部导航栏切换的权限拦截
- 在每个tab页面的 `onShow` 方法中调用
- 确保每次切换都进行权限检查

### 2. 统一的权限提示逻辑
- **弹窗标题**：需要登录
- **弹窗内容**：请先登录后再使用此功能
- **确认按钮**：去登录（跳转到首页）
- **取消按钮**：取消（如果不在首页则强制跳转到首页）

### 3. 各页面权限控制更新

#### 我的券页面 (`myCoupons.js`)
- 移除 `onLoad` 中的权限检查代码
- 在 `onShow` 中统一调用 `app.interceptTabSwitch()`
- 简化页面初始化逻辑

#### 消息页面 (`notifications.js`)
- 移除 `initData` 方法中的权限检查
- 在 `onShow` 中统一调用 `app.interceptTabSwitch()`
- 直接调用 `refreshNotifications()` 加载数据

#### 个人中心页面 (`profile.js`)
- 移除 `initData` 方法中的权限检查
- 在 `onShow` 中统一调用 `app.interceptTabSwitch()`
- 在 `refreshData` 中增加登录状态检查

## 修复效果

### 修复前的问题
1. 权限检查不一致，有些页面只在第一次检查
2. 登录提示弹窗样式和文案不统一
3. 取消登录后不会自动跳转到首页
4. 代码重复，维护困难

### 修复后的改进
1. **统一的权限控制**：所有tab页面都使用相同的权限检查逻辑
2. **每次都检查**：每次切换tab都会进行权限验证，不再只检查一次
3. **强制跳转**：无论用户选择"去登录"还是"取消"，都会确保跳转到首页
4. **代码复用**：全局方法避免代码重复，便于维护和修改
5. **用户体验一致**：所有页面的登录提示保持一致

## 技术实现细节

### 权限检查流程
1. 用户切换到需要登录的tab页面
2. `onShow` 方法调用 `app.interceptTabSwitch()`
3. 检查当前页面是否在允许访问列表中
4. 如果需要登录但用户未登录，显示登录提示
5. 根据用户选择进行相应的页面跳转

### 允许访问的页面配置
```javascript
const allowedPages = [
  'pages/index/index'  // 仅首页允许未登录访问
];
```

### 页面跳转逻辑
- 用户点击"去登录"：直接跳转到首页
- 用户点击"取消"：检查当前是否在首页，如果不是则强制跳转到首页

## 文件修改记录
1. `miniprogram/app.js`：新增全局权限控制方法
2. `miniprogram/pages/myCoupons/myCoupons.js`：统一权限检查
3. `miniprogram/pages/notifications/notifications.js`：统一权限检查
4. `miniprogram/pages/profile/profile.js`：统一权限检查

## 测试建议
1. 在未登录状态下，尝试切换到各个tab页面，确认都会提示登录
2. 测试登录提示弹窗的"去登录"和"取消"按钮功能
3. 确认登录后可以正常访问所有页面
4. 测试退出登录后再次切换tab的权限控制

修复完成后，情侣信誉券小程序的登录权限控制已经完全统一，用户体验更加一致和安全。 