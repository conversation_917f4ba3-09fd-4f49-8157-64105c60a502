/* feedback.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 50%, #F0F8FF 100%);
  padding: 30rpx;
  box-sizing: border-box;
  padding-bottom: 150rpx;
}

/* 区域标题 */
.section-header {
  margin-bottom: 30rpx;
  text-align: center;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: #666;
}

/* 常见问题区域 */
.faq-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.faq-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  overflow: hidden;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.faq-item.expanded {
  border-color: #FF69B4;
  background: rgba(255, 105, 180, 0.05);
}

.faq-question {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  cursor: pointer;
}

.question-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  line-height: 1.4;
}

.expand-icon {
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: bold;
  width: 40rpx;
  text-align: center;
}

.faq-answer {
  padding: 0 20rpx 24rpx 20rpx;
  border-top: 1rpx solid rgba(255, 105, 180, 0.1);
}

.answer-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

/* 反馈表单区域 */
.feedback-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

/* 表单组件 */
.form-group {
  margin-bottom: 40rpx;
  position: relative;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.form-label.required::after {
  content: ' *';
  color: #FF4444;
}

/* 反馈类型选择 */
.feedback-types {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.type-item.selected {
  border-color: #FF69B4;
  background: rgba(255, 105, 180, 0.1);
}

.type-icon {
  font-size: 36rpx;
  margin-bottom: 12rpx;
}

.type-label {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  font-weight: 500;
}

.type-item.selected .type-label {
  color: #FF69B4;
}

/* 表单输入 */
.form-input, .form-textarea {
  width: 100%;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  font-size: 26rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.form-input {
  height: 80rpx;
}

.form-textarea {
  min-height: 120rpx;
  line-height: 1.6;
}

.form-input:focus, .form-textarea:focus {
  background: #fff;
  border-color: #FF69B4;
  box-shadow: 0 0 0 4rpx rgba(255, 105, 180, 0.1);
}

.form-input.error, .form-textarea.error {
  border-color: #FF4444;
  background: rgba(255, 68, 68, 0.05);
}

.char-count {
  position: absolute;
  bottom: -30rpx;
  right: 0;
  font-size: 22rpx;
  color: #999;
}

.error-text {
  position: absolute;
  bottom: -30rpx;
  left: 0;
  font-size: 22rpx;
  color: #FF4444;
}

/* 图片上传 */
.image-upload {
  margin-top: 16rpx;
}

.image-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
}

.uploaded-image {
  width: 100%;
  height: 100%;
}

.remove-image {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-icon {
  color: white;
  font-size: 24rpx;
  line-height: 1;
}

.add-image-btn {
  aspect-ratio: 1;
  background: #f8f9fa;
  border: 2rpx dashed #ccc;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  color: #999;
}

.add-icon {
  font-size: 36rpx;
  font-weight: 300;
}

.add-text {
  font-size: 22rpx;
}

.upload-tip {
  font-size: 22rpx;
  color: #999;
  line-height: 1.4;
}

/* 提交区域 */
.submit-section {
  margin-top: 50rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(45deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 24rpx rgba(255, 105, 180, 0.4);
}

.submit-btn[disabled] {
  background: #ccc;
  color: #999;
  box-shadow: none;
}

/* 联系我们区域 */
.contact-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  gap: 20rpx;
  transition: all 0.3s ease;
}

.contact-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.contact-icon {
  font-size: 36rpx;
  width: 60rpx;
  text-align: center;
}

.contact-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.contact-title {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.contact-detail {
  font-size: 24rpx;
  color: #666;
}

.contact-action {
  font-size: 24rpx;
  color: #FF69B4;
  font-weight: 500;
  padding: 8rpx 16rpx;
  background: rgba(255, 105, 180, 0.1);
  border-radius: 12rpx;
}

/* 成功弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 80vw;
  max-width: 500rpx;
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.3);
}

.success-icon {
  font-size: 100rpx;
  margin-bottom: 30rpx;
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-20rpx); }
}

.success-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.success-message {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(45deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.3);
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #FF69B4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 20rpx;
  }
  
  .feedback-types {
    grid-template-columns: 1fr;
  }
  
  .image-list {
    grid-template-columns: repeat(2, 1fr);
  }
}

