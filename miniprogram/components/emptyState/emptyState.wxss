/**
 * 空状态提示组件样式
 * 优雅的空状态设计，提供友好的用户体验
 */

/* 主容器 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 96rpx 48rpx;
  min-height: 480rpx;
  position: relative;
  background: linear-gradient(180deg, rgba(248, 250, 252, 0.5) 0%, transparent 100%);
  border-radius: 24rpx;
  overflow: hidden;
}

/* 背景装饰 */
.empty-state::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(233, 30, 99, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(244, 143, 177, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

/* 尺寸变体 */
.empty-state.small {
  padding: 64rpx 32rpx;
  min-height: 320rpx;
}

.empty-state.large {
  padding: 128rpx 64rpx;
  min-height: 600rpx;
}

.empty-state.compact {
  padding: 48rpx 32rpx;
  min-height: 240rpx;
}

.empty-state.fullscreen {
  min-height: 100vh;
  padding: 10vh 48rpx;
}

/* 图标区域样式 */
.empty-icon-wrapper {
  margin-bottom: 40rpx;
  position: relative;
  filter: drop-shadow(0 12rpx 32rpx rgba(0, 0, 0, 0.06));
  transition: all 400ms cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-state:hover .empty-icon-wrapper {
  transform: translateY(-6rpx) scale(1.02);
  filter: drop-shadow(0 16rpx 48rpx rgba(0, 0, 0, 0.1));
}

/* 图片图标 */
.empty-icon-image {
  width: 240rpx;
  height: 240rpx;
  border-radius: 24rpx;
  object-fit: cover;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  transition: transform 300ms ease;
}

.empty-icon-image:hover {
  transform: scale(1.05) rotate(1deg);
}

.empty-state.small .empty-icon-image {
  width: 180rpx;
  height: 180rpx;
}

.empty-state.large .empty-icon-image {
  width: 280rpx;
  height: 280rpx;
}

.empty-state.compact .empty-icon-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
}

/* 文字和默认图标容器 */
.empty-icon-text,
.empty-icon-default {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  height: 240rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  border-radius: 50%;
  border: 6rpx solid rgba(233, 30, 99, 0.1);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 8rpx 32rpx rgba(233, 30, 99, 0.08),
    inset 0 2rpx 8rpx rgba(255, 255, 255, 0.6);
}

.empty-state.small .empty-icon-text,
.empty-state.small .empty-icon-default {
  width: 180rpx;
  height: 180rpx;
}

.empty-state.large .empty-icon-text,
.empty-state.large .empty-icon-default {
  width: 280rpx;
  height: 280rpx;
  border-width: 8rpx;
}

.empty-state.compact .empty-icon-text,
.empty-state.compact .empty-icon-default {
  width: 120rpx;
  height: 120rpx;
  border-width: 4rpx;
}

/* 图标容器装饰光晕 */
.empty-icon-text::before,
.empty-icon-default::before {
  content: '';
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  right: 20rpx;
  bottom: 20rpx;
  background: radial-gradient(circle, rgba(233, 30, 99, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: opacity 300ms ease;
}

.empty-state:hover .empty-icon-text::before,
.empty-state:hover .empty-icon-default::before {
  opacity: 1;
}

/* 图标文字内容 */
.icon-content,
.default-icon {
  font-size: 128rpx;
  line-height: 1;
  opacity: 0.85;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
  transition: all 300ms ease;
}

.empty-state:hover .icon-content,
.empty-state:hover .default-icon {
  opacity: 1;
  transform: scale(1.05);
}

.empty-state.small .icon-content,
.empty-state.small .default-icon {
  font-size: 96rpx;
}

.empty-state.large .icon-content,
.empty-state.large .default-icon {
  font-size: 160rpx;
}

.empty-state.compact .icon-content,
.empty-state.compact .default-icon {
  font-size: 64rpx;
}

/* 标题样式 */
.empty-title {
  margin-bottom: 20rpx;
  max-width: 600rpx;
}

.title-text {
  font-size: 40rpx;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.3;
  letter-spacing: -0.5rpx;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.empty-state.small .title-text {
  font-size: 34rpx;
}

.empty-state.large .title-text {
  font-size: 48rpx;
}

.empty-state.compact .title-text {
  font-size: 30rpx;
}

/* 描述样式 */
.empty-description {
  margin-bottom: 56rpx;
  max-width: 560rpx;
}

.description-text {
  font-size: 30rpx;
  color: #64748b;
  line-height: 1.7;
  font-weight: 400;
  letter-spacing: 0.2rpx;
}

.empty-state.small .description-text {
  font-size: 26rpx;
}

.empty-state.large .description-text {
  font-size: 34rpx;
}

.empty-state.compact .description-text {
  font-size: 24rpx;
}

/* 内容插槽 */
.empty-content-slot {
  margin-bottom: 40rpx;
  width: 100%;
  max-width: 600rpx;
}

/* 操作按钮区域 */
.empty-actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  align-items: center;
  width: 100%;
  max-width: 480rpx;
}

.action-button {
  width: 100%;
  max-width: 320rpx;
  height: 96rpx;
  border: none;
  border-radius: 48rpx;
  font-size: 34rpx;
  font-weight: 600;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5rpx;
}

.action-button::after {
  border: none;
}

/* 主要操作按钮 */
.primary-action {
  background: linear-gradient(135deg, #e91e63 0%, #f48fb1 50%, #ec4899 100%);
  color: white;
  box-shadow: 
    0 12rpx 36rpx rgba(233, 30, 99, 0.25),
    0 4rpx 12rpx rgba(233, 30, 99, 0.15);
  position: relative;
}

.primary-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 200ms ease;
}

.primary-action:hover::before {
  opacity: 1;
}

/* 主题变体 */
.primary-action.success {
  background: linear-gradient(135deg, #10b981 0%, #34d399 50%, #059669 100%);
  box-shadow: 
    0 12rpx 36rpx rgba(16, 185, 129, 0.25),
    0 4rpx 12rpx rgba(16, 185, 129, 0.15);
}

.primary-action.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 50%, #d97706 100%);
  box-shadow: 
    0 12rpx 36rpx rgba(245, 158, 11, 0.25),
    0 4rpx 12rpx rgba(245, 158, 11, 0.15);
}

.primary-action.error {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 50%, #dc2626 100%);
  box-shadow: 
    0 12rpx 36rpx rgba(239, 68, 68, 0.25),
    0 4rpx 12rpx rgba(239, 68, 68, 0.15);
}

.primary-action.info {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 50%, #2563eb 100%);
  box-shadow: 
    0 12rpx 36rpx rgba(59, 130, 246, 0.25),
    0 4rpx 12rpx rgba(59, 130, 246, 0.15);
}

/* 次要操作按钮 */
.secondary-action {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #475569;
  border: 3rpx solid #e2e8f0;
  box-shadow: 
    0 4rpx 16rpx rgba(71, 85, 105, 0.08),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.6);
}

/* 按钮悬浮和点击效果 */
.action-hover {
  transform: scale(0.97);
}

.primary-action:hover {
  transform: translateY(-3rpx) scale(1.02);
  box-shadow: 
    0 16rpx 48rpx rgba(233, 30, 99, 0.35),
    0 8rpx 24rpx rgba(233, 30, 99, 0.2);
}

.secondary-action:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-color: #cbd5e1;
  transform: translateY(-2rpx);
  box-shadow: 
    0 8rpx 24rpx rgba(71, 85, 105, 0.12),
    inset 0 2rpx 4rpx rgba(255, 255, 255, 0.8);
}

/* 自定义操作插槽 */
.custom-actions-slot {
  margin-top: 24rpx;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
}

/* 小尺寸按钮适配 */
.empty-state.small .action-button {
  height: 80rpx;
  font-size: 30rpx;
  max-width: 280rpx;
}

.empty-state.large .action-button {
  height: 112rpx;
  font-size: 38rpx;
  max-width: 360rpx;
}

.empty-state.compact .action-button {
  height: 64rpx;
  font-size: 26rpx;
  max-width: 240rpx;
}

/* 响应式设计 */
@media screen and (max-width: 480rpx) {
  .empty-state {
    padding: 64rpx 32rpx;
    min-height: 360rpx;
  }
  
  .empty-icon-image,
  .empty-icon-text,
  .empty-icon-default {
    width: 180rpx;
    height: 180rpx;
  }
  
  .icon-content,
  .default-icon {
    font-size: 96rpx;
  }
  
  .title-text {
    font-size: 34rpx;
  }
  
  .description-text {
    font-size: 26rpx;
  }
  
  .action-button {
    height: 80rpx;
    font-size: 30rpx;
  }
  
  .empty-actions {
    gap: 16rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .empty-state {
    background: linear-gradient(180deg, rgba(30, 41, 59, 0.5) 0%, transparent 100%);
  }
  
  .empty-state::before {
    background-image: 
      radial-gradient(circle at 20% 20%, rgba(244, 143, 177, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(233, 30, 99, 0.03) 0%, transparent 50%);
  }
  
  .empty-icon-text,
  .empty-icon-default {
    background: linear-gradient(135deg, #334155 0%, #475569 50%, #64748b 100%);
    border-color: rgba(244, 143, 177, 0.2);
    box-shadow: 
      0 8rpx 32rpx rgba(244, 143, 177, 0.1),
      inset 0 2rpx 8rpx rgba(255, 255, 255, 0.05);
  }
  
  .empty-icon-text::before,
  .empty-icon-default::before {
    background: radial-gradient(circle, rgba(244, 143, 177, 0.2) 0%, transparent 70%);
  }
  
  .title-text {
    color: #f1f5f9;
    background: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .description-text {
    color: #cbd5e1;
  }
  
  .secondary-action {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
    color: #cbd5e1;
    border-color: #64748b;
  }
  
  .secondary-action:hover {
    background: linear-gradient(135deg, #475569 0%, #64748b 100%);
    border-color: #94a3b8;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .empty-icon-text,
  .empty-icon-default {
    border-width: 8rpx;
    border-color: #000;
  }
  
  .title-text {
    color: #000;
    font-weight: 800;
    -webkit-text-fill-color: #000;
  }
  
  .description-text {
    color: #333;
    font-weight: 600;
  }
  
  .primary-action {
    background: #e91e63;
    border: 4rpx solid #000;
  }
  
  .secondary-action {
    background: #fff;
    color: #000;
    border: 4rpx solid #000;
  }
}

/* 无障碍支持 - 减少动画 */
@media (prefers-reduced-motion: reduce) {
  .empty-icon-wrapper,
  .empty-icon-image,
  .icon-content,
  .default-icon,
  .action-button {
    transition: none;
  }
  
  .empty-state:hover .empty-icon-wrapper {
    transform: none;
  }
  
  .empty-icon-image:hover {
    transform: none;
  }
  
  .empty-state:hover .icon-content,
  .empty-state:hover .default-icon {
    transform: none;
  }
  
  .primary-action:hover,
  .secondary-action:hover {
    transform: none;
  }
}

/* 打印样式 */
@media print {
  .empty-state {
    padding: 40rpx 20rpx;
    background: none;
    box-shadow: none;
    page-break-inside: avoid;
  }
  
  .empty-state::before {
    display: none;
  }
  
  .action-button {
    display: none;
  }
  
  .title-text {
    color: #000;
    -webkit-text-fill-color: #000;
  }
  
  .description-text {
    color: #666;
  }
}
