/**
 * 情侣信誉券小程序全局样式
 */

/* 全局变量定义 */
page {
  --primary-color: #FF69B4;
  --secondary-color: #FFB6C1;
  --accent-color: #FFC0CB;
  --background-color: #F0F8FF;
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --border-color: #E5E5E5;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --border-radius: 12rpx;
  --border-radius-large: 24rpx;
}

/* 全局重置样式 */


page {
  background-color: var(--background-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  color: var(--text-primary);
  line-height: 1.6;
}

/* 通用容器样式 */
.container {
  padding: 32rpx;
  min-height: 100vh;
}

.page-container {
  background: linear-gradient(135deg, #FFB6C1 0%, #FFC0CB 50%, #F0F8FF 100%);
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-light);
  padding: 32rpx;
  margin-bottom: 24rpx;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.card-subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-top: 8rpx;
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: var(--border-radius);
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.btn-primary:active {
  transform: scale(0.98);
  opacity: 0.9;
}

.btn-secondary {
  background: white;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-ghost {
  background: transparent;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-disabled {
  background: #F5F5F5;
  color: var(--text-light);
  border: none;
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.btn-large {
  padding: 32rpx 64rpx;
  font-size: 36rpx;
}

/* 统一的主要按钮样式 - 与首页"发送给ta"按钮保持一致 */
.primary-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  border-radius: 50rpx;
  background: linear-gradient(135deg, #ff69b4, #ffb6c1) !important;
  box-shadow:
    0 8rpx 24rpx rgba(255, 105, 180, 0.3),
    0 2rpx 8rpx rgba(255, 105, 180, 0.2);
  border: none;
  backdrop-filter: blur(10rpx);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.primary-button:active {
  transform: scale(0.98);
  box-shadow:
    0 4rpx 16rpx rgba(255, 105, 180, 0.4),
    0 1rpx 4rpx rgba(255, 105, 180, 0.3);
}

.primary-button::after {
  border: none;
}

/* 次要按钮样式 */
.secondary-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  border-radius: 50rpx;
  background: white;
  border: 2rpx solid #ff69b4;
  color: #ff69b4;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.secondary-button:active {
  transform: scale(0.98);
  background: rgba(255, 105, 180, 0.1);
}

.secondary-button::after {
  border: none;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 16rpx;
  font-weight: 500;
}

.input-field {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 32rpx;
  background: white;
  transition: border-color 0.3s ease;
}

.input-field:focus {
  border-color: var(--primary-color);
  outline: none;
}

.input-error {
  border-color: #FF4444;
}

.input-error-text {
  color: #FF4444;
  font-size: 24rpx;
  margin-top: 8rpx;
}

/* 列表样式 */
.list {
  background: white;
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.list-item {
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  align-items: center;
  transition: background-color 0.3s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: #F8F8F8;
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  font-size: 32rpx;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.list-item-subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.list-item-arrow {
  color: var(--text-light);
  font-size: 24rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 36rpx;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 28rpx;
  color: var(--text-light);
  line-height: 1.8;
}

/* 加载状态样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 64rpx;
}

.loading-text {
  margin-left: 16rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 32rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.tag-primary {
  background: var(--primary-color);
  color: white;
}

.tag-secondary {
  background: var(--secondary-color);
  color: white;
}

.tag-success {
  background: #4CAF50;
  color: white;
}

.tag-warning {
  background: #FF9800;
  color: white;
}

.tag-error {
  background: #F44336;
  color: white;
}

.tag-outline {
  background: transparent;
  border: 1rpx solid var(--primary-color);
  color: var(--primary-color);
}

/* 分割线样式 */
.divider {
  height: 1rpx;
  background: var(--border-color);
  margin: 32rpx 0;
}

.divider-text {
  position: relative;
  text-align: center;
  margin: 32rpx 0;
}

.divider-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background: var(--border-color);
}

.divider-text text {
  background: var(--background-color);
  padding: 0 24rpx;
  color: var(--text-light);
  font-size: 28rpx;
  position: relative;
  z-index: 1;
}

/* 工具类样式 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-around { justify-content: space-around; }
.flex-1 { flex: 1; }

.mt-8 { margin-top: 16rpx; }
.mt-16 { margin-top: 32rpx; }
.mt-24 { margin-top: 48rpx; }
.mt-32 { margin-top: 64rpx; }

.mb-8 { margin-bottom: 16rpx; }
.mb-16 { margin-bottom: 32rpx; }
.mb-24 { margin-bottom: 48rpx; }
.mb-32 { margin-bottom: 64rpx; }

.p-8 { padding: 16rpx; }
.p-16 { padding: 32rpx; }
.p-24 { padding: 48rpx; }
.p-32 { padding: 64rpx; }

.hidden { display: none; }
.visible { display: block; }

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 24rpx;
  }
  
  .card {
    padding: 24rpx;
  }
  
  .btn {
    padding: 20rpx 40rpx;
    font-size: 30rpx;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.bounce {
  animation: bounce 1s ease-in-out;
}
