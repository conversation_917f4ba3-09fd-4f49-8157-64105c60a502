<!-- profile.wxml -->
<view class="container">
  <!-- 骨架屏 -->
  <view wx:if="{{isLoading}}" class="skeleton-container">
    <view class="skeleton-user-section">
      <view class="skeleton-user-header">
        <view class="skeleton-avatar"></view>
        <view class="skeleton-user-info">
          <view class="skeleton-text skeleton-nickname"></view>
          <view class="skeleton-text skeleton-id"></view>
        </view>
        <view class="skeleton-edit-btn"></view>
      </view>
      <view class="skeleton-relationship">
        <view class="skeleton-text skeleton-relationship-text"></view>
        <view class="skeleton-btn"></view>
      </view>
    </view>
    <view class="skeleton-actions">
      <view class="skeleton-action-item"></view>
      <view class="skeleton-action-item"></view>
    </view>
    <view class="skeleton-stats">
      <view class="skeleton-stat-item"></view>
      <view class="skeleton-stat-item"></view>
      <view class="skeleton-stat-item"></view>
      <view class="skeleton-stat-item"></view>
    </view>
  </view>
  <!-- 实际内容 -->
  <view wx:else>
    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-header">
        <image class="user-avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill" bindtap="changeAvatar"></image>
        <view class="user-info">
          <text class="user-nickname">{{userInfo.nickName || '未设置昵称'}}</text>
          <text class="user-signature">{{userInfo.signature || '设置你的个性签名吧~'}}</text>
          <!-- 显示是否为自动生成的资料 -->
          <view wx:if="{{userInfo.isAutoGenerated}}" class="profile-status">
            <text class="auto-profile-hint">🤖 使用临时资料</text>
          </view>
        </view>
        <view class="edit-button" bindtap="editProfile">
          <text class="edit-icon">✏️</text>
        </view>
      </view>
      <!-- 情侣关系状态 -->
      <view class="relationship-status">
        <view wx:if="{{coupleInfo}}" class="couple-card">
          <view class="couple-avatars">
            <image class="couple-avatar my-avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill" binderror="onMyAvatarError"></image>
            <view class="heart-connector">💕</view>
            <image class="couple-avatar partner-avatar" src="{{coupleInfo.partnerAvatar}}" mode="aspectFill" binderror="onPartnerAvatarError" bindload="onPartnerAvatarLoad"></image>
          </view>
          <view class="couple-info">
            <text class="couple-names">{{userInfo.nickName}} & {{coupleInfo.partnerNickname}}</text>
            <text class="together-days">相恋第 {{coupleInfo.togetherDays}} 天</text>
          </view>
        </view>
        <view wx:else class="unbind-hint">
          <text class="hint-icon">💍</text>
          <text class="hint-text">暂未绑定情侣关系</text>
          <button class="bind-btn" bindtap="goToBinding">立即绑定</button>
        </view>
      </view>
    </view>
    <!-- 数据统计 -->
    <view class="stats-section">
      <text class="stats-title">我的数据</text>
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{stats.sentCount}}</text>
          <text class="stat-label">已发送券</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{stats.receivedCount}}</text>
          <text class="stat-label">已接收券</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{stats.redeemedCount}}</text>
          <text class="stat-label">已兑现券</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{coupleInfo ? coupleInfo.togetherDays : 0}}</text>
          <text class="stat-label">相恋天数</text>
        </view>
      </view>
    </view>
    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <text class="group-title">帮助支持</text>
        <view class="menu-item" hover-class="item-hover" bindtap="goToFeedback">
          <view class="menu-icon">💬</view>
          <text class="menu-title">帮助与反馈</text>
          <view class="menu-arrow">→</view>
        </view>
        <view class="menu-item" hover-class="item-hover" bindtap="goToAbout">
          <view class="menu-icon">ℹ️</view>
          <text class="menu-title">关于我们</text>
          <view class="menu-arrow">→</view>
        </view>
      </view>
    </view>
    <!-- 底部操作 -->
    <view class="bottom-actions">
      <button wx:if="{{coupleInfo}}" class="danger-btn" bindtap="showUnbindModal">解除情侣绑定</button>
    </view>
  </view>
  <!-- 解绑确认弹窗 - 重新设计 -->
  <view wx:if="{{showUnbindModal}}" class="modal-overlay-new" bindtap="hideUnbindModal">
    <view class="unbind-modal" catchtap="preventClose">
      <!-- 顶部装饰 -->
      <view class="modal-decoration">
        <view class="broken-heart">💔</view>
        <view class="decoration-line"></view>
      </view>
      <!-- 标题区域 -->
      <view class="modal-title-section">
        <text class="modal-main-title">真的要解除绑定吗？</text>
        <text class="modal-subtitle">这个决定会让我们很难过...</text>
      </view>
      <!-- 警告内容 - 优化为更紧凑的布局 -->
      <view class="warning-content">
        <view class="warning-item">
          <view class="warning-icon">🎫</view>
          <text class="warning-text">所有情侣券将被永久删除</text>
        </view>
        <view class="warning-item">
          <view class="warning-icon">💝</view>
          <text class="warning-text">美好回忆数据无法找回</text>
        </view>
        <view class="warning-item">
          <view class="warning-icon">📅</view>
          <text class="warning-text">相恋天数记录将被清零</text>
        </view>
      </view>
      <!-- 挽留文字 - 简化内容 -->
      <view class="retention-text">
        <text class="retention-main">也许只是一时冲动？</text>
        <text class="retention-sub">不如先冷静一下，重新绑定将是全新开始</text>
      </view>
      <!-- 确认输入 -->
      <view class="confirm-section">
        <text class="confirm-label">如果真的决定了，请输入"确认解绑"</text>
        <input class="confirm-input-new" placeholder="输入确认解绑" value="{{confirmText}}" bindinput="onConfirmInput" />
      </view>
      <!-- 按钮区域 -->
      <view class="modal-buttons">
        <button class="stay-btn" bindtap="hideUnbindModal">
          <text class="btn-text">💕 再想想</text>
        </button>
        <button class="unbind-btn {{confirmText === '确认解绑' ? 'active' : ''}}" bindtap="confirmUnbind" disabled="{{confirmText !== '确认解绑'}}">
          <text class="btn-text">💔 确认解绑</text>
        </button>
      </view>
    </view>
  </view>
</view>