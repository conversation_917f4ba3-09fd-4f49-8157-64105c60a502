<!--
  空状态提示组件 WXML
  用于列表无数据、搜索无结果等场景，支持自定义提示图标、文字和操作按钮
-->
<view class="empty-state {{sizeClass}} {{customClass}}" wx:if="{{visible}}">
  
  <!-- 图标区域 -->
  <view class="empty-icon-wrapper">
    <!-- 自定义图片 -->
    <image wx:if="{{iconImage}}" 
           class="empty-icon-image" 
           src="{{iconImage}}" 
           mode="aspectFit"
           lazy-load
           bindload="onIconLoad"
           binderror="onIconError" />
    
    <!-- 字符图标 -->
    <view wx:elif="{{icon}}" class="empty-icon-text">
      <text class="icon-content">{{icon}}</text>
    </view>
    
    <!-- 默认图标 -->
    <view wx:else class="empty-icon-default">
      <text class="default-icon">📭</text>
    </view>
  </view>
  
  <!-- 主标题 -->
  <view class="empty-title" wx:if="{{title}}">
    <text class="title-text">{{title}}</text>
  </view>
  
  <!-- 描述文本 -->
  <view class="empty-description" wx:if="{{description}}">
    <text class="description-text">{{description}}</text>
  </view>
  
  <!-- 自定义内容插槽 -->
  <view class="empty-content-slot" wx:if="{{showContentSlot}}">
    <slot name="content"></slot>
  </view>
  
  <!-- 操作按钮区域 -->
  <view class="empty-actions" wx:if="{{showActions}}">
    <!-- 主要操作按钮 -->
    <button wx:if="{{actionText}}" 
            class="action-button primary-action {{actionType}}" 
            bind:tap="onActionTap"
            hover-class="action-hover"
            loading="{{actionLoading}}"
            disabled="{{actionDisabled}}">
      {{actionText}}
    </button>
    
    <!-- 次要操作按钮 -->
    <button wx:if="{{secondaryActionText}}" 
            class="action-button secondary-action" 
            bind:tap="onSecondaryActionTap"
            hover-class="action-hover"
            loading="{{secondaryActionLoading}}"
            disabled="{{secondaryActionDisabled}}">
      {{secondaryActionText}}
    </button>
    
    <!-- 自定义操作插槽 -->
    <view class="custom-actions-slot" wx:if="{{showActionsSlot}}">
      <slot name="actions"></slot>
    </view>
  </view>
  
</view>

<!-- 组件样式 -->
<style>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 80rpx 40rpx;
  min-height: 400rpx;
  position: relative;
}

.empty-state.small {
  padding: 60rpx 32rpx;
  min-height: 300rpx;
}

.empty-state.large {
  padding: 120rpx 48rpx;
  min-height: 500rpx;
}

.empty-state.compact {
  padding: 48rpx 32rpx;
  min-height: 240rpx;
}

/* 图标区域样式 */
.empty-icon-wrapper {
  margin-bottom: 32rpx;
  position: relative;
  filter: drop-shadow(0 8rpx 24rpx rgba(0, 0, 0, 0.08));
  transition: transform 300ms ease;
}

.empty-state:hover .empty-icon-wrapper {
  transform: translateY(-4rpx);
}

.empty-icon-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
}

.empty-state.small .empty-icon-image {
  width: 160rpx;
  height: 160rpx;
}

.empty-state.large .empty-icon-image {
  width: 240rpx;
  height: 240rpx;
}

.empty-icon-text,
.empty-icon-default {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 50%;
  border: 4rpx solid #e2e8f0;
  position: relative;
}

.empty-state.small .empty-icon-text,
.empty-state.small .empty-icon-default {
  width: 160rpx;
  height: 160rpx;
}

.empty-state.large .empty-icon-text,
.empty-state.large .empty-icon-default {
  width: 240rpx;
  height: 240rpx;
}

.icon-content,
.default-icon {
  font-size: 120rpx;
  line-height: 1;
  opacity: 0.8;
}

.empty-state.small .icon-content,
.empty-state.small .default-icon {
  font-size: 96rpx;
}

.empty-state.large .icon-content,
.empty-state.large .default-icon {
  font-size: 144rpx;
}

/* 标题样式 */
.empty-title {
  margin-bottom: 16rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
}

.empty-state.small .title-text {
  font-size: 32rpx;
}

.empty-state.large .title-text {
  font-size: 40rpx;
}

/* 描述样式 */
.empty-description {
  margin-bottom: 48rpx;
  max-width: 520rpx;
}

.description-text {
  font-size: 28rpx;
  color: #64748b;
  line-height: 1.6;
}

.empty-state.small .description-text {
  font-size: 26rpx;
}

.empty-state.large .description-text {
  font-size: 30rpx;
}

/* 内容插槽 */
.empty-content-slot {
  margin-bottom: 32rpx;
  width: 100%;
}

/* 操作按钮区域 */
.empty-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
  width: 100%;
  max-width: 400rpx;
}

.action-button {
  width: 100%;
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 250ms ease;
  position: relative;
  overflow: hidden;
}

.action-button::after {
  border: none;
}

.primary-action {
  background: linear-gradient(135deg, #e91e63 0%, #f48fb1 100%);
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(233, 30, 99, 0.25);
}

.primary-action.success {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  box-shadow: 0 8rpx 24rpx rgba(16, 185, 129, 0.25);
}

.primary-action.warning {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  box-shadow: 0 8rpx 24rpx rgba(245, 158, 11, 0.25);
}

.secondary-action {
  background: #f8fafc;
  color: #475569;
  border: 2rpx solid #e2e8f0;
}

.action-hover {
  transform: scale(0.98);
  opacity: 0.9;
}

.primary-action:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(233, 30, 99, 0.35);
}

.secondary-action:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

/* 自定义操作插槽 */
.custom-actions-slot {
  margin-top: 16rpx;
  width: 100%;
}

/* 响应式适配 */
@media screen and (max-width: 480rpx) {
  .empty-state {
    padding: 60rpx 32rpx;
  }
  
  .empty-icon-image,
  .empty-icon-text,
  .empty-icon-default {
    width: 160rpx;
    height: 160rpx;
  }
  
  .icon-content,
  .default-icon {
    font-size: 96rpx;
  }
  
  .title-text {
    font-size: 32rpx;
  }
  
  .description-text {
    font-size: 26rpx;
  }
  
  .action-button {
    height: 80rpx;
    font-size: 30rpx;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .empty-icon-text,
  .empty-icon-default {
    background: linear-gradient(135deg, #334155 0%, #475569 100%);
    border-color: #64748b;
  }
  
  .title-text {
    color: #f1f5f9;
  }
  
  .description-text {
    color: #cbd5e1;
  }
  
  .secondary-action {
    background: #334155;
    color: #cbd5e1;
    border-color: #475569;
  }
  
  .secondary-action:hover {
    background: #475569;
    border-color: #64748b;
  }
}
</style>
