# 云函数部署指南

## 🎯 当前状态
您的小程序已经可以正常预览，但云函数还未部署，这就是为什么会出现 `FUNCTION_NOT_FOUND` 错误的原因。

## 📋 需要部署的云函数
根据项目诊断，以下云函数需要部署：

### ✅ 已准备好的核心云函数
- `loginOrCreateUser` - 用户登录/创建
- `generateInviteCode` - 生成邀请码
- `bindWithInviteCode` - 绑定邀请码
- `createOrUpdateCoupon` - 创建/更新券

### ⚠️ 需要完善的云函数
- `checkCoupleStatus` - 检查情侣状态
- `getCoupons` - 获取券列表
- `updateCouponStatus` - 更新券状态
- `updateUserProfile` - 更新用户资料

## 🚀 部署步骤

### 第一步：开通云开发控制台
1. 在微信开发者工具中，点击工具栏的"云开发"按钮
2. 点击"开通云开发"
3. 选择环境名称（建议：`couple-trust-prod`）
4. 选择套餐（开发阶段选择免费套餐即可）

### 第二步：获取环境ID
1. 开通成功后，记录下环境ID（类似：`cloud1-xxxxx`）
2. 这个ID需要替换app.js中的环境ID

### 第三步：在微信开发者工具中部署云函数
1. 在左侧文件树中，找到 `cloudfunctions` 目录
2. 右键点击某个云函数文件夹（如 `loginOrCreateUser`）
3. 选择"上传并部署：云端安装依赖"
4. 等待部署完成

### 第四步：批量部署所有云函数
对以下云函数重复第三步的操作：
- [ ] loginOrCreateUser
- [ ] generateInviteCode  
- [ ] bindWithInviteCode
- [ ] createOrUpdateCoupon

## ⚠️ 重要提醒

### 环境ID配置
部署完云函数后，需要更新 `miniprogram/app.js` 中的环境ID：

```javascript
wx.cloud.init({
  env: '你的真实环境ID', // 替换这里
  traceUser: true,
});
```

### 数据库集合创建
云函数部署后，还需要在云开发控制台创建数据库集合：
- `users` - 用户信息
- `couples` - 情侣绑定关系
- `coupons` - 信任券数据
- `notifications` - 通知消息

## 🔧 故障排除

### 云函数调用失败
如果云函数调用失败，检查：
1. 环境ID是否正确
2. 云函数是否部署成功
3. 数据库权限是否正确

### 部署失败
如果部署失败，可能原因：
1. 网络问题 - 重试部署
2. 依赖包问题 - 检查package.json
3. 权限问题 - 确认小程序账号权限

## 📞 获取帮助
- 微信开发者社区：https://developers.weixin.qq.com/community
- 云开发文档：https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html 