<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情侣信誉券 - 顶级品牌设计方案</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .showcase {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .hero {
            text-align: center;
            margin-bottom: 60px;
            color: white;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        
        .hero .subtitle {
            font-size: 1.4rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }
        
        .hero .tagline {
            font-size: 1rem;
            opacity: 0.7;
            font-style: italic;
        }
        
        .naming-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 40px;
            margin-bottom: 50px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .names-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .name-group h3 {
            color: #667eea;
            font-size: 1.3rem;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .name-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .name-item {
            background: #f7fafc;
            padding: 15px 20px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .name-item:hover {
            transform: translateX(5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }
        
        .name-item.recommended {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-left-color: #ffd700;
            position: relative;
        }
        
        .name-item.recommended::before {
            content: "👑";
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2rem;
        }
        
        .name-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .name-desc {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .icons-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .icons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }
        
        .icon-showcase {
            background: #ffffff;
            border-radius: 24px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.08);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }
        
        .icon-showcase::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        
        .icon-showcase:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
        
        .icon-container {
            background: #f8fafc;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 25px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
        }
        
        .icon-meta {
            text-align: left;
        }
        
        .icon-name {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .icon-style {
            font-size: 0.95rem;
            color: #667eea;
            font-weight: 500;
            margin-bottom: 15px;
        }
        
        .icon-description {
            font-size: 0.9rem;
            color: #4a5568;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .icon-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .feature-tag {
            background: #edf2f7;
            color: #2d3748;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .download-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 24px;
            padding: 30px;
            text-align: center;
            margin-top: 40px;
        }
        
        .download-btn {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .pro-badge {
            display: inline-flex;
            align-items: center;
            background: linear-gradient(135deg, #ffd700, #ff8c00);
            color: #1a202c;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 30px;
        }
        
        .pro-badge::before {
            content: "⭐";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="showcase">
        <!-- Hero Section -->
        <div class="hero">
            <h1>情侣信誉券</h1>
            <p class="subtitle">让承诺更有仪式感</p>
            <p class="tagline">Professional Brand Design by Master Designer</p>
        </div>

        <!-- Naming Section -->
        <div class="naming-section">
            <h2 class="section-title">🏆 品牌命名方案</h2>
            
            <div class="names-container">
                <div class="name-group">
                    <h3>🎯 专业推荐</h3>
                    <div class="name-list">
                        <div class="name-item recommended">
                            <div class="name-title">情侣信誉券</div>
                            <div class="name-desc">经典原创，突出信任与承诺的价值感</div>
                        </div>
                        <div class="name-item">
                            <div class="name-title">情侣约定券</div>
                            <div class="name-desc">简洁有力，直击核心功能</div>
                        </div>
                        <div class="name-item">
                            <div class="name-title">情侣承诺券</div>
                            <div class="name-desc">庄重感强，适合认真的约定</div>
                        </div>
                    </div>
                </div>
                
                <div class="name-group">
                    <h3>🌟 创意升级</h3>
                    <div class="name-list">
                        <div class="name-item">
                            <div class="name-title">情侣守护券</div>
                            <div class="name-desc">温暖守护，体现呵护与陪伴</div>
                        </div>
                        <div class="name-item">
                            <div class="name-title">情侣心愿券</div>
                            <div class="name-desc">浪漫美好，强调愿望的实现</div>
                        </div>
                        <div class="name-item">
                            <div class="name-title">情侣甜蜜券</div>
                            <div class="name-desc">青春活力，年轻情侣的最爱</div>
                        </div>
                        <div class="name-item">
                            <div class="name-title">情侣契约券</div>
                            <div class="name-desc">正式感强，现代都市情侣风</div>
                        </div>
                        <div class="name-item">
                            <div class="name-title">情侣誓言券</div>
                            <div class="name-desc">仪式感满分，适合重要时刻</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <span class="pro-badge">专业建议：情侣信誉券</span>
                <p style="color: #4a5568; margin-top: 15px;">既保留原创想法，"信誉"二字有分量感和价值感，听起来专业而不失温情</p>
            </div>
        </div>

        <!-- Icons Section -->
        <div class="icons-section">
            <h2 class="section-title">🎨 顶级图标设计方案</h2>
            <p style="text-align: center; color: #4a5568; margin-bottom: 20px;">作为顶级图标设计大师，为您呈现5款专业级作品</p>
            
            <div class="icons-grid">
                <!-- 方案1 -->
                <div class="icon-showcase">
                    <div class="icon-container">
                        <svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient id="voucherGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FFE4E6"/>
                              <stop offset="50%" stop-color="#FFB6C1"/>
                              <stop offset="100%" stop-color="#FF69B4"/>
                            </linearGradient>
                            <linearGradient id="paperGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FFFFFF"/>
                              <stop offset="100%" stop-color="#FAFAFA"/>
                            </linearGradient>
                            <filter id="premiumShadow1">
                              <feDropShadow dx="3" dy="5" stdDeviation="8" flood-opacity="0.25"/>
                            </filter>
                            <pattern id="securityPattern1" patternUnits="userSpaceOnUse" width="20" height="20">
                              <rect width="20" height="20" fill="#FFF5F7"/>
                              <circle cx="10" cy="10" r="1" fill="#FFB6C1" opacity="0.3"/>
                            </pattern>
                          </defs>
                          <circle cx="72" cy="72" r="70" fill="url(#voucherGradient1)" filter="url(#premiumShadow1)"/>
                          <rect x="20" y="40" width="104" height="64" rx="8" fill="url(#paperGradient1)" filter="url(#premiumShadow1)"/>
                          <rect x="20" y="40" width="104" height="64" rx="8" fill="url(#securityPattern1)" opacity="0.4"/>
                          <rect x="20" y="40" width="104" height="64" rx="8" fill="none" stroke="#FF69B4" stroke-width="2"/>
                          <path d="M20 45 L20 40 Q20 36, 24 36 L28 36 Q32 36, 32 40 L32 45 Z" fill="#FF69B4" opacity="0.8"/>
                          <path d="M20 99 L20 104 Q20 108, 24 108 L28 108 Q32 108, 32 104 L32 99 Z" fill="#FF69B4" opacity="0.8"/>
                          <rect x="28" y="50" width="88" height="8" rx="2" fill="#FF69B4"/>
                          <path d="M60 75 C58 73, 55 73, 55 76 C55 79, 60 84, 60 84 C60 84, 65 79, 65 76 C65 73, 62 73, 60 75" fill="#FF69B4"/>
                          <path d="M84 75 C82 73, 79 73, 79 76 C79 79, 84 84, 84 84 C84 84, 89 79, 89 76 C89 73, 86 73, 84 75" fill="#FF69B4"/>
                          <line x1="65" y1="78" x2="79" y2="78" stroke="#FF69B4" stroke-width="2" stroke-linecap="round"/>
                          <circle cx="100" cy="90" r="8" fill="none" stroke="#FF1744" stroke-width="1.5"/>
                        </svg>
                    </div>
                    <div class="icon-meta">
                        <div class="icon-name">经典券面设计</div>
                        <div class="icon-style">专业金融风格</div>
                        <div class="icon-description">采用传统券面设计语言，融入安全纹理、撕边元素和印章认证，体现专业性和可信度。双心连接设计象征情侣间的紧密关系。</div>
                        <div class="icon-features">
                            <span class="feature-tag">安全纹理</span>
                            <span class="feature-tag">撕边设计</span>
                            <span class="feature-tag">印章认证</span>
                            <span class="feature-tag">专业感</span>
                        </div>
                    </div>
                </div>

                <!-- 方案2 -->
                <div class="icon-showcase">
                    <div class="icon-container">
                        <svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient id="modernGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FF8A80"/>
                              <stop offset="50%" stop-color="#FFAB91"/>
                              <stop offset="100%" stop-color="#FFCC9A"/>
                            </linearGradient>
                            <linearGradient id="cardGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FFFFFF"/>
                              <stop offset="100%" stop-color="#F8F9FA"/>
                            </linearGradient>
                            <filter id="luxuryShadow2">
                              <feDropShadow dx="4" dy="6" stdDeviation="12" flood-opacity="0.2"/>
                            </filter>
                            <linearGradient id="goldAccent2" x1="0%" y1="0%" x2="100%" y2="0%">
                              <stop offset="0%" stop-color="#FFD700"/>
                              <stop offset="50%" stop-color="#FFA500"/>
                              <stop offset="100%" stop-color="#FF8C00"/>
                            </linearGradient>
                          </defs>
                          <rect width="144" height="144" rx="28" fill="url(#modernGradient2)" filter="url(#luxuryShadow2)"/>
                          <rect x="24" y="36" width="96" height="72" rx="12" fill="url(#cardGradient2)" filter="url(#luxuryShadow2)"/>
                          <rect x="24" y="36" width="96" height="6" rx="3" fill="url(#goldAccent2)"/>
                          <rect x="36" y="52" width="16" height="12" rx="2" fill="#E0E0E0" stroke="#BDBDBD" stroke-width="1"/>
                          <rect x="38" y="54" width="12" height="8" rx="1" fill="#F5F5F5"/>
                          <circle cx="48" cy="80" r="10" fill="#FFE4E6" stroke="#FF69B4" stroke-width="2"/>
                          <circle cx="76" cy="80" r="10" fill="#FFE4E6" stroke="#FF69B4" stroke-width="2"/>
                          <circle cx="48" cy="77" r="3" fill="#FF69B4"/>
                          <path d="M43 85 Q48 88, 53 85" stroke="#FF69B4" stroke-width="2" fill="none"/>
                          <circle cx="76" cy="77" r="3" fill="#FF69B4"/>
                          <path d="M71 85 Q76 88, 81 85" stroke="#FF69B4" stroke-width="2" fill="none"/>
                          <path d="M62 80 C60 78, 57 78, 57 81 C57 84, 62 89, 62 89 C62 89, 67 84, 67 81 C67 78, 64 78, 62 80" fill="#FF1744"/>
                          <circle cx="100" cy="90" r="6" fill="none" stroke="#FF69B4" stroke-width="1"/>
                        </svg>
                    </div>
                    <div class="icon-meta">
                        <div class="icon-name">现代卡片设计</div>
                        <div class="icon-style">简约奢华风</div>
                        <div class="icon-description">借鉴银行卡设计理念，金色装饰条彰显高端品质，芯片和NFC元素体现科技感，情侣头像设计温馨亲切。</div>
                        <div class="icon-features">
                            <span class="feature-tag">银行卡风格</span>
                            <span class="feature-tag">金色装饰</span>
                            <span class="feature-tag">科技感</span>
                            <span class="feature-tag">奢华质感</span>
                        </div>
                    </div>
                </div>

                <!-- 方案3 -->
                <div class="icon-showcase">
                    <div class="icon-container">
                        <svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <radialGradient id="sealGradient3" cx="50%" cy="50%" r="50%">
                              <stop offset="0%" stop-color="#FFCDD2"/>
                              <stop offset="70%" stop-color="#F8BBD9"/>
                              <stop offset="100%" stop-color="#E1BEE7"/>
                            </radialGradient>
                            <linearGradient id="documentGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FFFEF7"/>
                              <stop offset="100%" stop-color="#FFF8E1"/>
                            </linearGradient>
                            <filter id="authenticShadow3">
                              <feDropShadow dx="2" dy="4" stdDeviation="6" flood-opacity="0.3"/>
                            </filter>
                            <pattern id="watermark3" patternUnits="userSpaceOnUse" width="30" height="30">
                              <rect width="30" height="30" fill="#FAFAFA"/>
                              <path d="M15 8 C13 6, 10 6, 10 9 C10 12, 15 17, 15 17 C15 17, 20 12, 20 9 C20 6, 17 6, 15 8" fill="#FFE4E6" opacity="0.3"/>
                            </pattern>
                          </defs>
                          <circle cx="72" cy="72" r="70" fill="url(#sealGradient3)" filter="url(#authenticShadow3)"/>
                          <rect x="28" y="32" width="88" height="80" rx="6" fill="url(#documentGradient3)" filter="url(#authenticShadow3)"/>
                          <rect x="28" y="32" width="88" height="80" rx="6" fill="url(#watermark3)" opacity="0.5"/>
                          <rect x="36" y="44" width="72" height="4" rx="2" fill="#FF69B4"/>
                          <circle cx="72" cy="78" r="18" fill="#FF1744" opacity="0.9" filter="url(#authenticShadow3)"/>
                          <circle cx="72" cy="78" r="15" fill="none" stroke="#FFFFFF" stroke-width="1"/>
                          <circle cx="96" cy="96" r="8" fill="#FF69B4" opacity="0.8"/>
                          <line x1="40" y1="100" x2="70" y2="100" stroke="#FFB6C1" stroke-width="1" stroke-dasharray="2,2"/>
                        </svg>
                    </div>
                    <div class="icon-meta">
                        <div class="icon-name">印章认证风格</div>
                        <div class="icon-style">传统与现代结合</div>
                        <div class="icon-description">采用传统文档+印章的设计语言，水印背景增强安全感，红色印章体现权威性，签名线增加仪式感。</div>
                        <div class="icon-features">
                            <span class="feature-tag">传统印章</span>
                            <span class="feature-tag">水印背景</span>
                            <span class="feature-tag">仪式感</span>
                            <span class="feature-tag">权威性</span>
                        </div>
                    </div>
                </div>

                <!-- 方案4 -->
                <div class="icon-showcase">
                    <div class="icon-container">
                        <svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient id="brandGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FF8A80"/>
                              <stop offset="25%" stop-color="#FFAB91"/>
                              <stop offset="75%" stop-color="#FFCC9A"/>
                              <stop offset="100%" stop-color="#FFE0B2"/>
                            </linearGradient>
                            <linearGradient id="premiumCard4" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FFFFFF"/>
                              <stop offset="50%" stop-color="#FAFAFA"/>
                              <stop offset="100%" stop-color="#F5F5F5"/>
                            </linearGradient>
                            <filter id="brandShadow4">
                              <feDropShadow dx="5" dy="7" stdDeviation="15" flood-opacity="0.18"/>
                            </filter>
                            <linearGradient id="roseGold4" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FFD700"/>
                              <stop offset="50%" stop-color="#FF69B4"/>
                              <stop offset="100%" stop-color="#FF1744"/>
                            </linearGradient>
                          </defs>
                          <rect width="144" height="144" rx="32" fill="url(#brandGradient4)" filter="url(#brandShadow4)"/>
                          <rect x="20" y="28" width="104" height="88" rx="16" fill="url(#premiumCard4)" filter="url(#brandShadow4)"/>
                          <rect x="20" y="28" width="104" height="88" rx="16" fill="none" stroke="url(#roseGold4)" stroke-width="3"/>
                          <circle cx="52" cy="72" r="16" fill="#FFE4E6" stroke="#FF69B4" stroke-width="2"/>
                          <circle cx="92" cy="72" r="16" fill="#E3F2FD" stroke="#2196F3" stroke-width="2"/>
                          <path d="M72 60 C70 58, 67 58, 67 61 C67 64, 72 69, 72 69 C72 69, 77 64, 77 61 C77 58, 74 58, 72 60" fill="#FF1744"/>
                        </svg>
                    </div>
                    <div class="icon-meta">
                        <div class="icon-name">情侣对称设计</div>
                        <div class="icon-style">高端品牌风</div>
                        <div class="icon-description">左右对称的情侣头像设计，玫瑰金边框彰显奢华品质，中央心形连接象征爱情纽带，体现品牌的高端定位。</div>
                        <div class="icon-features">
                            <span class="feature-tag">对称美学</span>
                            <span class="feature-tag">玫瑰金</span>
                            <span class="feature-tag">高端品牌</span>
                            <span class="feature-tag">情侣主题</span>
                        </div>
                    </div>
                </div>

                <!-- 方案5 -->
                <div class="icon-showcase">
                    <div class="icon-container">
                        <svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
                          <defs>
                            <linearGradient id="appleGradient5" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#F8F9FA"/>
                              <stop offset="50%" stop-color="#E9ECEF"/>
                              <stop offset="100%" stop-color="#DEE2E6"/>
                            </linearGradient>
                            <linearGradient id="glassCard5" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FFFFFF" stop-opacity="0.95"/>
                              <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0.85"/>
                            </linearGradient>
                            <filter id="modernShadow5">
                              <feDropShadow dx="0" dy="8" stdDeviation="20" flood-opacity="0.12"/>
                            </filter>
                            <linearGradient id="accentColor5" x1="0%" y1="0%" x2="100%" y2="100%">
                              <stop offset="0%" stop-color="#FF6B6B"/>
                              <stop offset="50%" stop-color="#FF8E8E"/>
                              <stop offset="100%" stop-color="#FFB3B3"/>
                            </linearGradient>
                          </defs>
                          <rect width="144" height="144" rx="40" fill="url(#appleGradient5)"/>
                          <rect x="24" y="32" width="96" height="80" rx="20" fill="url(#glassCard5)" filter="url(#modernShadow5)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
                          <circle cx="72" cy="60" r="20" fill="url(#accentColor5)" opacity="0.1"/>
                          <circle cx="66" cy="58" r="6" fill="none" stroke="url(#accentColor5)" stroke-width="2"/>
                          <circle cx="78" cy="58" r="6" fill="none" stroke="url(#accentColor5)" stroke-width="2"/>
                          <path d="M72 52 C70 50, 67 50, 67 53 C67 56, 72 61, 72 61 C72 61, 77 56, 77 53 C77 50, 74 50, 72 52" fill="url(#accentColor5)"/>
                        </svg>
                    </div>
                    <div class="icon-meta">
                        <div class="icon-name">现代极简风格</div>
                        <div class="icon-style">苹果设计语言</div>
                        <div class="icon-description">采用苹果设计语言，玻璃态卡片效果现代时尚，极简的情侣图标线条优美，底部状态指示器体现交互细节。</div>
                        <div class="icon-features">
                            <span class="feature-tag">极简主义</span>
                            <span class="feature-tag">玻璃态</span>
                            <span class="feature-tag">现代时尚</span>
                            <span class="feature-tag">苹果风格</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="download-section">
                <h3 style="margin-bottom: 20px; font-size: 1.4rem;">🎁 专业设计包</h3>
                <p style="opacity: 0.9; margin-bottom: 25px;">
                    所有图标均为SVG矢量格式，144×144像素，支持无损缩放<br>
                    包含完整设计源文件，可自由修改颜色和细节
                </p>
                <button class="download-btn">下载设计源文件</button>
                <button class="download-btn">获取品牌指南</button>
                <button class="download-btn">导出PNG格式</button>
            </div>
        </div>
    </div>

    <script>
        // 增强交互效果
        document.querySelectorAll('.name-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.name-item').forEach(i => i.style.transform = '');
                this.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 200);
            });
        });

        document.querySelectorAll('.download-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.innerHTML = '✓ 已复制';
                setTimeout(() => {
                    this.innerHTML = this.innerHTML.replace('✓ 已复制', '下载设计源文件');
                }, 1500);
            });
        });
    </script>
</body>
</html> 