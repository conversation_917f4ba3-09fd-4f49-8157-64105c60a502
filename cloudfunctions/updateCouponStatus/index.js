/**
 * 券状态更新云函数
 * 提供统一的券状态更新接口，支持多种操作类型
 */

const cloud = require('wx-server-sdk');

// 初始化云环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 * @param {Object} event 云函数调用参数
 * @param {string} event.couponId 券ID
 * @param {string} event.action 操作类型 (receive|redeem|approve|reject|cancel)
 * @param {string} event.note 操作备注（可选）
 * @param {Object} context 云函数运行上下文
 */
exports.main = async (event, context) => {
  console.log('开始执行券状态更新云函数:', event);
  
  try {
    const {
      couponId,
      action,
      note = '',
      rejectReason = ''
    } = event;

    // 参数验证
    if (!couponId || !action) {
      throw new Error('缺少必要参数: couponId 或 action');
    }

    // 验证操作类型
    const validActions = ['redeem', 'approve', 'reject'];
    if (!validActions.includes(action)) {
      throw new Error(`无效的操作类型: ${action}`);
    }

    // 获取微信调用上下文
    const wxContext = cloud.getWXContext();
    const { OPENID } = wxContext;
    
    if (!OPENID) {
      throw new Error('无法获取用户 OpenID');
    }

    // 获取券信息
    const couponQuery = await db.collection('coupons').doc(couponId).get();
    if (!couponQuery.data) {
      throw new Error('券不存在');
    }

    const coupon = couponQuery.data;
    const currentTime = new Date();

    // 检查券是否已过期（除了取消操作）
    if (action !== 'cancel' && coupon.expiryDate && new Date(coupon.expiryDate) <= currentTime) {
      throw new Error('券已过期，无法执行此操作');
    }

    // 权限验证和状态验证
    let updateData = {
      updateTime: currentTime
    };
    let resultMessage = '';

    switch (action) {
      case 'redeem':
        // 申请兑现：只有接收方可以操作，券状态必须是 received
        if (coupon.recipientId !== OPENID) {
          throw new Error('只有券的接收方可以申请兑现');
        }
        if (coupon.status !== 'received') {
          throw new Error(`券当前状态为 ${coupon.status}，无法申请兑现`);
        }
        updateData.status = 'pending_redeem';
        updateData.redeemApplyTime = currentTime;
        updateData.redeemNote = note;
        resultMessage = '兑现申请已提交';
        break;

      case 'approve':
        // 批准兑现：只有发送方可以操作，券状态必须是 pending_redeem
        if (coupon.creatorId !== OPENID) {
          throw new Error('只有券的发送方可以批准兑现');
        }
        if (coupon.status !== 'pending_redeem') {
          throw new Error(`券当前状态为 ${coupon.status}，无法批准兑现`);
        }
        updateData.status = 'redeemed';
        updateData.redeemTime = currentTime;
        updateData.approvalNote = note;
        resultMessage = '兑现已批准';
        break;

      case 'reject':
        // 拒绝兑现：只有发送方可以操作，券状态必须是 pending_redeem
        if (coupon.creatorId !== OPENID) {
          throw new Error('只有券的发送方可以拒绝兑现');
        }
        if (coupon.status !== 'pending_redeem') {
          throw new Error(`券当前状态为 ${coupon.status}，无法拒绝兑现`);
        }
        if (!rejectReason) {
          throw new Error('拒绝兑现必须提供拒绝原因');
        }
        updateData.status = 'received'; // 拒绝后状态回到已接收
        updateData.rejectTime = currentTime;
        updateData.rejectReason = rejectReason;
        resultMessage = '兑现已拒绝';
        break;

      default:
        throw new Error(`未支持的操作类型: ${action}`);
    }

    // 更新券状态
    await db.collection('coupons').doc(couponId).update({
      data: updateData
    });

    // 修复问题5：移除重复的通知发送逻辑
    // 通知发送由 updateNotificationStatus 云函数统一处理
    console.log('券状态更新完成，通知发送由 updateNotificationStatus 云函数处理');

    console.log(`券状态更新成功: ${couponId}, 操作: ${action}, 新状态: ${updateData.status}`);

    return {
      success: true,
      message: resultMessage,
      data: {
        couponId: couponId,
        action: action,
        oldStatus: coupon.status,
        newStatus: updateData.status,
        operatorId: OPENID,
        note: note,
        rejectReason: rejectReason,
        timestamp: currentTime.toISOString()
      }
    };

  } catch (error) {
    console.error('券状态更新云函数执行失败:', error);
    
    return {
      success: false,
      message: '操作失败',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}; 