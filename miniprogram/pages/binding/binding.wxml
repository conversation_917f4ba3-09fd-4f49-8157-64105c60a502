<!--pages/binding/binding.wxml-->
<view class="container" style="background: {{themeGradient}};">
  <view class="binding-header card">
    <image src="/images/couple-hearts.svg" class="header-icon"/>
    <text class="header-title">绑定你的另一半</text>
    <text class="header-subtitle">分享爱与承诺，从绑定开始</text>
  </view>

  <view class="tab-navigation">
    <view class="tab-item {{activeTab === 'enterCode' ? 'active' : ''}}" bindtap="switchTab" data-tab="enterCode">输入TA的码</view>
    <view class="tab-item {{activeTab === 'myCode' ? 'active' : ''}}" bindtap="switchTab" data-tab="myCode">我的邀请码</view>
  </view>

  <!-- Enter Partner's Code Section -->
  <view wx:if="{{activeTab === 'enterCode'}}" class="binding-section card">
    <text class="section-instruction">请输入你伴侣分享的邀请码：</text>
    <view class="input-group">
      <input 
        class="invite-code-input" 
        type="text" 
        value="{{inviteCode}}" 
        bindinput="onInviteCodeInput" 
        placeholder="输入邀请码"
        maxlength="6" 
        adjust-position="true"
        style="text-transform: uppercase;"
      />
      <button 
        class="primary-button bind-button" 
        bindtap="handleBindWithCode" 
        loading="{{isBinding}}" 
        disabled="{{isBinding || !inviteCode}}">
        {{isBinding ? '绑定中...' : '确认绑定'}}
      </button>
    </view>
    <text class="help-text">让你的伴侣在“我的邀请码”页面找到并分享给你。</text>
  </view>

  <!-- My Invite Code Section -->
  <view wx:if="{{activeTab === 'myCode'}}" class="binding-section card">
    <text class="section-instruction">分享你的邀请码给你的伴侣：</text>
    <view wx:if="{{isGeneratingCode && !myInviteCode}}" class="code-display-area loading-code">
        <view class="loading-spinner-global small"></view>
        <text>生成中...</text>
    </view>
    <view wx:if="{{!isGeneratingCode && myInviteCode}}" class="code-display-area">
      <text class="my-invite-code">{{myInviteCode}}</text>
      <view class="code-actions">
        <button class="secondary-button copy-code-btn" bindtap="copyMyCode">复制邀请码</button>
        <button class="primary-button share-code-btn" open-type="share">分享给TA</button>
      </view>
    </view>
    <view wx:if="!myInviteCode && !isGeneratingCode" class="code-display-area">
      <text class="help-text">邀请码生成失败，请稍后重试或</text>
      <button class="secondary-button refresh-code-btn" bindtap="generateMyInviteCode">重新生成</button>
    </view>
    <text class="help-text">邀请码有效期为24小时，请尽快使用哦。</text>
  </view>

  <view class="tips-section card">
    <text class="tips-title">绑定小贴士 💕</text>
    <view class="tip-item">1. 确保双方都已登录情侣信任券小程序。</view>
    <view class="tip-item">2. 一方生成邀请码，另一方输入该邀请码即可完成绑定。</view>
    <view class="tip-item">3. 绑定后，你们就可以互相发送和接收充满爱意的信任券啦！</view>
  </view>
</view>
