const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 标记所有通知为已读
 */
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID
  
  try {
    // 标记用户所有未读通知为已读 - 修复：使用recipientId字段
    const updateResult = await db.collection('notifications')
      .where({
        recipientId: openid,
        isRead: false
      })
      .update({
        data: {
          isRead: true,
          readTime: new Date()
        }
      })
    
    return {
      success: true,
      updatedCount: updateResult.stats.updated,
      message: `已标记 ${updateResult.stats.updated} 条通知为已读`
    }
    
  } catch (error) {
    console.error('标记所有通知已读失败:', error)
    return {
      success: false,
      error: 'SYSTEM_ERROR',
      message: '系统错误，请稍后重试'
    }
  }
} 