// settings.js
const app = getApp();

Page({
  data: {
    notificationSettings: {
      newCoupon: true,
      couponReceived: true,
      redeemRequest: true,
      couponRedeemed: true,
      expiryReminder: true
    },
    privacySettings: {
      analytics: true
    },
    appSettings: {
      autoUpdate: true
    },
    cacheSize: '0.0',
    appVersion: '1.0.0',
    phoneNumber: '',
    isLoading: false,
    loadingText: '',
    showConfirmModal: false,
    confirmModal: {
      title: '',
      message: ''
    },
    showPrivacyModal: false,
    currentAction: null
  },

  onLoad: function() {
    console.log('设置页面加载');
    this.loadSettings();
    this.getCacheSize();
  },

  /**
   * 加载设置数据
   */
  loadSettings() {
    this.setData({ isLoading: true, loadingText: '加载设置中...' });

    wx.cloud.callFunction({
      name: 'getUserSettings',
      data: {},
      success: (res) => {
        if (res.result.success) {
          const settings = res.result.data || {};
          this.setData({
            notificationSettings: {
              ...this.data.notificationSettings,
              ...settings.notificationSettings
            },
            privacySettings: {
              ...this.data.privacySettings,
              ...settings.privacySettings
            },
            appSettings: {
              ...this.data.appSettings,
              ...settings.appSettings
            },
            phoneNumber: settings.phoneNumber || '',
            isLoading: false
          });
        } else {
          this.setData({ isLoading: false });
        }
      },
      fail: (error) => {
        console.error('加载设置失败:', error);
        this.setData({ isLoading: false });
      }
    });
  },

  /**
   * 获取缓存大小
   */
  getCacheSize() {
    try {
      const size = (Math.random() * 10).toFixed(1); // 模拟缓存大小
      this.setData({ cacheSize: size });
    } catch (error) {
      console.error('获取缓存大小失败:', error);
    }
  },

  /**
   * 通知设置变化
   */
  onNotificationChange(e) {
    const type = e.currentTarget.dataset.type;
    const value = e.detail.value;
    
    console.log('通知设置变化:', type, value);

    this.setData({
      [`notificationSettings.${type}`]: value
    });

    this.saveSettings();
  },

  /**
   * 隐私设置变化
   */
  onPrivacyChange(e) {
    const type = e.currentTarget.dataset.type;
    const value = e.detail.value;
    
    console.log('隐私设置变化:', type, value);

    this.setData({
      [`privacySettings.${type}`]: value
    });

    this.saveSettings();
  },

  /**
   * 应用设置变化
   */
  onAppSettingsChange(e) {
    const type = e.currentTarget.dataset.type;
    const value = e.detail.value;
    
    console.log('应用设置变化:', type, value);

    this.setData({
      [`appSettings.${type}`]: value
    });

    this.saveSettings();
  },

  /**
   * 保存设置
   */
  saveSettings() {
    const settings = {
      notificationSettings: this.data.notificationSettings,
      privacySettings: this.data.privacySettings,
      appSettings: this.data.appSettings
    };

    wx.cloud.callFunction({
      name: 'saveUserSettings',
      data: { settings },
      success: (res) => {
        if (res.result.success) {
          console.log('设置保存成功');
        } else {
          console.error('设置保存失败:', res.result.message);
        }
      },
      fail: (error) => {
        console.error('设置保存失败:', error);
      }
    });
  },

  /**
   * 管理订阅消息
   */
  manageSubscribeMessages() {
    console.log('管理订阅消息');
    
    wx.requestSubscribeMessage({
      tmplIds: [
        'YOUR_NEW_COUPON_TEMPLATE_ID',
        'YOUR_COUPON_RECEIVED_TEMPLATE_ID',
        'YOUR_REDEEM_REQUEST_TEMPLATE_ID',
        'YOUR_COUPON_REDEEMED_TEMPLATE_ID',
        'YOUR_COUPON_EXPIRY_TEMPLATE_ID'
      ],
      success: (res) => {
        console.log('订阅消息设置结果:', res);
        let successCount = 0;
        Object.values(res).forEach(status => {
          if (status === 'accept') successCount++;
        });
        
        wx.showToast({
          title: `已订阅${successCount}个消息类型`,
          icon: 'success',
          duration: 2000
        });
      },
      fail: (error) => {
        console.error('订阅消息设置失败:', error);
        wx.showToast({
          title: '设置失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 查看隐私政策
   */
  viewPrivacyPolicy() {
    console.log('查看隐私政策');
    this.setData({ showPrivacyModal: true });
  },

  /**
   * 查看用户协议
   */
  viewUserAgreement() {
    console.log('查看用户协议');
    wx.showModal({
      title: '用户协议',
      content: '感谢您使用情侣信任券，详细的用户协议内容请访问官方网站查看。',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  /**
   * 清理缓存
   */
  clearCache() {
    console.log('清理缓存');
    
    this.showConfirm(
      '清理缓存',
      '确定要清理所有缓存数据吗？这可能会影响应用加载速度。',
      'clearCache'
    );
  },

  /**
   * 执行清理缓存
   */
  performClearCache() {
    this.setData({ isLoading: true, loadingText: '清理中...' });

    // 模拟清理缓存
    setTimeout(() => {
      this.setData({ 
        cacheSize: '0.0',
        isLoading: false
      });
      
      wx.showToast({
        title: '缓存清理完成',
        icon: 'success',
        duration: 2000
      });
    }, 1500);
  },

  /**
   * 检查更新
   */
  checkUpdate() {
    console.log('检查更新');
    
    this.setData({ isLoading: true, loadingText: '检查更新中...' });

    const updateManager = wx.getUpdateManager();
    
    updateManager.onCheckForUpdate((res) => {
      console.log('更新检查结果:', res);
      this.setData({ isLoading: false });
      
      if (res.hasUpdate) {
        wx.showModal({
          title: '发现新版本',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      } else {
        wx.showToast({
          title: '已是最新版本',
          icon: 'success',
          duration: 2000
        });
      }
    });

    updateManager.onUpdateReady(() => {
      console.log('新版本下载完成');
    });

    updateManager.onUpdateFailed(() => {
      console.error('新版本下载失败');
      this.setData({ isLoading: false });
      wx.showToast({
        title: '检查更新失败',
        icon: 'none',
        duration: 2000
      });
    });

    // 如果没有自动检查，手动触发
    setTimeout(() => {
      if (this.data.isLoading) {
        this.setData({ isLoading: false });
        wx.showToast({
          title: '已是最新版本',
          icon: 'success',
          duration: 2000
        });
      }
    }, 3000);
  },

  /**
   * 导出数据
   */
  exportData() {
    console.log('导出数据');
    
    this.setData({ isLoading: true, loadingText: '导出数据中...' });

    wx.cloud.callFunction({
      name: 'exportUserData',
      data: {},
      success: (res) => {
        this.setData({ isLoading: false });
        
        if (res.result.success) {
          wx.showModal({
            title: '数据导出成功',
            content: '您的数据已导出完成，可通过邮箱接收下载链接。',
            showCancel: false,
            confirmText: '我知道了'
          });
        } else {
          wx.showToast({
            title: '导出失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (error) => {
        console.error('导出数据失败:', error);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '导出失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 注销账号
   */
  deleteAccount() {
    console.log('注销账号');
    
    this.showConfirm(
      '注销账号',
      '⚠️ 警告：注销账号将永久删除您的所有数据，包括券记录、聊天记录等，此操作不可恢复！',
      'deleteAccount'
    );
  },

  /**
   * 执行注销账号
   */
  performDeleteAccount() {
    this.setData({ isLoading: true, loadingText: '注销中...' });

    wx.cloud.callFunction({
      name: 'deleteUserAccount',
      data: {},
      success: (res) => {
        this.setData({ isLoading: false });
        
        if (res.result.success) {
          wx.showModal({
            title: '账号注销成功',
            content: '您的账号已成功注销，感谢使用我们的服务。',
            showCancel: false,
            success: () => {
              // 清除本地数据并返回首页
              wx.clearStorageSync();
              wx.reLaunch({
                url: '/pages/index/index'
              });
            }
          });
        } else {
          wx.showToast({
            title: '注销失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      },
      fail: (error) => {
        console.error('注销账号失败:', error);
        this.setData({ isLoading: false });
        wx.showToast({
          title: '注销失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 绑定手机号
   */
  bindPhone() {
    console.log('绑定手机号');
    
    wx.showToast({
      title: '功能开发中',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 查看登录记录
   */
  viewLoginHistory() {
    console.log('查看登录记录');
    
    wx.showToast({
      title: '功能开发中',
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 显示确认弹窗
   */
  showConfirm(title, message, action) {
    this.setData({
      showConfirmModal: true,
      confirmModal: { title, message },
      currentAction: action
    });
  },

  /**
   * 隐藏确认弹窗
   */
  hideConfirmModal() {
    this.setData({
      showConfirmModal: false,
      currentAction: null
    });
  },

  /**
   * 确认操作
   */
  confirmAction() {
    const action = this.data.currentAction;
    this.hideConfirmModal();
    
    switch (action) {
      case 'clearCache':
        this.performClearCache();
        break;
      case 'deleteAccount':
        this.performDeleteAccount();
        break;
    }
  },

  /**
   * 隐藏隐私政策弹窗
   */
  hidePrivacyModal() {
    this.setData({ showPrivacyModal: false });
  },

  /**
   * 防止事件冒泡
   */
  preventClose() {
    // 空函数，防止事件冒泡
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '情侣信任券 - 记录我们的美好约定',
      path: '/pages/index/index'
    };
  }
});

