<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="voucherGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFE4E6"/>
      <stop offset="50%" stop-color="#FFB6C1"/>
      <stop offset="100%" stop-color="#FF69B4"/>
    </linearGradient>
    <linearGradient id="paperGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFFFFF"/>
      <stop offset="100%" stop-color="#FAFAFA"/>
    </linearGradient>
    <filter id="premiumShadow1">
      <feDropShadow dx="3" dy="5" stdDeviation="8" flood-opacity="0.25"/>
    </filter>
    <pattern id="securityPattern1" patternUnits="userSpaceOnUse" width="20" height="20">
      <rect width="20" height="20" fill="#FFF5F7"/>
      <circle cx="10" cy="10" r="1" fill="#FFB6C1" opacity="0.3"/>
    </pattern>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="72" cy="72" r="70" fill="url(#voucherGradient1)" filter="url(#premiumShadow1)"/>
  
  <!-- 券面主体 -->
  <rect x="20" y="40" width="104" height="64" rx="8" fill="url(#paperGradient1)" filter="url(#premiumShadow1)"/>
  
  <!-- 安全纹理背景 -->
  <rect x="20" y="40" width="104" height="64" rx="8" fill="url(#securityPattern1)" opacity="0.4"/>
  
  <!-- 券面边框 -->
  <rect x="20" y="40" width="104" height="64" rx="8" fill="none" stroke="#FF69B4" stroke-width="2"/>
  
  <!-- 左侧撕边设计 -->
  <path d="M20 45 L20 40 Q20 36, 24 36 L28 36 Q32 36, 32 40 L32 45 Z" fill="#FF69B4" opacity="0.8"/>
  <path d="M20 99 L20 104 Q20 108, 24 108 L28 108 Q32 108, 32 104 L32 99 Z" fill="#FF69B4" opacity="0.8"/>
  
  <!-- 标题区域 -->
  <rect x="28" y="50" width="88" height="8" rx="2" fill="#FF69B4"/>
  <text x="72" y="65" text-anchor="middle" font-family="serif" font-size="10" font-weight="bold" fill="#FF69B4"></text>
  
  <!-- 双心图案 -->
  <path d="M60 75 C58 73, 55 73, 55 76 C55 79, 60 84, 60 84 C60 84, 65 79, 65 76 C65 73, 62 73, 60 75" fill="#FF69B4"/>
  <path d="M84 75 C82 73, 79 73, 79 76 C79 79, 84 84, 84 84 C84 84, 89 79, 89 76 C89 73, 86 73, 84 75" fill="#FF69B4"/>
  
  <!-- 连接线 -->
  <line x1="65" y1="78" x2="79" y2="78" stroke="#FF69B4" stroke-width="2" stroke-linecap="round"/>
  
  <!-- 印章效果 -->
  <circle cx="100" cy="90" r="8" fill="none" stroke="#FF69B4" stroke-width="1.5"/>
  <text x="100" y="94" text-anchor="middle" font-family="serif" font-size="6" fill="#FF69B4"></text>
</svg>
