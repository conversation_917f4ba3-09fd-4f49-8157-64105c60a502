/**
 * 更新通知状态云函数
 * 当券状态发生变化时，更新相关通知的显示状态
 */

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('开始执行更新通知状态云函数:', event);
  
  try {
    const { couponId, newCouponStatus, action } = event;
    
    if (!couponId || !newCouponStatus || !action) {
      throw new Error('缺少必要参数: couponId, newCouponStatus, action');
    }

    // 获取当前调用者的openid
    const { OPENID } = cloud.getWXContext();
    
    // 根据不同的动作处理通知状态
    let updateResult = {};
    
    switch (action) {
      case 'apply_redeem':
        updateResult = await handleApplyRedeem(couponId, OPENID);
        break;
      case 'approve_redeem':
        updateResult = await handleApproveRedeem(couponId, OPENID);
        break;
      case 'reject_redeem':
        updateResult = await handleRejectRedeem(couponId, OPENID);
        break;
      case 'expire_coupon':
        updateResult = await handleExpireCoupon(couponId);
        break;
      default:
        throw new Error('不支持的操作类型: ' + action);
    }

    return {
      success: true,
      message: '通知状态更新成功',
      data: updateResult
    };

  } catch (error) {
    console.error('更新通知状态失败:', error);
    return {
      success: false,
      message: error.message || '更新通知状态失败'
    };
  }
};

/**
 * 处理申请兑现的通知状态变化
 */
async function handleApplyRedeem(couponId, applicantId) {
  console.log('处理申请兑现的通知状态变化:', { couponId, applicantId });
  
  // 1. 修复问题3：不隐藏新券提醒，保留历史记录
  // 只更新券状态，不隐藏通知
  await db.collection('notifications')
    .where({
      couponId: couponId,
      type: 'new_coupon',
      recipientId: applicantId
    })
    .update({
      data: {
        couponStatus: 'pending_redeem',
        updateTime: new Date()
      }
    });

  // 2. 为申请人创建"等待兑现"通知
  const couponDoc = await db.collection('coupons').doc(couponId).get();
  const coupon = couponDoc.data;
  
  await db.collection('notifications').add({
    data: {
      title: '等待对方兑现',
      content: `您已申请兑现券：${coupon.title}，等待对方处理`,
      type: 'redeem_request',
      subType: 'waiting_approval',
      recipientId: applicantId,
      senderId: coupon.creatorId,
      couponId: couponId,
      isRead: false,
      createTime: new Date(),
      notificationStatus: 'active',
      couponStatus: 'pending_redeem',
      userRole: 'applicant'
    }
  });

  // 3. 为券创建者创建"收到申请"通知
  await db.collection('notifications').add({
    data: {
      title: '收到兑现申请',
      content: `对方申请兑现券：${coupon.title}`,
      type: 'redeem_request',
      subType: 'received_application',
      recipientId: coupon.creatorId,
      senderId: applicantId,
      couponId: couponId,
      isRead: false,
      createTime: new Date(),
      notificationStatus: 'active',
      couponStatus: 'pending_redeem',
      userRole: 'approver'
    }
  });

  return { action: 'apply_redeem', couponId, applicantId };
}

/**
 * 处理同意兑现的通知状态变化
 */
async function handleApproveRedeem(couponId, approverId) {
  console.log('处理同意兑现的通知状态变化:', { couponId, approverId });
  
  const couponDoc = await db.collection('coupons').doc(couponId).get();
  const coupon = couponDoc.data;
  
  // 1. 修复问题3：不隐藏审批人的兑现申请通知，保留历史记录
  // 只更新券状态，不隐藏通知
  await db.collection('notifications')
    .where({
      couponId: couponId,
      type: 'redeem_request',
      recipientId: approverId,
      userRole: 'approver'
    })
    .update({
      data: {
        couponStatus: 'redeemed',
        updateTime: new Date()
      }
    });

  // 2. 修复问题3：不隐藏申请人的等待通知，保留历史记录
  // 只更新券状态，不隐藏通知
  await db.collection('notifications')
    .where({
      couponId: couponId,
      type: 'redeem_request',
      recipientId: coupon.recipientId,
      userRole: 'applicant'
    })
    .update({
      data: {
        couponStatus: 'redeemed',
        updateTime: new Date()
      }
    });

  // 3. 为审批人创建"已兑现"通知
  await db.collection('notifications').add({
    data: {
      title: '您已兑现爱人申请的券',
      content: `您已成功兑现券：${coupon.title}`,
      type: 'redeem_result',
      subType: 'processed_by_me',
      recipientId: approverId,
      senderId: coupon.recipientId,
      couponId: couponId,
      isRead: false,
      createTime: new Date(),
      notificationStatus: 'active',
      couponStatus: 'redeemed',
      userRole: 'processor'
    }
  });

  // 4. 为申请人创建"兑现成功"通知
  await db.collection('notifications').add({
    data: {
      title: '已成功兑现这张券',
      content: `您的券：${coupon.title} 已成功兑现`,
      type: 'redeem_result',
      subType: 'approved_for_me',
      recipientId: coupon.recipientId,
      senderId: approverId,
      couponId: couponId,
      isRead: false,
      createTime: new Date(),
      notificationStatus: 'active',
      couponStatus: 'redeemed',
      userRole: 'beneficiary'
    }
  });

  return { action: 'approve_redeem', couponId, approverId };
}

/**
 * 处理拒绝兑现的通知状态变化
 */
async function handleRejectRedeem(couponId, rejectorId) {
  console.log('处理拒绝兑现的通知状态变化:', { couponId, rejectorId });
  
  const couponDoc = await db.collection('coupons').doc(couponId).get();
  const coupon = couponDoc.data;
  
  // 1. 修复问题3：不隐藏审批人的兑现申请通知，保留历史记录
  // 只更新券状态，不隐藏通知
  await db.collection('notifications')
    .where({
      couponId: couponId,
      type: 'redeem_request',
      recipientId: rejectorId,
      userRole: 'approver'
    })
    .update({
      data: {
        couponStatus: 'received', // 拒绝后恢复为待兑现状态
        updateTime: new Date()
      }
    });

  // 2. 修复问题3：不隐藏申请人的等待通知，保留历史记录
  // 只更新券状态，不隐藏通知
  await db.collection('notifications')
    .where({
      couponId: couponId,
      type: 'redeem_request',
      recipientId: coupon.recipientId,
      userRole: 'applicant'
    })
    .update({
      data: {
        couponStatus: 'received', // 拒绝后恢复为待兑现状态
        updateTime: new Date()
      }
    });

  // 3. 恢复申请人的新券提醒，同时更新券状态
  await db.collection('notifications')
    .where({
      couponId: couponId,
      type: 'new_coupon',
      recipientId: coupon.recipientId
    })
    .update({
      data: {
        notificationStatus: 'active',
        couponStatus: 'received', // 拒绝后恢复为待兑现状态
        updateTime: new Date()
      }
    });

  return { action: 'reject_redeem', couponId, rejectorId };
}

/**
 * 处理券过期的通知状态变化 - 修复问题4
 */
async function handleExpireCoupon(couponId) {
  console.log('处理券过期的通知状态变化:', { couponId });

  // 1. 更新所有相关通知的券状态为过期
  await db.collection('notifications')
    .where({
      couponId: couponId,
      notificationStatus: 'active'
    })
    .update({
      data: {
        couponStatus: 'expired',
        updateTime: new Date()
      }
    });

  // 2. 查找券信息，用于创建过期通知
  const couponDoc = await db.collection('coupons').doc(couponId).get();
  if (couponDoc.data) {
    const coupon = couponDoc.data;

    // 3. 为券的接收方创建过期提醒通知
    await db.collection('notifications').add({
      data: {
        title: '券已过期',
        content: `您的券「${coupon.name || coupon.title}」已过期`,
        type: 'coupon_expired',
        recipientId: coupon.recipientId,
        senderId: 'system',
        couponId: couponId,
        isRead: false,
        createTime: new Date(),
        notificationStatus: 'active',
        couponStatus: 'expired',
        userRole: 'recipient'
      }
    });

    // 4. 为券的创建方创建过期提醒通知
    await db.collection('notifications').add({
      data: {
        title: '您发送的券已过期',
        content: `您发送的券「${coupon.name || coupon.title}」已过期`,
        type: 'coupon_expired',
        recipientId: coupon.creatorId,
        senderId: 'system',
        couponId: couponId,
        isRead: false,
        createTime: new Date(),
        notificationStatus: 'active',
        couponStatus: 'expired',
        userRole: 'creator'
      }
    });
  }

  return { action: 'expire_coupon', couponId };
}