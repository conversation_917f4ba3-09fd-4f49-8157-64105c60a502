<!-- 方案3：卡片设计 - 简约风格 -->
<svg width="144" height="144" viewBox="0 0 144 144" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <!-- <stop offset="0%" stop-color="#F8BBD9"/> -->
      <!-- <stop offset="100%" stop-color="#E1BEE7"/> -->
      <stop offset="0%" stop-color="#FF69B4"/>
      <stop offset="50%" stop-color="#FFB6C1"/>
      <stop offset="100%" stop-color="#FFC0CB"/>
    </linearGradient>
    <filter id="shadow3">
      <feDropShadow dx="3" dy="3" stdDeviation="5" flood-opacity="0.15"/>
    </filter>
  </defs>
  <!-- 背景 -->
  <circle cx="72" cy="72" r="70" fill="url(#gradient3)" filter="url(#shadow3)"/>
  <!-- 卡片主体 -->
  <rect x="32" y="45" width="80" height="54" rx="8" fill="#FFFFFF" filter="url(#shadow3)"/>
  <!-- 卡片装饰线 -->
  <line x1="45" y1="60" x2="99" y2="60" stroke="#FFB6C1" stroke-width="2" stroke-linecap="round"/>
  <line x1="45" y1="72" x2="85" y2="72" stroke="#FFB6C1" stroke-width="2" stroke-linecap="round"/>
  <line x1="45" y1="84" x2="75" y2="84" stroke="#FFB6C1" stroke-width="2" stroke-linecap="round"/>
  <!-- 右上角心形 -->
  <path d="M95 52 C93 50, 90 50, 90 53 C90 56, 95 60, 95 60 C95 60, 100 56, 100 53 C100 50, 97 50, 95 52" 
        fill="#FF69B4"/>
</svg>
