/* 骨架屏样式 */
.skeleton-container {
  padding: 0 24rpx;
  background: transparent;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-line,
.skeleton-avatar,
.skeleton-button,
.skeleton-tab,
.skeleton-card {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
}

/* 头部骨架 */
.skeleton-header {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  backdrop-filter: blur(10rpx);
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
}

.skeleton-greeting {
  flex: 1;
  margin-right: 24rpx;
}

.skeleton-line-main {
  height: 36rpx;
  width: 200rpx;
  margin-bottom: 12rpx;
}

.skeleton-line-sub {
  height: 24rpx;
  width: 160rpx;
}

.skeleton-button {
  width: 120rpx;
  height: 64rpx;
  border-radius: 32rpx;
}

/* 标签页骨架 */
.skeleton-tabs {
  display: flex;
  gap: 24rpx;
  margin-bottom: 24rpx;
  padding: 0 24rpx;
}

.skeleton-tab {
  flex: 1;
  height: 48rpx;
  border-radius: 24rpx;
}

/* 卡片列表骨架 */
.skeleton-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0 24rpx;
}

.skeleton-card {
  height: 397rpx;
  border-radius: 24rpx;
  padding: 28rpx 32rpx;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(255, 182, 193, 0.3);
}

.skeleton-card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.skeleton-card-title {
  height: 32rpx;
  width: 80%;
  margin-bottom: 16rpx;
}

.skeleton-card-desc {
  height: 24rpx;
  width: 100%;
  margin-bottom: 8rpx;
}

.skeleton-card-desc.short {
  width: 60%;
}

.skeleton-card-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
}

.skeleton-card-meta {
  height: 20rpx;
  width: 80rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .skeleton-card {
    height: 360rpx;
    padding: 24rpx 28rpx;
  }
  
  .skeleton-card-title {
    height: 28rpx;
  }
  
  .skeleton-card-desc {
    height: 20rpx;
  }
}
