# 情侣信誉券小程序 MVP UI 设计文档

本文档基于情侣信誉券小程序 MVP 原型设计参考建议，为 MVP 阶段的 UI 设计提供详细规范和描述。旨在产出一套简洁、温馨且具有良好情感连接的界面，指导后续的视觉稿制作和前端开发。

## 1. 整体视觉风格定义

### 1.1 视觉感受

整体视觉风格定义为 **温馨、简约、有爱**。强调情侣间的亲密感和互动乐趣，避免过于商业化或复杂的视觉元素。

### 1.2 色彩搭配方案

色彩方案旨在营造温馨浪漫的氛围，同时保证信息传达的清晰度。

*   **主色 (Primary Color):** `#FF6D96` (温暖的粉色系，象征浪漫、甜蜜)
    *   应用场景：核心操作按钮（如创建、绑定）、重要状态提示（如待使用券背景/边框）、品牌元素。
*   **辅色 (Secondary Color):** `#FFE4E1` (淡雅的浅粉色，与主色形成深浅对比)
    *   应用场景：背景填充、分割线、辅助元素的底色。
*   **点缀色 (Accent Color):** `#FFD700` (少量使用，用于突出强调)
    *   应用场景：特定状态图标（如兑换成功）、重要提醒文字、活动标记。
*   **中性色 (Neutral Colors):**
    *   `#333333` (深灰色，用于主要文本、标题，保证可读性)
    *   `#666666` (中灰色，用于辅助文本、描述信息)
    *   `#CCCCCC` (浅灰色，用于边框、分割线、禁用状态的元素)
    *   `#FFFFFF` (白色，用于背景、卡片、可点击区域)

### 1.3 字体选择与排版规范

选择易读且带有一定人情味的字体，保持统一的排版规范。

*   **字体家族:** 优先使用小程序推荐的系统字体（如 PingFang SC, Arial, sans-serif），保证跨设备兼容性和加载速度。在特定需要强调情感的地方，可考虑少量使用简洁可爱的衬线体（需评估字体文件大小和授权）。MVP 阶段建议优先使用系统字体以保障性能。

*   **字号 (PX):**
    *   **标题 (H1/Page Title):** 20px (加粗)
    *   **列表标题/重要名称 (H2):** 16px (加粗)
    *   **正文/普通信息 (Body):** 14px
    *   **辅助信息/描述 (Caption):** 12px
    *   **按钮内文字 (Button Text):** 16px (加粗)

*   **行高 (Line Height):**
    *   标题：1.2 - 1.4 倍字号
    *   正文/描述：1.5 - 1.6 倍字号

*   **字重 (Font Weight):**
    *   标题：Bold
    *   正文、辅助信息：Regular
    *   按钮文字：Bold

### 1.4 图标风格

图标风格采用 **线性 (Outline)** 风格，简洁明快，与整体简约风格一致。描边粗细适中，保持辨识度。必要时可填充点缀色，但以线性为主。

*   常用图标举例：设置、历史、心形、加号、日期、用户头像占位符、删除、绑定、解绑等。

## 2. 关键页面高保真视觉设计描述

以下为 MVP 原型设计参考建议中提及的关键页面的详细视觉设计描述。

### 2.1 首页/券列表页

*   **页面布局结构:**
    *   采用上下布局：顶部导航区固定顶部，券列表区占据下方大部分空间，底部导航栏固定底部。
    *   券列表采用垂直方向的列表展示，每个列表项为一个独立的卡片区域。
    *   整体采用 12 列或 24 列的栅格系统进行布局，确保元素水平对齐和间距的统一。主要内容区域左右留白 20-30px。

*   **色彩和字体的具体应用:**
    *   顶部导航栏背景使用浅粉色 `#FFE4E1`，标题文字使用深灰色 `#333333`，伴侣名称可以使用主色 `#FF6D96`。
    *   券列表卡片背景色为白色 `#FFFFFF`，有轻微阴影效果提升层次感。
    *   券名称使用 16px 加粗的深灰色 `#333333`。
    *   状态、创建人、有效期等辅助信息使用 12px 的中灰色 `#666666`。
    *   **区分券状态:**
        *   **待使用:** 卡片边框使用主色 `#FF6D96`，状态文字使用主色加粗。
        *   **已使用/已核销:** 卡片边框使用浅灰色 `#CCCCCC` 或中灰色 `#666666`，状态文字使用中灰色。
        *   **已过期:** 卡片边框使用浅灰色 `#CCCCCC` 并可能带有斜线水印，状态文字使用中灰色并带有删除线。
    *   底部导航栏背景色使用浅粉色 `#FFE4E1`，图标和文字默认为中灰色 `#666666`，当前选中项图标和文字使用主色 `#FF6D96` 并加粗。

*   **图标和图片的样式与占位:**
    *   顶部可能展示伴侣的圆角头像 (占位图使用中灰色圆形区域)。
    *   券列表项左侧或右侧可根据券状态或创建人显示小型图标（如心形、勾选标记、时间标记），图标采用线性风格，颜色根据状态调整。

*   **元素间距与对齐方式:**
    *   顶部导航栏左右内边距 20px。
    *   券列表项垂直间距 10-15px。
    *   卡片内部文字、图标等左对齐，内边距 15-20px。
    *   底部导航栏图标文字等宽间距水平分布，垂直居中对齐。

*   **重要的视觉反馈:**
    *   **列表项点击效果:** 点击券列表项时，卡片背景短暂变灰或有轻微的缩放动画。
    *   **加载状态:** 页面首次加载或下拉刷新时，顶部出现线性或圆形的加载指示器（颜色使用主色），列表区域可显示骨架屏占位。

### 2.2 创建券页面

*   **页面布局结构:**
    *   标准的表单布局：顶部导航栏标题，下方为垂直堆叠的表单项（标签 + 输入框/选择器），底部为提交按钮。
    *   每个表单项及其标签独占一行，输入框与标签垂直上下或水平对齐（推荐垂直对齐以节省横向空间）。
    *   整体布局左右留白 20-30px。

*   **色彩和字体的具体应用:**
    *   页面背景色使用淡雅的浅粉色 `#FFE4E1` 或白色 `#FFFFFF`。
    *   顶部导航栏背景使用浅粉色 `#FFE4E1`，标题“创建信誉券”使用深灰色 `#333333`。
    *   表单项标签文字使用 14px 的中灰色 `#666666`。
    *   输入框/选择器背景为白色 `#FFFFFF`，带有浅灰色 `#CCCCCC` 边框。输入文本使用 14px 深灰色 `#333333`。占位符文本使用浅灰色 `#CCCCCC`。
    *   创建按钮背景使用主色 `#FF6D96`，文字使用 18px 加粗的白色 `#FFFFFF`。
    *   错误提示文字使用醒目的提醒色（如红色 `#DC3545`），字号 12px。

*   **图标和图片的样式与占位:**
    *   有效期选择器可能带有日历图标。
    *   所属伴侣选择器可能带有下拉箭头或用户头像占位符。
    *   图标采用线性风格，颜色使用中灰色 `#666666`。

*   **元素间距与对齐方式:**
    *   表单项之间垂直间距 20-25px。
    *   标签与输入框之间垂直间距 5-10px。
    *   输入框内部文本左对齐，有 10-15px 内左边距。
    *   创建按钮宽度充满主要内容区域（左右留白一致），高度 40-45px。

*   **重要的视觉反馈:**
    *   **输入框聚焦状态:** 输入框被点击时，边框颜色变为醒目的颜色（如主色 `#FF6D96`）或有轻微的外部光晕效果。
    *   **表单验证错误:** 提交时如有必填项未填写或格式错误，对应的输入框边框变红，下方显示错误提示文字。
    *   **按钮点击效果:** 点击创建按钮时，按钮颜色短暂变深或有按压效果。
    *   **创建成功/失败提示:** 提交后页面上方出现非模态的提示条（Toast）显示“创建成功”或“创建失败，请重试”。

### 2.3 券详情页

*   **页面布局结构:**
    *   顶部导航栏标题为券名称。
    *   核心区域展示券的详细信息，信息条目垂直堆叠。
    *   下方为操作按钮（如“立即核销”）。
    *   信息区和操作按钮区左右保持 20-30px 留白。

*   **色彩和字体的具体应用:**
    *   页面背景色使用淡雅的浅粉色 `#FFE4E1` 或白色 `#FFFFFF`。
    *   顶部导航栏背景使用浅粉色 `#FFE4E1`，标题券名称使用深灰色 `#333333`。
    *   券名称在详情页使用 18px 加粗的深灰色 `#333333`， prominent displayed。
    *   描述文字使用 12px 中灰色 `#666666`，行高稍大以保证可读性。
    *   状态、创建人、有效期等信息标签（如“状态:”）使用 14px 中灰色 `#666666`，对应的值使用 14px 深灰色 `#333333`。
    *   “立即核销”按钮背景色在“待使用”状态下使用暖绿色 `#28A745`（突出“完成”、“兑换成功”的概念，与粉色系形成视觉区分），文字使用 18px 加粗白色 `#FFFFFF`。
    *   如果券已核销或过期，核销按钮隐藏或变灰禁用，页面可显示状态水印或标记。

*   **图标和图片的样式与占位:**
    *   详情信息旁可根据需要添加线性小图标（如状态图标、日期图标），颜色与对应文字颜色一致。

*   **元素间距与对齐方式:**
    *   券名称与下方描述信息垂直间距 15-20px。
    *   信息条目之间垂直间距 10-15px。
    *   信息区与操作按钮区垂直间距 30-40px。
    *   核销按钮宽度充满主要内容区域，高度 40-45px。

*   **重要的视觉反馈:**
    *   **按钮点击效果:** 点击核销按钮时，颜色短暂变深或有按压效果。
    *   **核销确认弹窗:** 弹窗背景蒙层为半透明深色，弹窗主体背景为白色，标题和提示文字使用深灰色，确认按钮使用暖绿色 `#28A745`，取消按钮使用中灰色 `#666666` 或无背景的文本按钮。弹窗弹出时有渐入或缩放动画。
    *   **核销成功/失败:** 核销操作完成后，页面上方出现提示条（Toast）。

### 2.4 伴侣管理页

*   **页面布局结构:**
    *   顶部导航栏标题“伴侣管理”。
    *   根据用户是否已绑定，显示“已绑定伴侣信息区”或“邀请/绑定区”。两个区域不会同时显示。
    *   区域内部元素垂直或水平布局。

*   **色彩和字体的具体应用:**
    *   页面背景色使用淡雅的浅粉色 `#FFE4E1` 或白色 `#FFFFFF`。
    *   顶部导航栏背景使用浅粉色 `#FFE4E1`，标题“伴侣管理”使用深灰色 `#333333`。
    *   **已绑定状态:** 伴侣信息卡片背景白色 `#FFFFFF`。伴侣名称使用 16px 加粗深灰色 `#333333`。状态文字“已绑定”使用 12px 中灰色 `#666666`。解绑按钮背景使用警告红色 `#DC3545`，文字白色。
    *   **未绑定状态:**
        *   邀请码/输入码区域背景白色 `#FFFFFF`。
        *   提示文字（如“我的邀请码:”、“输入伴侣提供的邀请码:”）使用 14px 中灰色 `#666666`。
        *   生成的邀请码文字使用 18px 加粗的主色 `#FF6D96`。
        *   邀请码输入框类似创建券页面的输入框样式。
        *   绑定按钮背景使用主色 `#FF6D96`，文字白色。

*   **图标和图片的样式与占位:**
    *   伴侣头像占位图使用中灰色圆形区域，实际显示伴侣用户上传的头像。
    *   可能添加复制邀请码图标（线性风格）。

*   **元素间距与对齐方式:**
    *   已绑定伴侣信息卡片内部，头像、名称、状态、按钮水平或垂直对齐，合理分配空间。
    *   邀请/绑定区域，各元素垂直堆叠，间距 15-20px。
    *   按钮宽度充满主要内容区域，高度 40-45px。

*   **重要的视觉反馈:**
    *   **解绑/绑定确认弹窗:** 类似核销确认弹窗，解绑确认强调风险（红色按钮），绑定确认强调成功（主色按钮）。
    *   **邀请码生成/输入验证反馈:** 生成邀请码后清晰展示，输入邀请码时有输入框聚焦和验证提示。
    *   **绑定/解绑成功提示:** 操作完成后页面上方出现提示条（Toast）。

### 2.5 历史记录页

*   **页面布局结构:**
    *   顶部导航栏标题“历史记录”。
    *   下方为历史信誉券列表，结构类似首页列表，但强调历史状态和日期。

*   **色彩和字体的具体应用:**
    *   页面背景色使用淡雅的浅粉色 `#FFE4E1` 或白色 `#FFFFFF`。
    *   顶部导航栏背景使用浅粉色 `#FFE4E1`，标题“历史记录”使用深灰色 `#333333`。
    *   历史券列表项卡片背景白色 `#FFFFFF`，带有浅灰色边框 `#CCCCCC`。
    *   券名称 16px 加粗深灰色 `#333333`。
    *   状态（已核销/已过期）、创建人、核销/过期日期等信息使用 12px 中灰色 `#666666`。
    *   状态文字本身可根据状态使用不同颜色进行强调（如已核销使用中灰色，已过期可使用更浅的灰色或斜线）。

*   **图标和图片的样式与占位:**
    *   列表项左侧或右侧可根据已核销或已过期状态添加相应线性图标（如打勾表示核销，叉号或沙漏表示过期），颜色使用中灰色或浅灰色。

*   **元素间距与对齐方式:**
    *   列表项垂直间距 10-15px。
    *   卡片内部文字、图标左对齐，内边距 15-20px。

*   **重要的视觉反馈:**
    *   **列表项点击效果:** 点击历史券列表项可进入只读的券详情页，点击时卡片背景短暂变灰。
    *   **加载更多:** 列表滚动到底部时，下方出现加载指示器（Spinner），加载完成后消失或提示“无更多内容”。

## 3. 核心 UI 组件库规范

以下定义小程序中常用核心 UI 组件的样式和行为。

### 3.1 按钮 (Buttons)

旨在提供清晰的操作入口，并通过颜色、形状和状态区分优先级和功能。

*   **形状:** 默认圆角矩形 (border-radius 4-8px)。
*   **高度:** 统一 40-45px。
*   **文字:** 16px 加粗。

| 类型         | 背景色   | 文字色   | 边框色   | 状态    | 视觉表现                                  |
| :----------- | :------- | :------- | :------- | :------ | :---------------------------------------- |
| **主要按钮**   | `#FF6D96` | `#FFFFFF` | None     | 默认    | 背景主色，文字白色。                      |
|              | `#E56287` | `#FFFFFF` | None     | 点击    | 背景色变深，有按压效果。                  |
|              | `#FF9EB8` | `#FFFFFF` | None     | 禁用    | 背景色变浅，文字颜色不变或变浅，无点击效果。 |
| **次要按钮**   | `#FFFFFF` | `#FF6D96` | `#FF6D96` | 默认    | 背景白色，文字主色，主色边框。            |
|              | `#FFE4E1` | `#E56287` | `#E56287` | 点击    | 背景变浅，文字和边框颜色变深。             |
|              | `#FFFFFF` | `#FF9EB8` | `#FF9EB8` | 禁用    | 文字和边框颜色变浅，无点击效果。           |
| **文本按钮**   | None     | `#666666` | None     | 默认    | 无背景和边框，文字中灰色。                |
|              | None     | `#333333` | None     | 点击    | 文字颜色变深，可能有轻微背景变色。         |
|              | None     | `#CCCCCC` | None     | 禁用    | 文字颜色变浅，无点击效果。                |
| **图标按钮**   | None     | `#666666` | None     | 默认    | 线性图标，颜色中灰色。                    |
|              | None     | `#333333` | None     | 点击    | 图标颜色变深。                            |
|              | None     | `#CCCCCC` | None     | 禁用    | 图标颜色变浅。                            |

### 3.2 输入框 (Input Fields)

用于用户输入文本、数字等信息。

*   **形状:** 默认圆角矩形 (border-radius 4-8px)。
*   **高度:** 统一 35-40px。
*   **内边距:** 文本左侧有 10-15px 内边距。
*   **组件构成:** 可包含标签 (Label)、输入区域 (Input Area)、占位符 (Placeholder)、错误提示 (Error Message)。

| 元素         | 样式描述                                                                 | 状态        | 视觉表现                                                                                     |
| :----------- | :----------------------------------------------------------------------- | :---------- | :------------------------------------------------------------------------------------------- |
| **输入框背景** | `#FFFFFF`                                                                | 默认        | 白色背景，浅灰色边框 `#CCCCCC`。                                                             |
|              | `#FFFFFF`                                                                | 聚焦 (Focus)| 白色背景，边框颜色变为 `#FF6D96` 主色或有轻微外发光。                                          |
|              | `#FFFFFF`                                                                | 禁用 (Disabled)| 背景变浅或变灰，边框颜色 `#CCCCCC`，无输入光标。                                               |
|              | `#FFFFFF`                                                                | 错误 (Error)| 背景白色，边框颜色变为红色 `#DC3545`。                                                         |
| **标签文字**   | 14px 中灰色 `#666666`                                                    | 默认        |                                                                                              |
| **输入文本**   | 14px 深灰色 `#333333`                                                    | N/A         |                                                                                              |
| **占位符文字** | 14px 浅灰色 `#CCCCCC`                                                    | 默认        |                                                                                              |
| **错误提示文字** | 12px 红色 `#DC3545`                                                      | 错误        | 显示在输入框下方，左对齐。                                                                   |
| **不同类型**   | 文本 (text)、数字 (number)、日期选择器 (date picker)、多行文本域 (textarea: 高度可变) | N/A         | 根据类型可能附带键盘类型适配或额外的选择器弹窗。文本域无固定高度，随内容增长或设定最小高度。 |

### 3.3 标签 (Tabs) 和分段控制器 (Segmented Controls)

主要用于切换同级内容视图或筛选状态。MVP 阶段更多可能使用 Tabs 或简单的筛选按钮组。

*   **样式:** 底部导航栏即为一种 Tab Bar。页面内切换 Tab 可使用不带背景的 Tab 组件。
*   **Tab 组件:**
    *   背景：无背景或浅粉色 `#FFE4E1`。
    *   文字：默认状态 14px 中灰色 `#666666`。选中状态 14px 加粗主色 `#FF6D96`。
    *   底部指示条：选中 Tab 下方有主色 `#FF6D96` 的细线指示条。
    *   间距：Tab 之间等宽或固定水平间距。

*   **分段控制器 (Segmented Control - 相对 Tabs 更紧凑):** MVP 阶段可能用不上，但定义备用。
    *   形状：圆角矩形外框。
    *   背景：浅灰色 `#EEEEEE` 外框。
    *   内部：各段为一个按钮。
    *   文字：默认 14px 中灰色 `#666666`。选中状态 14px 加粗 深灰色 `#333333`。
    *   选中背景：选中项背景为白色 `#FFFFFF`，有轻微阴影或深色边框区分。

### 3.4 导航栏 (Navigation Bars)

*   **顶部导航栏 (Top Navigation Bar):**
    *   高度：小程序默认导航栏高度。
    *   背景色：浅粉色 `#FFE4E1`。
    *   标题：页面标题文字，居中显示，16px 加粗深灰色 `#333333`。
    *   返回按钮：左侧，线性图标，颜色深灰色 `#333333`。

*   **底部标签导航栏 (Bottom Tabs):**
    *   高度：小程序默认底部导航栏高度。
    *   背景色：浅粉色 `#FFE4E1`。
    *   图标和文字：默认状态图标和文字使用中灰色 `#666666`。选中状态图标和文字使用主色 `#FF6D96` 并加粗。图标使用线性风格。

### 3.5 列表项 (List Items)

用于展示列表数据，如券列表、历史记录列表。

*   **形状:** 默认圆角矩形卡片 (border-radius 4-8px)。
*   **背景:** `#FFFFFF`，轻微阴影。
*   **构成:** 包含左侧图标/占位、主要标题（券名称），下方辅助信息（状态、创建人、日期）。
*   **间距:** 列表项垂直间距 10-15px。卡片内部元素左右边距 15-20px。
*   **视觉区分:** 通过卡片边框颜色、状态文字颜色和图标来区分券的不同状态（待使用、已核销、已过期），参考 2.1 首页描述。

### 3.6 弹窗/模态框 (Alerts, Modals, Action Sheets)

用于向用户获取确认、展示重要信息或提供操作选项。

*   **背景蒙层:** 半透明深色 (如 `#000000` 透明度 40-60%)，覆盖下方内容，点击蒙层可关闭（需根据具体类型和场景判断）。
*   **弹窗主体 (Alert/Modal):**
    *   背景：白色 `#FFFFFF`。
    *   形状：圆角矩形。
    *   内容：标题（可选，16px 加粗深灰色 `#333333`），消息文字（14px 深灰色 `#333333`，行高 1.5），操作按钮。
    *   按钮布局：垂直堆叠或水平并排。取消按钮在中灰色或文本样式，确认/主要操作按钮使用主色 `#FF6D96` 或特定操作颜色（如核销用绿色，解绑用红色）。

*   **动作面板 (Action Sheet):** MVP 阶段可能用不上，但定义备用。
    *   从屏幕底部弹出。
    *   背景：白色 `#FFFFFF`。
    *   选项列表：每个选项一行，居中文本，16px 深灰色 `#333333` 或特定颜色（如删除选项用红色）。
    *   取消按钮：单独一行，有一定间隔，样式类似普通按钮或特殊处理。

*   **动画:** 弹出和关闭时有渐入渐出或从底部/中心缩放的动画效果。

### 3.7 加载指示器 (Loading Indicators)

用于表示后台操作正在进行，避免用户误以为卡死。

*   **类型:**
    *   **Spinner (活动指示器):** 圆形转动动画，颜色使用主色 `#FF6D96`。常用于页面加载、按钮点击提交时。
    *   **Progress Bar (进度条):** MVP 阶段不太可能需要精确进度条。
*   **位置:** 可在页面顶部（全局加载）、按钮内部、列表底部（加载更多）、特定区域中心。背景可透明或半透明。

### 3.8 选择器 (Pickers, Selects)

用于从预设选项中选择，如日期选择。

*   **触发:** 通过点击一个输入框或按钮触发底部的选择器模态框。
*   **模态框:** 从底部弹出，包含标题、确认/取消按钮、选择滚轮/控件。
*   **样式:**
    *   背景：白色 `#FFFFFF`。
    *   标题：16px 深灰色 `#333333`。
    *   按钮：确认使用主色 `#FF6D96`，取消使用中灰色 `#666666`。
    *   选择控件：使用小程序原生日期选择器或其他自定义滚轮样式，选中项有明显视觉标记。

### 3.9 开关 (Switches)

用于开启/关闭某个选项或功能。MVP 阶段可能在设置页用到。

*   **形状:** 标准的滑动开关样式。
*   **颜色:** 关闭状态轨道为浅灰色 `#CCCCCC`，滑块为白色。开启状态轨道为暖绿色 `#28A745`，滑块为白色。

### 3.10 头像和占位图 (Avatars and Placeholders)

用于展示用户头像，或在图片加载前、无图片时作为替代。

*   **形状:** 圆形 (border-radius 50%) 或圆角矩形。
*   **占位符:**
    *   纯色背景：中灰色 `#CCCCCC` 或浅粉色 `#FFE4E1`。
    *   图标占位：在背景上居中显示一个线性的用户图标，颜色中灰色 `#666666`。
*   **实际头像:** 显示用户上传的图片，裁剪为圆形或圆角。

---
本文档为情侣信誉券小程序 MVP 阶段的 UI 设计提供了详细的视觉风格定义、关键页面描述和核心组件规范。后续可基于此文档进行高保真视觉稿设计和前端开发实现。在实际开发中，可参照微信小程序官方组件库和设计指南，结合本文档的规范进行细化和调整。