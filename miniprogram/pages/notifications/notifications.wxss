/* notifications.wxss */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffeef7 0%, #f8e8ff 100%);
  padding-top: 20rpx;
}

/* 页面头部 */
.page-header {
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 16rpx;
  min-height: 60rpx; /* 确保头部有足够高度 */
}

.header-left {
  display: flex;
  align-items: baseline;
  gap: 16rpx;
  flex: 1;
  min-width: 0; /* 防止文字溢出 */
}

.page-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #ff1493;
  white-space: nowrap;
}

.message-count {
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
  white-space: nowrap;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 6rpx 10rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
  border: none;
  font-size: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.15);
  transition: all 0.3s ease;
  min-width: auto; /* 自适应宽度 */
  height: auto; /* 自适应高度 */
  white-space: nowrap; /* 防止文字换行 */
}

.action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 105, 180, 0.2);
}

.mark-all-btn {
  color: #4caf50;
}

.clear-btn {
  color: #ff6b6b;
}

.btn-icon {
  font-size: 20rpx;
  line-height: 1;
  opacity: 0.9;
}

.btn-text {
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1;
}

.mark-all-btn .btn-icon {
  color: #4caf50;
  text-shadow: 0 0 8rpx rgba(76, 175, 80, 0.3);
}

.clear-btn .btn-icon {
  color: #ff6b6b;
  text-shadow: 0 0 8rpx rgba(255, 107, 107, 0.3);
}

.action-btn.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.action-btn.disabled .btn-icon,
.action-btn.disabled .btn-text {
  opacity: 0.6;
}

/* 二级标签样式 */
.sub-tabs-container {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  margin-top: 10rpx;
  border-radius: 20rpx;
  padding: 10rpx 0;
}

.sub-tabs-scroll {
  white-space: nowrap;
}

.sub-tabs {
  display: flex;
  padding: 0 20rpx;
  gap: 20rpx;
}

.sub-tab {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.sub-tab.active {
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  color: white;
  border-color: rgba(255, 20, 147, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(255, 20, 147, 0.3);
}

.sub-tab-label {
  font-size: 24rpx;
  font-weight: 500;
}

.sub-tab.active .sub-tab-label {
  color: white;
  font-weight: 600;
}

.sub-tab-count {
  background: rgba(255, 20, 147, 0.1);
  color: #ff1493;
  font-size: 20rpx;
  font-weight: 600;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1.2;
}

.sub-tab.active .sub-tab-count {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

/* 响应式布局优化 */
@media (max-width: 375px) {
  .header-main {
    gap: 12rpx;
  }
  
  .header-left {
    gap: 12rpx;
  }
  
  .page-title {
    font-size: 32rpx;
  }
  
  .message-count {
    font-size: 24rpx;
  }
  
  .action-btn {
    padding: 8rpx 12rpx;
    gap: 4rpx;
  }
  
  .btn-text {
    font-size: 20rpx;
  }
  
  .btn-icon {
    font-size: 22rpx;
  }
  
  /* 二级标签响应式优化 */
  .sub-tabs {
    gap: 12rpx;
    padding: 0 15rpx;
  }
  
  .sub-tab {
    padding: 6rpx 12rpx;
    gap: 6rpx;
  }
  
  .sub-tab-label {
    font-size: 22rpx;
  }
  
  .sub-tab-count {
    font-size: 18rpx;
    padding: 1rpx 6rpx;
    min-width: 28rpx;
  }
}

.action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 105, 180, 0.2);
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 22rpx;
  font-weight: 500;
}

/* 筛选标签 */
.filter-tabs-container {
  margin: 0 30rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  backdrop-filter: blur(8rpx);
  box-shadow: 0 2rpx 12rpx rgba(255, 105, 180, 0.1);
}

.filter-tabs-scroll {
  width: 100%;
  white-space: nowrap;
}

.filter-tabs {
  display: flex;
  padding: 8rpx;
}

.filter-tab {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  margin: 0 6rpx;
  border-radius: 12rpx;
  background: transparent;
  transition: all 0.3s ease;
  position: relative;
}

.filter-tab.active {
  background: linear-gradient(135deg, #ff69b4, #ff1493);
  color: white;
  transform: scale(1.05);
}

.tab-label {
  font-size: 24rpx;
  font-weight: 500;
  margin-right: 8rpx;
}

.tab-count {
  background: rgba(255, 255, 255, 0.9);
  color: #ff69b4;
  font-size: 18rpx;
  font-weight: 600;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  min-width: 20rpx;
  text-align: center;
}

.filter-tab.active .tab-count {
  background: rgba(255, 255, 255, 0.95);
  color: #ff1493;
}

/* 内容区域 */
.content-container {
  flex: 1;
  margin: 0 30rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.loading-heart {
  font-size: 80rpx;
  animation: heartbeat 1.5s ease-in-out infinite;
  margin-bottom: 20rpx;
}

.loading-dots {
  display: flex;
  gap: 12rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #ff69b4;
  animation: bounce 1.4s ease-in-out infinite both;
}

.dot2 { animation-delay: 0.16s; }
.dot3 { animation-delay: 0.32s; }

.loading-text {
  font-size: 28rpx;
  color: #999;
  opacity: 0.8;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-animation {
  position: relative;
  margin-bottom: 40rpx;
}

.empty-icon {
  font-size: 100rpx;
  position: relative;
  z-index: 2;
  animation: float 3s ease-in-out infinite;
}

.empty-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring {
  position: absolute;
  border: 2rpx solid rgba(255, 105, 180, 0.2);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.ring1 {
  width: 120rpx;
  height: 120rpx;
  margin: -60rpx 0 0 -60rpx;
}

.ring2 {
  width: 160rpx;
  height: 160rpx;
  margin: -80rpx 0 0 -80rpx;
  animation-delay: 0.5s;
}

.ring3 {
  width: 200rpx;
  height: 200rpx;
  margin: -100rpx 0 0 -100rpx;
  animation-delay: 1s;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #666;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
  line-height: 1.6;
}

/* 消息列表 */
.notifications-scroll {
  height: calc(100vh - 300rpx);
}

.notifications-list {
  padding-bottom: 40rpx;
}

.notification-item-wrapper {
  margin-bottom: 24rpx;
}

.notification-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 30rpx;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(255, 105, 180, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-card.unread {
  border-left: 6rpx solid #ff69b4;
  background: rgba(255, 240, 248, 0.95);
}

.notification-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.2);
}

.notification-main {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.notification-icon-container {
  position: relative;
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffe0f0, #ffb3d9);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 105, 180, 0.2);
}

.notification-icon {
  font-size: 36rpx;
  position: relative;
  z-index: 2;
}

.icon-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 105, 180, 0.3) 0%, transparent 70%);
  border-radius: 20rpx;
  animation: glow 2s ease-in-out infinite alternate;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
  gap: 16rpx;
}

.notification-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.notification-time {
  font-size: 22rpx;
  color: #999;
  flex-shrink: 0;
}

.notification-message {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 16rpx;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.coupon-preview {
  margin-top: 12rpx;
}

/* 修复问题6：券信息行布局 - 券名左对齐，状态标签右对齐 */
.coupon-info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.coupon-name-text {
  font-size: 22rpx;
  color: #ff69b4;
  background: rgba(255, 105, 180, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
  flex-shrink: 0;
}

/* 修复问题6：状态标签样式 */
.coupon-status-tag {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.coupon-status-text {
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

/* 修复问题6：不同状态的颜色 */
.status-pending .coupon-status-text {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.status-warning .coupon-status-text {
  color: #fa8c16;
  background: rgba(250, 140, 22, 0.1);
}

.status-success .coupon-status-text {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.status-expired .coupon-status-text {
  color: #8c8c8c;
  background: rgba(140, 140, 140, 0.1);
}

.status-cancelled .coupon-status-text {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

.status-draft .coupon-status-text {
  color: #d9d9d9;
  background: rgba(217, 217, 217, 0.1);
}

.status-unknown .coupon-status-text {
  color: #666;
  background: rgba(102, 102, 102, 0.1);
}

/* 未读标识 */
.unread-indicator {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unread-dot {
  width: 16rpx;
  height: 16rpx;
  background: #ff1493;
  border-radius: 50%;
  box-shadow: 0 0 0 4rpx rgba(255, 20, 147, 0.2);
  animation: pulse-dot 2s ease-in-out infinite;
}



/* 装饰元素 */
.notification-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.decoration-heart {
  position: absolute;
  font-size: 20rpx;
  opacity: 0.3;
  animation: float 4s ease-in-out infinite;
}

.heart1 {
  top: 20rpx;
  right: 30rpx;
  animation-delay: 0s;
}

.heart2 {
  bottom: 20rpx;
  left: 30rpx;
  animation-delay: 2s;
}

/* 加载更多 */
.load-more-container {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
}

.load-more-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 25rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.15);
  transition: all 0.3s ease;
}

.load-more-btn:active {
  transform: scale(0.95);
}

.load-more-icon {
  font-size: 24rpx;
  color: #ff69b4;
}

.load-more-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 底部加载状态 */
.bottom-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
  gap: 16rpx;
}

/* 下拉刷新 */
.refresh-indicator {
  position: fixed;
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  background: rgba(255, 255, 255, 0.95);
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 24rpx rgba(255, 105, 180, 0.2);
  z-index: 1000;
}

.refresh-animation {
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-heart {
  font-size: 40rpx;
  animation: spin 1s linear infinite;
}

.refresh-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 底部安全区域 */
.bottom-safe-area {
  height: 40rpx;
}

/* 动画定义 */
@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes bounce {
  0%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-12rpx); }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10rpx); }
}

@keyframes pulse-dot {
  0%, 100% { 
    transform: scale(1); 
    opacity: 1;
  }
  50% { 
    transform: scale(1.2); 
    opacity: 0.8;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.1; }
}

@keyframes pulse-dot {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

@keyframes glow {
  0% { opacity: 0.5; }
  100% { opacity: 0.8; }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding-bottom: 120rpx;
  }
  
  .notification-item {
    padding: 20rpx;
  }
  
  .filter-tabs {
    padding: 0 20rpx 20rpx;
  }
  
  .notification-list {
    padding: 0 20rpx;
  }
}

